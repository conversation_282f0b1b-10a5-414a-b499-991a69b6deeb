# 蔵前三筋日式豬排専門店

## [api](http://swagger.omos.tw/?urls.primaryName=Diner%20APP)

* 代碼 omos
* 帳號 um_mg
* 密碼 123456

## OKSHOP便當店

* 代碼 omodiner2
* 帳號 um
* 密碼 a

* 帳號 um2
* 密碼 a

## build

* flutter build apk --flavor production
* flutter build apk --flavor development
* python deploy.py

## [line 線上點餐](https://dev-diner.omos.tw)

* [apk](https://trello.com/b/zTgK2IV5/apk)
* [accounts](https://trello.com/b/UQBBsRGv/accounts)

## [Dependencies on unpublished packages](https://flutter.dev/docs/development/packages-and-plugins/using-packages#dependencies-on-unpublished-packages)

```yaml
dependencies:
  plugin1:
    path: ../plugin1/
```

```yaml
dependencies:
  plugin1:
    git:
      url: git://github.com/flutter/plugin1.git
```

```yaml
dependencies:
  package1:
    git:
      url: git://github.com/flutter/packages.git
      path: packages/package1
```

### [Use local dependency](https://stackoverflow.com/a/********)

```yaml
dependencies:
  flutter:
      sdk: flutter
  my_new_package:
      path: ./my_new_package
```

## [created a package using following command](https://stackoverflow.com/q/********)

```zsh
flutter create --template=package my_new_package
```

### usage

```dart
import "package:my_new_package/my_new_package.dart" // can not find the package
```

## [create plugin](https://flutter.dev/docs/development/packages-and-plugins/developing-packages#step-1-create-the-package-1)

create plugin run  `flutter create -t plugin --platforms android -a java flutter_sunmi_printer`

To add platforms, run `flutter create -t plugin --platforms <platforms> .` under flutter_sunmi_printer_4.

## 相關網址

### [OK早午餐](http://omos01.okshop.tw/home)

### [訂訂](https://www.dining.tw/)

## omos.jsk

```zsh
keytool -list -v -keystore omos.jks 
```

```txt
金鑰儲存庫類型: PKCS12
金鑰儲存庫提供者: SUN

您的金鑰儲存庫包含 1 項目

別名名稱: omos
建立日期: 2021年4月13日
項目類型: PrivateKeyEntry
憑證鏈長度: 1
憑證 [1]:
擁有者: CN=omos, OU=omos, O=omos, L=KH, ST=Unknown, C=TW
發行人: CN=omos, OU=omos, O=omos, L=KH, ST=Unknown, C=TW
序號: 2bcd4594
有效期自: Tue Apr 13 10:16:36 CST 2021 到: Thu Mar 20 10:16:36 CST 2121
憑證指紋:
         SHA1: E6:8E:0A:CF:93:6D:63:5D:5D:C8:51:A9:F2:89:BD:C3:EB:D0:A1:B7
         SHA256: 6C:BC:8B:70:48:5D:2B:FC:3E:D1:F0:13:85:7B:3D:19:47:EC:EE:BF:87:24:DE:20:52:B5:F9:08:E3:FA:2E:99
簽章演算法名稱: SHA256withRSA
主體公開金鑰演算法: 2048 位元的 RSA 金鑰
版本: 3

擴充套件: 

#1: ObjectId: 2.5.29.14 Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 09 81 F2 0A D6 32 15 BB   F4 07 F4 27 7E E2 4C 90  .....2.....'..L.
0010: 15 41 E0 75                                        .A.u
]
```

## 標籤機

### Widget

* TagSticker - 預覽用
* OrderSticker - 實際截圖

## fvm 設定路徑
  
```zsh
export PATH="$PATH":"$(pwd)/.fvm/flutter_sdk/bin"
```

## unit test

### mock

1. 宣告

```dart
@GenerateMocks([InvoiceProvider])
```

2. 產生

```zsh
flutter pub run build_runner build --delete-conflicting-outputs
```

3. 使用

sync

```dart
when(mockInvoiceProvider.getInvoiceNumber()).thenReturn(expectedInvoiceNumber);
```

async

```dart
when(mockInvoiceProvider.getInvoiceNumber()).thenAnswer((_) async => expectedInvoiceNumber);
```
