
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {
    compileSdkVersion 33

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "tw.omos.muyipork"
        minSdkVersion 21
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        prod {
            storeFile file('omos.jks')
            storePassword '83193989'
            keyAlias 'omos'
            keyPassword '83193989'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            applicationIdSuffix '.debug'
            versionNameSuffix '-DEBUG'
        }
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.prod
        }
    }
    flavorDimensions 'dim'
    productFlavors {
        development {
            dimension 'dim'
            applicationIdSuffix '.dev'
            versionNameSuffix '-DEV'
        }
        production {
            dimension 'dim'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // implementation files('libs/EZioAD.jar')
    implementation 'com.android.support:multidex:1.0.3'
}
