{"project_info": {"project_number": "44148148864", "project_id": "okshop-d2c15", "storage_bucket": "okshop-d2c15.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:44148148864:android:cc70f53b0cddcef9f9b465", "android_client_info": {"package_name": "tw.omos.muyipork"}}, "oauth_client": [{"client_id": "44148148864-h95lo9magknehr3clir63fkcefv6aj6o.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "tw.omos.muyipork", "certificate_hash": "e68e0acf936d635d5dc851a9f289bdc3ebd0a1b7"}}, {"client_id": "44148148864-b0nlu94msbjb5c6i55p10s3q402milli.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDgV__h0JK7z8x7hZxeAJkT_ghtXjZq1yg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "44148148864-b0nlu94msbjb5c6i55p10s3q402milli.apps.googleusercontent.com", "client_type": 3}, {"client_id": "44148148864-bqbq3n7qa4dk2q1n5nrj5j55bmtpm05q.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "tw.omos.muyipork.dev.debug"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:44148148864:android:6e7fa6643ff7cc0bf9b465", "android_client_info": {"package_name": "tw.omos.muyipork.debug"}}, "oauth_client": [{"client_id": "44148148864-b0nlu94msbjb5c6i55p10s3q402milli.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDgV__h0JK7z8x7hZxeAJkT_ghtXjZq1yg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "44148148864-b0nlu94msbjb5c6i55p10s3q402milli.apps.googleusercontent.com", "client_type": 3}, {"client_id": "44148148864-bqbq3n7qa4dk2q1n5nrj5j55bmtpm05q.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "tw.omos.muyipork.dev.debug"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:44148148864:android:d9085ff6d86927c4f9b465", "android_client_info": {"package_name": "tw.omos.muyipork.dev"}}, "oauth_client": [{"client_id": "44148148864-knl97h3j79c9237sa5ui957u2ju8trdu.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "tw.omos.muyipork.dev", "certificate_hash": "e68e0acf936d635d5dc851a9f289bdc3ebd0a1b7"}}, {"client_id": "44148148864-b0nlu94msbjb5c6i55p10s3q402milli.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDgV__h0JK7z8x7hZxeAJkT_ghtXjZq1yg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "44148148864-b0nlu94msbjb5c6i55p10s3q402milli.apps.googleusercontent.com", "client_type": 3}, {"client_id": "44148148864-bqbq3n7qa4dk2q1n5nrj5j55bmtpm05q.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "tw.omos.muyipork.dev.debug"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:44148148864:android:2e4b09326b041e8ef9b465", "android_client_info": {"package_name": "tw.omos.muyipork.dev.debug"}}, "oauth_client": [{"client_id": "44148148864-b0nlu94msbjb5c6i55p10s3q402milli.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDgV__h0JK7z8x7hZxeAJkT_ghtXjZq1yg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "44148148864-b0nlu94msbjb5c6i55p10s3q402milli.apps.googleusercontent.com", "client_type": 3}, {"client_id": "44148148864-bqbq3n7qa4dk2q1n5nrj5j55bmtpm05q.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "tw.omos.muyipork.dev.debug"}}]}}}], "configuration_version": "1"}