<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="tw.omos.muyipork">
    <!-- Flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <application android:usesCleartextTraffic="true">
        <!-- possibly other elements -->
    </application>
</manifest>
