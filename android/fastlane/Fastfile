# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

DROPBOX_APP_KEY = "lr37a5r60xyx6df"
DROPBOX_APP_SECRET = "xaqv5x0bw4wqo4g"
DROPBOX_REFRESH_TOKEN = "0c1pyi97IQgAAAAAAAAAAVAyD7lDSjbj_rq56-TbD74piCqxHnnN4lMQKJLhxt2i"
DROPBOX_PATH = "/apk"
OUTPUT_DIRECTORY = "../build/app/outputs/flutter-apk"
WRITE_MODE_OVERWRITE = "overwrite"
FIREBASE_TOKEN = "1//0epQSyTKEA_OtCgYIARAAGA4SNwF-L9IrMMZzU2dgA0CytblsF4SeL2RauQG7vu61sJX80V_lSbdlYdbOojph2HE42PWwyFcOUF4"

KEY_DROPBOX_ACCESS_TOKEN = "dropbox_access_token"

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    crashlytics
  
    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end

  lane :dropbox_fetch_access_token do
    uri = URI.parse("https://api.dropbox.com/oauth2/token")
    request = Net::HTTP::Post.new(uri)
    request.basic_auth(DROPBOX_APP_KEY, DROPBOX_APP_SECRET)
    request.set_form_data(
      "grant_type" => "refresh_token",
      "refresh_token" => DROPBOX_REFRESH_TOKEN, 
    )
    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == "https") do |http|
      http.request(request)
    end
    # res = Net::HTTP.post_form(
    #   URI('https://api.dropbox.com/oauth2/token'),
    #   'grant_type' => 'refresh_token',
    #   'refresh_token' => DROPBOX_REFRESH_TOKEN,
    # )
    puts response.body if response.is_a?(Net::HTTPSuccess)
    parsed_json = JSON.parse(response.body)
    puts parsed_json
    dropbox_access_token = parsed_json["access_token"]
    puts "#{KEY_DROPBOX_ACCESS_TOKEN}: #{dropbox_access_token}"
    ENV[KEY_DROPBOX_ACCESS_TOKEN] = dropbox_access_token
  end

  lane :deploy_development_to_dropbox do
    dropbox_fetch_access_token
    dropbox(
      file_path: "#{OUTPUT_DIRECTORY}/app-development-release.apk",
      dropbox_path: DROPBOX_PATH,
      write_mode: WRITE_MODE_OVERWRITE,
      app_key: DROPBOX_APP_KEY,
      app_secret: DROPBOX_APP_SECRET,
      access_token: ENV[KEY_DROPBOX_ACCESS_TOKEN],
      keychain_password: ''
    )
  end

  lane :deploy_production_to_dropbox do
    dropbox_fetch_access_token
    dropbox(
      file_path: "#{OUTPUT_DIRECTORY}/app-production-release.apk",
      dropbox_path: DROPBOX_PATH,
      write_mode: WRITE_MODE_OVERWRITE,
      app_key: DROPBOX_APP_KEY,
      app_secret: DROPBOX_APP_SECRET,
      access_token: ENV[KEY_DROPBOX_ACCESS_TOKEN],
      keychain_password: ''
    )
  end

  lane :deploy_version_to_dropbox do
    dropbox_fetch_access_token
    dropbox(
      file_path: "../assets/models/app_settings.json",
      dropbox_path: DROPBOX_PATH,
      write_mode: WRITE_MODE_OVERWRITE,
      app_key: DROPBOX_APP_KEY,
      app_secret: DROPBOX_APP_SECRET,
      access_token: ENV[KEY_DROPBOX_ACCESS_TOKEN],
      keychain_password: ""
    )
  end

  lane :deploy_to_dropbox do
    deploy_development_to_dropbox
    deploy_production_to_dropbox
    deploy_version_to_dropbox
  end

  lane :deploy_development_to_firebase do
    firebase_app_distribution(
      app: "1:44148148864:android:d9085ff6d86927c4f9b465",
      apk_path: "#{OUTPUT_DIRECTORY}/app-development-release.apk",
      firebase_cli_token: FIREBASE_TOKEN,
    )
  end

  lane :deploy_production_to_firebase do
    firebase_app_distribution(
      app: "1:44148148864:android:d9085ff6d86927c4f9b465",
      apk_path: "#{OUTPUT_DIRECTORY}/app-production-release.apk",
      firebase_cli_token: FIREBASE_TOKEN,
    )
  end

  lane :build_clean do
    sh("cd .. && flutter clean")
  end

  lane :build_development_apk do
    sh("cd .. && flutter build apk --flavor development")
  end

  lane :build_production_apk do
    sh("cd .. && flutter build apk --flavor production")
  end

  lane :build_apk do
    # build_clean
    build_development_apk
    build_production_apk
  end

  lane :build_and_deploy do
    build_apk
    deploy_to_dropbox
  end
end
