<svg id="Component_445" data-name="Component 445"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink" width="38" height="41" viewBox="0 0 38 41">
    <defs>
        <radialGradient id="radial-gradient" cx="0.5" cy="0.5" r="0.5" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-opacity="0.588"/>
            <stop offset="1" stop-opacity="0"/>
        </radialGradient>
        <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#01c82d"/>
            <stop offset="1" stop-color="#01a024"/>
        </linearGradient>
        <clipPath id="clip-path">
            <rect id="Rectangle_163" data-name="Rectangle 163" width="28.134" height="27.256" fill="#fff"/>
        </clipPath>
        <clipPath id="clip-path-2">
            <rect id="Rectangle_162" data-name="Rectangle 162" width="21.033" height="7.493" transform="translate(0 0)" fill="#01b329"/>
        </clipPath>
    </defs>
    <ellipse id="Ellipse_21" data-name="Ellipse 21" cx="17" cy="3" rx="17" ry="3" transform="translate(2 35)" fill="url(#radial-gradient)"/>
    <circle id="Ellipse_20" data-name="Ellipse 20" cx="19" cy="19" r="19" fill="url(#linear-gradient)"/>
    <g id="Group_207" data-name="Group 207" transform="translate(4.755 6.111)">
        <g id="Group_205" data-name="Group 205" clip-path="url(#clip-path)">
            <path id="Path_95" data-name="Path 95" d="M28.134,11.539a9.454,9.454,0,0,1-.244,2.141,9.838,9.838,0,0,1-.7,2.008C25.314,20.659,16.166,25.8,13.543,27.2a.483.483,0,0,1-.708-.428V24.254a1.355,1.355,0,0,0-1.161-1.346A15.893,15.893,0,0,1,6.5,21.266a13.922,13.922,0,0,1-2.479-1.653A10.544,10.544,0,0,1,0,11.539C0,5.167,6.3,0,14.067,0A15.637,15.637,0,0,1,24.216,3.55a10.516,10.516,0,0,1,3.919,7.989" transform="translate(0 -0.001)" fill="#fff"/>
        </g>
    </g>
    <g id="Group_204" data-name="Group 204" transform="translate(-1899.503 -10024.38)">
        <g id="Group_203" data-name="Group 203" transform="translate(1908.243 10037.738)">
            <g id="Group_202" data-name="Group 202" transform="translate(0 0)" clip-path="url(#clip-path-2)">
                <path id="Path_91" data-name="Path 91" d="M.809,0H.568A.568.568,0,0,0,0,.568V6.922a.568.568,0,0,0,.568.568H4.536A.568.568,0,0,0,5.1,6.922V6.709a.568.568,0,0,0-.568-.568H1.944a.568.568,0,0,1-.568-.568v-5A.568.568,0,0,0,.813,0Z" transform="translate(0 0)" fill="#01b329"/>
                <path id="Path_92" data-name="Path 92" d="M46.878.03h.231A.568.568,0,0,1,47.677.6V6.952a.568.568,0,0,1-.568.567h-.231a.568.568,0,0,1-.568-.567V.6A.568.568,0,0,1,46.878.03" transform="translate(-40.546 -0.026)" fill="#01b329"/>
                <path id="Path_93" data-name="Path 93" d="M67.546.24a.564.564,0,0,0-.439-.21h-.019A.568.568,0,0,0,66.52.6V6.952a.568.568,0,0,0,.568.568h.241a.568.568,0,0,0,.568-.568V2.831l3.658,4.48a.568.568,0,0,0,1.007-.36V.6A.568.568,0,0,0,71.994.03h-.229A.568.568,0,0,0,71.2.6V4.724Z" transform="translate(-58.241 -0.026)" fill="#01b329"/>
                <path id="Path_94" data-name="Path 94" d="M125.777,6.162V4.42h3.312a.569.569,0,0,0,.569-.568V3.658a.569.569,0,0,0-.569-.568h-3.312V1.379h3.5a.568.568,0,0,0,.568-.568V.6a.568.568,0,0,0-.568-.568h-4.3a.567.567,0,0,0-.567.568V6.952a.567.567,0,0,0,.567.567h4.414a.568.568,0,0,0,.568-.567V6.73a.568.568,0,0,0-.568-.568Z" transform="translate(-108.927 -0.026)" fill="#01b329"/>
            </g>
        </g>
    </g>
</svg>
