{"data": {"comment": "第一次來拜訪", "created_at": "2021-04-24T15:32:50", "id": 430, "invoice_info": {"address": "", "carrier_id": "", "city_id": 1, "cityarea_id": 1, "company": "", "name": "", "vat_number": ""}, "invoice_type": 1, "member_id": 60, "member_name": "林姓消費者", "member_status": 1, "order_addresses": [{"address": "222", "city": "高雄市", "city_id": 1, "cityarea": "新興區", "cityarea_id": 1, "name": "yuming", "phone": "111", "postcode": "", "type": 1}], "order_diner": {"adult": 1, "cancel_reason": "", "checkout_at": "2020-01-01T13:20:29", "child": 1, "id": 93, "is_print": 0, "meal_at": "2020-01-01T13:20:29", "memo": "", "source": 0, "table1_id": 1, "table1_name": "", "table2_id": 1, "table2_name": ""}, "order_discount": [{"discount_description": {"coupon_id": 195, "description": "消費滿500元，免費兌換簽名照一張", "discount": 50, "expiry_date": "2021-12-28T15:59:59", "id": 325, "image_url": "https://dev-image.okpos.tw/12/2bcf51d0627411ecb92e1ab13bec7451.jpg", "is_online": 0, "kind": 2, "last_count": 1, "promotion_type": 2, "status": 1, "title": "Alan簽名照兌換券"}, "discount_name": "現場折價", "discount_price": -50.0, "type": 3}, {"discount_description": {"coupon_id": 195, "description": "消費滿500元，免費兌換簽名照一張", "expiry_date": "2021-12-28T15:59:59", "id": 325, "image_url": "https://dev-image.okpos.tw/12/2bcf51d0627411ecb92e1ab13bec7451.jpg", "is_online": 0, "kind": 2, "last_count": null, "promotion_type": 2, "status": 1, "title": "Alan簽名照兌換券"}, "discount_name": "Alan簽名照兌換券", "discount_price": 0.0, "type": 0}], "order_invoice": {"carrier_id": "/8LR6-P2", "carrier_type": 3, "invoice_paper": true, "npo_ban": "987", "number": "GG12345678", "random_number": "1234", "status": 0, "vat_number": "12345678"}, "order_items": [{"final_price": 490.0, "id": 70, "is_vip": 0, "product_category_ids": [71], "product_id": 1, "product_name": "厚切腰內豬排", "product_spec_1": "[\"香草優格+100\", \"香草優格+0\"]", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 3, "type": 0, "vip_price": 400.0}, {"final_price": 390.0, "id": 71, "product_category_ids": [71], "product_id": 1, "product_name": "厚切腰內豬排", "product_spec_1": "香草優格+100, 草莓優格-100", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 15, "type": 0}, {"final_price": 700.0, "id": 346, "product_category_ids": [71], "product_id": 15, "product_name": "厚切腰內豬排", "product_spec_1": "半碗飯、不要太多肉", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 1, "type": 0}, {"final_price": 380.0, "id": 347, "product_category_ids": [71], "product_id": 16, "product_name": "西班牙松阪豬", "product_spec_1": "", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 2, "type": 0}, {"final_price": 0.0, "id": 348, "product_category_ids": [71], "product_id": 7, "product_name": "額外費用", "product_spec_1": "", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 1, "type": 0}, {"final_price": 0.0, "id": 349, "product_category_ids": [71], "product_id": 9, "product_name": "服務費", "product_spec_1": "", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 1, "type": 0}], "order_number": "L20210425000001", "order_payment": {"info": {"paid": 300.0, "change": 30.0, "other": "", "payment_fee": 200.0}, "name": "現金", "pay_method_id": 1, "payment_method_id": 1, "total": 12.0}, "order_payments": [{"info": {"change": 0, "other": "", "paid": 100.0, "payment_fee": 0.0}, "name": "ABB", "pay_method_id": 0, "payment_method_id": 385, "total": 100.0}, {"info": {"change": 0.0, "other": "", "paid": 90.0, "payment_fee": 0.0}, "name": "現金", "pay_method_id": 1, "payment_method_id": 1, "total": 100.0}, {"info": {"change": 0, "other": "", "paid": 100.0}, "name": "台灣Pay", "pay_method_id": 3, "payment_method_id": 3, "total": 100.0}], "order_shipping": {"shipping_fee": 300.0, "shipping_id": 23, "shipping_method_id": 23, "shipping_name": "常溫"}, "payment_status": 2, "redeem_member_points": -1, "refund_created_at": "", "refund_store_account_name": "", "status": 0, "subtotal": 2980.0, "total": 12.0, "type": 0, "updated_at": "2023-04-09T06:45:27"}, "sub_order": [{"comment": "第一次來拜訪", "created_at": "2021-04-24T15:32:50", "id": 430, "invoice_info": {"address": "", "carrier_id": "", "city_id": 1, "cityarea_id": 1, "company": "", "name": "", "vat_number": ""}, "invoice_type": 1, "member_id": 60, "member_name": "林姓消費者", "member_status": 1, "order_addresses": [{"address": "222", "city": "高雄市", "city_id": 1, "cityarea": "新興區", "cityarea_id": 1, "name": "yuming", "phone": "111", "postcode": "", "type": 1}], "order_diner": {"adult": 1, "cancel_reason": "", "checkout_at": "2020-01-01T13:20:29", "child": 1, "id": 93, "is_print": 0, "meal_at": "2020-01-01T13:20:29", "memo": "", "source": 0, "table1_id": 1, "table1_name": "", "table2_id": 1, "table2_name": ""}, "order_discount": [{"discount_name": "現場折價", "discount_price": -50.0, "type": 3}], "order_invoice": {"carrier_id": "/8LR6-P2", "carrier_type": 3, "invoice_paper": true, "npo_ban": "987", "number": "GG12345678", "random_number": "1234", "status": 0, "vat_number": "12345678"}, "order_items": [{"final_price": 490.0, "id": 70, "product_category_ids": [71], "product_id": 1, "product_name": "厚切腰內豬排", "product_spec_1": "[\"香草優格+100\", \"香草優格+0\"]", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 3, "type": 0, "vip_price": 400.0}, {"final_price": 390.0, "id": 71, "product_category_ids": [71], "product_id": 1, "product_name": "厚切腰內豬排", "product_spec_1": "香草優格+100, 草莓優格-100", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 15, "type": 0}, {"final_price": 700.0, "id": 346, "product_category_ids": [71], "product_id": 15, "product_name": "厚切腰內豬排", "product_spec_1": "半碗飯、不要太多肉", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 1, "type": 0}, {"final_price": 380.0, "id": 347, "product_category_ids": [71], "product_id": 16, "product_name": "西班牙松阪豬", "product_spec_1": "", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 2, "type": 0}, {"final_price": 0.0, "id": 348, "product_category_ids": [71], "product_id": 7, "product_name": "額外費用", "product_spec_1": "", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 1, "type": 0}, {"final_price": 0.0, "id": 349, "product_category_ids": [71], "product_id": 9, "product_name": "服務費", "product_spec_1": "", "product_spec_1_ids": [1, 2], "product_tax_type": 1, "quantity": 1, "type": 0}], "order_number": "L20210425000001", "order_payment": {"info": {"paid": 300.0, "change": 30.0, "payment_fee": 200.0}, "name": "現金", "pay_method_id": 1, "payment_method_id": 1, "total": 12.0}, "order_payments": [{"info": {"change": 0, "other": "", "paid": 100.0, "payment_fee": 0.0}, "name": "ABB", "pay_method_id": 0, "payment_method_id": 385, "total": 100.0}, {"info": {"change": 0.0, "other": "", "paid": 90.0, "payment_fee": 0.0}, "name": "現金", "pay_method_id": 1, "payment_method_id": 1, "total": 100.0}, {"info": {"change": 0, "other": "", "paid": 100.0}, "name": "台灣Pay", "pay_method_id": 3, "payment_method_id": 3, "total": 100.0}], "order_shipping": {"shipping_fee": 300.0, "shipping_id": 23, "shipping_method_id": 23, "shipping_name": "常溫"}, "payment_status": 2, "redeem_member_points": -1, "refund_created_at": "", "refund_store_account_name": "", "status": 0, "subtotal": 2980.0, "total": 12.0, "type": 0}]}