{"data": {"app": {"extra_charges": 0, "invoice_amount": 5035, "invoice_number_end": "RQ00000414", "invoice_number_start": "RQ00000380", "invoice_quantity": 12, "invoice_void_amount": 308, "invoice_void_number": ["**********"], "invoice_void_quantity": 1, "on_site_discount": 0, "order_amount": 4950, "order_number_end": "L20211021000051", "order_number_start": "L20211020000019", "order_quantity": 12, "order_tax": 85, "order_total": 5035, "payment": [{"channel_pay_method_name": "現金", "expenses": 0, "income": 286}, {"channel_pay_method_name": "信用卡", "expenses": 0, "income": 3529}, {"channel_pay_method_name": "振興五倍券", "expenses": 0, "income": 314}], "print_last_date": "", "print_store_account_name": "", "refund_amount": 308, "refund_orders": [{"invoice_number": "**********", "member_name": "", "order_number": "L20211021000010", "order_total": 308, "payment_name": "振興五倍券", "refund_created_at": "", "refund_store_account_name": ""}], "refund_quantity": 1, "type": 0, "updated_at": "2021-11-02T10:54:17"}, "online": {"extra_charges": 0, "invoice_amount": 286, "invoice_number_end": "**********", "invoice_number_start": "**********", "invoice_quantity": 1, "invoice_void_amount": 0, "invoice_void_number": ["**********"], "invoice_void_quantity": 0, "on_site_discount": 0, "order_amount": 273, "order_number_end": "dL20211021000001", "order_number_start": "dL20211021000001", "order_quantity": 1, "order_tax": 13, "order_total": 286, "payment": [{"channel_pay_method_name": "Happy Cash", "expenses": 0, "income": 90}, {"channel_pay_method_name": "Foodpanda", "expenses": 0, "income": 90}], "print_last_date": "", "print_store_account_name": "", "refund_amount": 0, "refund_orders": [{"invoice_number": "", "order_number": "L20210909000021", "order_total": 120, "payment_name": "現金", "refund_created_at": "", "refund_store_account_name": ""}], "refund_quantity": 0, "type": 1, "updated_at": "2021-11-02T10:54:17"}}}