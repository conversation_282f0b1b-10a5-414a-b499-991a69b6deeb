{"data": [{"id": 1, "is_custom": false, "name": "現金", "pay_method_id": 1, "sort": 0, "status": 1}, {"id": 2, "is_custom": false, "name": "信用卡", "pay_method_id": 2, "sort": 1, "status": 0}, {"id": 3, "is_custom": false, "name": "台灣Pay", "pay_method_id": 3, "sort": 1, "status": 1}, {"id": 385, "is_custom": true, "name": "ABB", "pay_method_id": 0, "sort": 2, "status": 1}, {"id": 10, "is_custom": false, "name": "Line Pay", "pay_method_id": 10, "sort": 3, "status": 1}, {"id": 11, "is_custom": false, "name": "悠遊卡 Easy Card", "pay_method_id": 11, "sort": 4, "status": 1}, {"id": 13, "is_custom": false, "name": "iPass 一卡通", "pay_method_id": 13, "sort": 5, "status": 1}, {"id": 6, "is_custom": false, "name": "iCash", "pay_method_id": 6, "sort": 6, "status": 1}, {"id": 14, "is_custom": false, "name": "Foodpanda", "pay_method_id": 14, "sort": 6, "status": 0}, {"id": 15, "is_custom": false, "name": "歐付寶", "pay_method_id": 15, "sort": 7, "status": 1}, {"id": 16, "is_custom": false, "name": "UberEats", "pay_method_id": 16, "sort": 7, "status": 0}, {"id": 364, "is_custom": false, "name": "振興五倍券", "pay_method_id": 21, "sort": 8, "status": 0}, {"id": 12, "is_custom": false, "name": "街口支付", "pay_method_id": 12, "sort": 10, "status": 0}, {"id": 356, "is_custom": false, "name": "轉帳匯款", "pay_method_id": 20, "sort": 10, "status": 0}, {"id": 5, "is_custom": false, "name": "Line Pay Money", "pay_method_id": 5, "sort": 11, "status": 0}, {"id": 8, "is_custom": false, "name": "Happy Cash", "pay_method_id": 8, "sort": 13, "status": 0}, {"id": 4, "is_custom": false, "name": "支付寶", "pay_method_id": 4, "sort": 14, "status": 0}, {"id": 7, "is_custom": false, "name": "微信支付", "pay_method_id": 7, "sort": 15, "status": 0}]}