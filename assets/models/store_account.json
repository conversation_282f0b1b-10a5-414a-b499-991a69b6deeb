{"data": [{"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-04-24T10:09:26", "id": 49, "last_login": null, "name": "a", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-04-24T10:09:26", "username": ""}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "具備駕照可協助幫忙外送，有訂單可以先告知", "created_at": "2020-01-01T13:20:29", "id": 33, "last_login": null, "name": "林姓員工", "role": {"id": 2, "name": "店員"}, "status": 1, "updated_at": "2021-05-06T03:10:49", "username": "a0003"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "負責配菜", "created_at": "2020-01-01T13:20:29", "id": 38, "last_login": null, "name": "林姓員工", "role": {"id": 2, "name": "店員"}, "status": 1, "updated_at": "2021-05-06T03:10:49", "username": "a0004"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "負責飲料區", "created_at": "2020-01-01T13:20:29", "id": 40, "last_login": null, "name": "林姓員工", "role": {"id": 2, "name": "店員"}, "status": 1, "updated_at": "2021-05-06T03:10:49", "username": "a0005"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "備註字數測試：塞納河畔 左岸的咖啡\n我手一杯 品嚐你的美\n留下唇印的嘴\n花店玫瑰 名字寫錯誰\n告白氣球 風吹到對街\n微笑在天上飛\n你說你有點難追\n想讓我知難而退\n禮物不需挑最貴\n只要香榭的落葉\n營造浪漫的約會\n不害怕搞砸一切\n擁有你就擁有 全世界\n親愛的 愛上你 ", "created_at": "2021-04-24T10:09:26", "id": 41, "last_login": "2021-05-31T07:10:02", "name": "周杰倫", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-05-31T07:10:02", "username": "a0006"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-04-24T10:09:26", "id": 42, "last_login": null, "name": "a0007", "role": {"id": 2, "name": "店員"}, "status": 1, "updated_at": "2021-04-24T10:09:26", "username": "a0007"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-04-24T10:09:26", "id": 44, "last_login": "2021-05-31T07:46:26", "name": "a0008", "role": {"id": 2, "name": "店員"}, "status": 1, "updated_at": "2021-05-31T07:46:26", "username": "a0008"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-04-24T10:09:26", "id": 46, "last_login": "2021-06-06T03:37:48", "name": "a0009", "role": {"id": 2, "name": "店員"}, "status": 1, "updated_at": "2021-06-06T03:37:48", "username": "a0009"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "a0010", "created_at": "2021-04-24T10:09:26", "id": 47, "last_login": "2021-07-08T12:20:09", "name": "a0010", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-07-08T12:20:09", "username": "a0010"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "aa", "created_at": "2021-04-24T10:09:26", "id": 48, "last_login": "2021-06-21T03:09:00", "name": "a0011", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-06-21T03:09:00", "username": "a0011"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "愛炸豬排", "created_at": "2021-04-26T06:21:32", "id": 50, "last_login": "2021-05-28T07:47:01", "name": "wei", "role": {"id": 2, "name": "店員"}, "status": 1, "updated_at": "2021-05-28T07:47:01", "username": "a1234"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "不準時上班", "created_at": "2021-03-11T10:30:04", "id": 9, "last_login": "2021-07-03T12:04:13", "name": "al", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-07-03T12:04:13", "username": "al"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "aaaa", "created_at": "2021-04-19T10:09:30", "id": 17, "last_login": "2021-07-01T05:05:01", "name": "alan", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-07-01T05:05:01", "username": "alan"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-10-04T03:00:00", "id": 136, "last_login": "2021-10-07T01:53:37", "name": "carrie", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-10-07T01:53:37", "username": "carrie"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-02-19T06:10:11", "id": 86, "last_login": "2021-10-06T08:34:53", "name": "dio", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-10-06T08:34:53", "username": "dio"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-02-19T06:10:11", "id": 88, "last_login": "2021-09-28T03:29:35", "name": "louis", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-09-28T03:29:35", "username": "louis"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-03-02T08:54:46", "id": 3, "last_login": "2021-05-03T03:29:21", "name": "lube", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-05-03T03:26:08", "username": "lube"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-04-12T02:30:06", "id": 14, "last_login": "2021-08-11T13:21:12", "name": "neo", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-08-11T13:21:12", "username": "neo"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "通常會提早上班", "created_at": "2020-01-01T13:20:29", "id": 20, "last_login": "2021-05-12T01:28:24", "name": "owen", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-05-12T01:28:24", "username": "owen"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "不準時上班", "created_at": "2021-02-19T06:10:11", "id": 1, "last_login": "2021-08-04T17:08:10", "name": "shrak", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-08-04T17:08:10", "username": "shrak"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-02-19T06:10:11", "id": 12, "last_login": "2021-10-17T00:37:29", "name": "um", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-10-17T00:37:29", "username": "um"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-06-05T15:26:17", "id": 61, "last_login": "2021-10-17T00:38:12", "name": "um2", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-10-17T00:38:12", "username": "um2"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": null, "created_at": "2021-02-19T06:10:11", "id": 11, "last_login": "2021-10-04T07:51:53", "name": "um_mg", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-10-04T07:51:53", "username": "um_mg"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "字數測試，塞納河畔 左岸的咖啡我手一杯 品嚐你的美留下唇印的嘴花店玫瑰 名字寫錯誰告白氣球 風吹到對街微笑在天上飛你說你有點難追想讓我知難而退禮物不需挑最貴只要香榭的落葉營造浪漫的約會不害怕搞砸一切擁有你就擁有 全世界親愛的 愛上你 從那天起", "created_at": "2021-03-11T10:07:41", "id": 4, "last_login": "2021-10-04T09:01:49", "name": "蘇姓員工", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-10-04T09:01:49", "username": "wei"}, {"brand_id": 1, "channel_id": 1, "client_id": 1, "comment": "負責幫忙點餐", "created_at": "2020-01-01T13:20:29", "id": 19, "last_login": "2021-09-06T01:40:29", "name": "zoe", "role": {"id": 1, "name": "店長"}, "status": 1, "updated_at": "2021-09-06T01:40:29", "username": "zoe"}]}