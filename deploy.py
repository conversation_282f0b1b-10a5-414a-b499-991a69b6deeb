from deploy_base import execute
from deploy_ios import main as deploy_ios
import os


COMMAND_CLEAN = "flutter clean"
COMMAND_ANDROID = "bundle exec fastlane build_and_deploy"


def main():
    # clean
    ret = execute(COMMAND_CLEAN)
    if ret != 0:
        return ret
    os.chdir("./android")
    print(os.getcwd())
    ret = execute(COMMAND_ANDROID)
    if ret != 0:
        return ret
    os.chdir("..")
    print(os.getcwd())
    deploy_ios()


if __name__ == "__main__":
    main()
