import subprocess
import time

IOS_ACCOUNT = "<EMAIL>"
IOS_PASSWORD = "dejd-yyue-sidu-artm"
IPA_PATH = "build/ios/ipa/Payload.ipa"
CMD_CLEAN = "flutter clean"
CMD_BUILD_SANDBOX = (
    "flutter build ipa --flavor=development --export-options-plist=$PWD/ios/sandbox.plist"
)
CMD_BUILD_PRODUCTION = "flutter build ipa --flavor=production --export-options-plist=$PWD/ios/production.plist"
CMD_VALID = f"xcrun altool --validate-app -t ios -f {IPA_PATH} -u {IOS_ACCOUNT} -p {IOS_PASSWORD}"
CMD_VALID_SANDBOX = CMD_VALID
CMD_VALID_PRODUCTION = CMD_VALID
CMD_UPLOAD = (
    f"xcrun altool --upload-app -t ios -f {IPA_PATH} -u {IOS_ACCOUNT} -p {IOS_PASSWORD}"
)
CMD_UPLOAD_SANDBOX = CMD_UPLOAD
CMD_UPLOAD_PRODUCTION = CMD_UPLOAD


def execute(command: str):
    print("======= start execute =======")
    start = time.time()

    command_run = subprocess.Popen(command, shell=True)
    command_run.wait()
    end = time.time()
    result_code = command_run.returncode
    if result_code != 0:
        print("======= execute failed, take: %.2f sec =======" % (end - start))
    else:
        print("======= execute success, take: %.2f sec =======" % (end - start))
    return result_code
