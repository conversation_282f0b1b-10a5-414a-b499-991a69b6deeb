# 宣告

@dev = dev-api-diner-app.omos.tw
@prod = api-diner-app.omos.tw
# @host = {{dev}}
@baseUrl = https://{{host}}
@token = {{token_0}}
@tokenr = {{tokenr_0}}
@fcm_key = AAAACkdvSoA:APA91bFzRkoE37RMwKa-iDwCVBWFqlwy2FKJtn_Ed9SacssyCFVAa_mud9Lyr5qrSJS6JwSr-niW506Bpq_4a-iWu3kDk352DS2LGHZMCslM3BYoQf0QpU6V71eRABYBQdtXBrhRykOL
@fcm_token = {{fcm_token3}}
@fcm_token1 = ccNmHfUVSMi7XydaN_6SmP:APA91bGbeL6T5M0Pk_URJjJxCF4YtJ7CNitABrHIghR0pgCuhF7yzUPHOP9uhNM-CAYrU1guT6E08a7pZq9r4qwRLp5O7ApnxI8DEfC0zVFBkO5J2j_LR9LTXMfs96HZCekehWf41lVD
@fcm_token2 = cMT0AMdBn0R5o5koqTApm3:APA91bEIjMJieaqWj2u-VbR8OIWb4092PVyR3Vk6DeqURtPmM_0Fhb5WTqtWqBBrNlR6gA-GbyPvlJdYskppeQkoRP03qXvlJwkj6QaCIWQUsoGywvGbrMvE3AXrbrLNu-O4NinqktXv
@fcm_token3 = c1b3jMv1Tv-Tz4v4FsoH-Z:APA91bEQgItuJ7T1FXteOYjvS-wSSHsj_ywf58Z7PDgoDuq0mXrhtNm5b6VQVgi_fFHRdVyO9euWhcmSjHD3JykDoCCYdQ9QX-gb-s9169oRH4zISI0Olt3_jbQWKnUQihF4AEesVIVC
@date = {{date_2}}
@date_1 = 2021-9-9
@date_2 = 2021-10-21
@member_id = {{member_id_1}}
@member_id_1 = 63
@token_0 = {{login.response.body.token}}
@tokenr_0 = {{renew.response.body.token}}
@token_1 = eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoidW01IiwiY2xpZW50X2lkIjoxLCJpZCI6MTQ0LCJ1c2VybmFtZSI6InVtNSIsInRpbWV6b25lIjoiQXNpYS9UYWlwZWkiLCJicmFuZF9pZCI6MSwiY2hhbm5lbF9pZCI6MSwicm9sZSI6eyJpZCI6MiwibmFtZSI6Ilx1NWU5N1x1NTRlMSJ9LCJsYXN0X2xvZ2luIjoiMjAyMS0xMS0xNFQwNTo1NDowNyIsInN5c190eXBlIjoiY2MiLCJpYXQiOjE2MzY5NDY5NzAsImV4cCI6MTYzNjk4Mjk3MH0.9MEmTXrEa1W17LjNCAQ1hih7rRDZI8HPmwE8hpQB3eE
@coupon_id = {{coupon_id_1}}
@coupon_id_1 = 1
# @product_id = {{product_id_1}}
@product_id_1 = 491
@coupon_id = {{coupon_id_1}}
@coupon_id_1 = 1
@coupon_like_id = {{coupon_like_id_1}}
@coupon_like_id_1 = 1
# @code = {{code_omos01}}
@code_omos01 = omos01
@code_red_coffee = 0925180653
@code_pig = 90685329
@code_wox = woxing
# @username = {{username_um}}
# @password = {{password_um}}
@username_um = um2
@password_um = a
@username_admin = okshop83193989
@password_admin = A12345
@username_alan = alan
@password_alan = 1234qwer

###############################################################################
### 店長、店員登入
###############################################################################
# @name login

POST {{baseUrl}}/login  HTTP/1.1
Content-Type: application/x-www-form-urlencoded

code={{code}}
&username={{username}}
&password={{password}}

###############################################################################
### token 更新
###############################################################################
# @name renew

GET {{baseUrl}}/renew HTTP/1.1
Authorization: Bearer {{token}}

###############################################################################
### 店長、店員登出
###############################################################################

GET {{baseUrl}}/logout HTTP/1.1
Authorization: Bearer {{tokenr}}

###############################################################################
### 變更密碼
###############################################################################

POST {{baseUrl}}/profile/password-reset HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

old_password=123456
&new_password=123456
&check_password=123456

###############################################################################
### 店員、店長帳號列表
###############################################################################

GET {{baseUrl}}/store-accounts HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 新增帳號
###############################################################################

POST {{baseUrl}}/store-accounts HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

role_id=2
&username=a0005
&name=%E6%9E%97%E5%A7%93%E5%93%A1%E5%B7%A5
&password=%EF%BC%88%E5%8A%A0%E5%AF%86%EF%BC%8C%E7%84%A1%E6%B3%95%E9%A1%AF%E7%A4%BA%EF%BC%89
&status=1
&comment=%E4%B8%8D%E6%BA%96%E6%99%82%E4%B8%8A%E7%8F%AD
&created_at=2020-01-01%2013%3A20%3A29
&updated_at=2020-01-07%2014%3A30%3A21

###############################################################################
### 更新帳號
###############################################################################
# @prompt account_id 操作員 id
# @prompt status 0: 關閉, 1: 啟用

PUT {{baseUrl}}/store-accounts/{{account_id}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

name=林姓員工
&password=a
&status={{status}}
&comment=不準時上班

###############################################################################
### 取得單一帳號資料
###############################################################################
# @prompt account_id 操作員 id

GET {{baseUrl}}/store-accounts/{{account_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 桌號樹狀清單
###############################################################################

GET {{baseUrl}}/tables HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得單一品牌資料
###############################################################################

GET {{baseUrl}}/brands/info HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 訂單總數
###############################################################################

GET {{baseUrl}}/orders/total HTTP/1.1
Authorization: Bearer {{tokenr}}

###############################################################################
### 訂單列表 0
###############################################################################

GET {{baseUrl}}/orders
?with_sub_order=1
&sort_type=updated_at
&sort=desc
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 訂單列表 1
###############################################################################
# @prompt type 0: 餐飲內用, 1: 外帶/自取, 2: 餐飲外送, 3: 零售自取, 4: 零售宅配, 5: 零售超商, 6: 現場點餐
# @prompt status 0: 處理中, 1: 已確認, 2: 訂單完成, 3: 訂單取消 (店家), 4: 訂單異常, 5: 訂單退貨、退款, 6: 訂單取消 (消費者)

GET {{baseUrl}}/orders
?page=1
&status={{status}}
&type={{type}}
&limit=50 HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 訂單列表 2
###############################################################################
# @prompt type 0: 餐飲內用, 1: 外帶/自取, 2: 餐飲外送, 3: 零售自取, 4: 零售宅配, 5: 零售超商, 6: 現場點餐

GET {{baseUrl}}/orders
?page=1
&status=[0,1,2,3,4,5,6]
&type=[0,1,2,3,4,5,6]
&limit=50 HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得單一消費訂單資料
###############################################################################
# @prompt order_id 訂單 id

GET {{baseUrl}}/orders/{{order_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 訂單狀態變更（棄單、接單、取消、退款）
###############################################################################

PUT {{baseUrl}}/orders/386/status
?is_push_msg=1 HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

status=0
&cancel_reason=""

content

###############################################################################
### 新增訂單
###############################################################################

POST {{baseUrl}}/orders HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

#&items=[{"product_id":32,"product_name":"厚切腰內豬排","product_spec_1":"半碗飯、不要太多肉","final_price":800,"quantity":1},{"product_id":31,"product_name":"西班牙松阪豬","product_spec_1":"","final_price":380,"quantity":2}]

member_id=2
&items=[{"product_id":424,"product_name":"厚切腰內豬排","product_spec_1":"半碗飯、不要太多肉","final_price":800,"quantity":1}]
&carrier_type=3
&discount=-50
&carrier_id=%2F8LR6-P2
&subtotal=2980
&total=12
&random_number=1234
&fee=5
&type=4
&additional_charges=50
&payment_status=2
&status=0
&source=0
&paid=300
&invoice=true
&meal_at=2020-01-01%2013%3A20%3A29
&change=30
&table2_id=1
&table1_id=1
&adult=1
&invoice_paper=true
&invoice_number=GG12345678
&child=1
&npo_ban=987
&vat_number=123456789
&receiver_name=ggg
&receiver_phone=0900102345
&receiver_city_id=1
&receiver_cityarea_id=1
&receiver_address=bbbb
&buyer_name=zzz
&buyer_phone=123454321
&buyer_city_id=1
&buyer_cityarea_id=2
&buyer_address=aaaa
&comment=qqqqqqqq

###############################################################################
### 產品列表
###############################################################################
# @prompt kind 0: offline, 1: online, 2: retail

GET {{baseUrl}}/products
?kind={{kind}}
&limit=500 HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得單一產品資料 - 點餐介面用
###############################################################################
# @prompt product_id 產品 id: 558

GET {{baseUrl}}/products/{{product_id}}/menu HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得單一產品資料
###############################################################################
# @prompt product_id 產品 id

GET {{baseUrl}}/products/{{product_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新產品
###############################################################################
# @prompt product_id 產品 id

PUT {{baseUrl}}/products/{{product_id}} HTTP/1.1
Accept: application/json
Content-Type:application/x-www-form-urlencoded
Authorization: Bearer {{tokenr}}

title=藍色上衣
&price=60
&stock=9999
&categories=172
&addition_categories=[]

###############################################################################
### 刪除產品
###############################################################################
# @prompt product_id 產品 id

DELETE {{baseUrl}}/products/{{product_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 新增產品
###############################################################################
# @prompt kind 0: offline, 1: online, 2: retail

POST {{baseUrl}}/products HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

kind={{kind}}
&title=%E5%8E%9A%E5%88%87%E8%85%B0%E5%85%A7%E8%B1%AC%E6%8E%92
&summary=%E8%B1%AC%E8%82%89%E7%82%BA%E5%8F%B0%E7%81%A3%E6%9C%AC%E5%9C%B0%EF%BC%8C%E8%82%89%E8%B3%AA%E6%96%B0%E9%AE%AE
&price=390
&stock=10
&categories=61,62,63
&addition_categories=%5B%7B%22addition_category_id%22%3A1%2C%22title%22%3A%22%E9%A3%B2%E6%96%99%22%2C%22required%22%3A0%2C%22option%22%3A0%7D%2C%7B%22addition_category_id%22%3A2%2C%22title%22%3A%22%E7%94%9C%E9%A3%9F%22%2C%22required%22%3A1%2C%22option%22%3A1%2C%22option_min%22%3A1%2C%22option_maz%22%3A2%7D%5D

###############################################################################
### 分類列表
###############################################################################
# @prompt kind 0: offline, 1: online, 2: retail

GET {{baseUrl}}/categories
?kind={{kind}}
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 新增分類
###############################################################################

POST {{baseUrl}}/categories HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

kind=0
&name=A
&sort=1

###############################################################################
### 更新分類
###############################################################################
# @prompt category_id 分類 id
# @prompt name 分類名稱

PUT {{baseUrl}}/categories/{{category_id}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

name={{name}}
&sort=1

###############################################################################
### 分類附加品列表
###############################################################################
# @prompt kind 0: offline, 1: online, 2: retail

GET {{baseUrl}}/addition-categories
?kind={{kind}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 新增附加品分類
###############################################################################

POST {{baseUrl}}/addition-categories HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

kind=0
&id=1
&name=A
&sort=12

###############################################################################
### 附加品列表
###############################################################################
# @prompt kind 0: offline, 1: online, 2: retail

GET {{baseUrl}}/addition-products
?kind={{kind}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得餐廳設定資料
###############################################################################

GET {{baseUrl}}/setting HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新餐廳資料
###############################################################################

PUT {{baseUrl}}/setting HTTP/1.1
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{tokenr}}

hours={"week":[{"day":1,"status":1,"hours":[{"open":"00:00","close":"24:00"}]},{"day":2,"status":1,"hours":[{"open":"00:00","close":"24:00"}]},{"day":3,"status":1,"hours":[{"open":"00:00","close":"23:30"}]},{"day":4,"status":1,"hours":[{"open":"00:00","close":"24:00"}]},{"day":5,"status":1,"hours":[{"open":"00:00","close":"24:00"}]},{"day":6,"status":1,"hours":[{"open":"00:00","close":"24:00"}]},{"day":7,"status":1,"hours":[{"open":"00:00","close":"23:30"}]}],"comment":""}
&other={"line_order":{"dine_in":1,"to_go":1,"delivery":0,"limit":0,"preparation_min":0},"auto":{"order":0,"order_print":0,"abandon":0,"abandon_min":0,"close":0,"close_min":0},"fee":{"type":0,"percent":0},"print_order":1,"print_detail":1,"checkout_type":1}

###############################################################################
### 傳送訊息給單一會員
###############################################################################
# @prompt member_id 會員 id

PUT {{baseUrl}}/members/{{member_id}}/push_message HTTP/1.1
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{tokenr}}

message=1234

###############################################################################
### 品牌會員資料
###############################################################################

GET {{baseUrl}}/members
?page=1
&limit=50 HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 城市列表
###############################################################################

GET {{baseUrl}}/cities HTTP/1.1

###############################################################################
### 鄉鎮列表
###############################################################################

GET {{baseUrl}}/cityareas HTTP/1.1

###############################################################################
### 取得零售自取設定
###############################################################################

GET {{baseUrl}}/setting/shipping/instore HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得零售宅配設定
###############################################################################

GET {{baseUrl}}/setting/shipping/delivery HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 由 id 取得 shipping 方式設定 (O)
###############################################################################

GET {{baseUrl}}/setting/shipping/1 HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 由 id 取得付款方式設定 (O)
###############################################################################

GET {{baseUrl}}/setting/payment/18 HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 由 id 更新付款方式設定 (O)
###############################################################################

PUT {{baseUrl}}/setting/payment/18 HTTP/1.1
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{tokenr}}

status=1
&status_diner=1
&description_diner=456

###############################################################################
### 取得零售金流設定 (O)
###############################################################################

GET {{baseUrl}}/setting/payment HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新零售金流設定 (12)
###############################################################################

PUT {{baseUrl}}/setting/payment HTTP/1.1
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{tokenr}}

bank_account_name=歐魔斯有限公司
&bank_branch=新生分行
&cod_payment_fee=1
&bank_account=1234-5678-999
&cod_payment_free=1000
&bank_name=國泰世華
&instore_status=1
&bank_status=1
&cod_description=1
&cod_status=1
&bank_description=1
&instore_description=1

###############################################################################
### 取得零售金流設定 - 店內付款 (O)
###############################################################################

GET {{baseUrl}}/setting/payment/instore HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新零售金流設定 - 店內付款 (2)
###############################################################################

PUT {{baseUrl}}/setting/payment/instore HTTP/1.1
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{tokenr}}

status=1
&description=123

###############################################################################
### 取得零售金流設定 - 轉帳匯款 (O)
###############################################################################

GET {{baseUrl}}/setting/payment/bank HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新零售金流設定 - 轉帳匯款 (6)
###############################################################################

PUT {{baseUrl}}/setting/payment/bank HTTP/1.1
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{tokenr}}

status=1
&name=國泰世華
&branch=新生分行
&account_name=歐魔斯有限公司
&account=1234-5678-999
&description=1

###############################################################################
### 取得零售金流設定 - 貨到付款 (O)
###############################################################################

GET {{baseUrl}}/setting/payment/cod HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新零售金流設定 - 貨到付款 (4)
###############################################################################

PUT {{baseUrl}}/setting/payment/cod HTTP/1.1
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{tokenr}}

fee=1
&free=1000
&status=1
&description=1

###############################################################################
### 取得餐廳＆零售設定 (O)
###############################################################################

GET {{baseUrl}}/setting/all HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 圖片庫列表 (O)
###############################################################################

GET {{baseUrl}}/images HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得單一圖片
###############################################################################
# @prompt id 50

GET {{baseUrl}}/images/{{id}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 圖片上傳(base64)
###############################################################################

POST {{baseUrl}}/images HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

content

###############################################################################
### 圖片上傳(binary)
###############################################################################

POST {{baseUrl}}/images/files HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

content

###############################################################################
### 取得單一圖片資料(O)
###############################################################################

GET {{baseUrl}}/images/2 HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 刪除單一圖片資料
###############################################################################

DELETE {{baseUrl}}/images/2 HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 表單資料範例
###############################################################################

POST https://api.example.com/user/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="text"

title
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="image"; filename="1.png"
Content-Type: image/png

< ./1.png
------WebKitFormBoundary7MA4YWxkTrZu0gW--

###############################################################################
### 標籤機列表
###############################################################################

GET {{baseUrl}}/setting/labels HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 批次新增標籤機(清除原先所有設定）
###############################################################################

POST {{baseUrl}}/setting/labels/batch_with_truncate HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

labels=[{"name": "abc","print_count":0,"status":0,"category_ids":[],"ip_address":"asd"}]

###############################################################################
### 取得單一店家電子發票資料 (O)
###############################################################################

GET {{baseUrl}}/brands/invoice HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新單一店家電子發票資料 (O)
###############################################################################

PUT {{baseUrl}}/brands/invoice HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

tax_type=1
&gui_exception=1
&status=1

###############################################################################
### 品牌最新消息 (O)
###############################################################################

GET {{baseUrl}}/brands/news HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新品牌最新消息資料 (O)
###############################################################################

PUT {{baseUrl}}/brands/news HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

news="123"

###############################################################################
### 品牌圖片輪播列表 (O)
###############################################################################

GET {{baseUrl}}/brands/banners HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新品牌圖片輪播資料 (O)
###############################################################################

PUT {{baseUrl}}/brands/banners HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

images=[{"image_id":309,"sort":1}]

###############################################################################
### 取得單一店家基本資料 (O)
###############################################################################

GET {{baseUrl}}/brands/basic HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新單一店家基本資料 (O)
###############################################################################

PUT {{baseUrl}}/brands/basic HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

city_id=1
&cityarea_id=1
&address=民權東西1號
&phone=(02)2222-5252
&website=https://www.omos.tw
&facebook=https://www.facebook.com
&instagram=https://www.instagram.com
&youtube=https://www.youtube.com
&email=<EMAIL>
&custom_url=[{"name": "自定義一","url": "https://abc.com.tw"}, {"name": "自定義二","url": "https://abc.com.tw"}, {"name": "","url": ""}]

###############################################################################
### 取得零售金流設定 - 綠界 (O)
###############################################################################

GET {{baseUrl}}/setting/payment/ecpay HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新零售金流設定 - 綠界 (O)
###############################################################################

PUT {{baseUrl}}/setting/payment/ecpay HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

merchant_id=2000214
&hash_key=5294y06JbISpM5x9
&hash_iv=v77hoKGq4kWxNNIS
&description=
&credit_status=1

###############################################################################
### 提供的付款方式列表
###############################################################################

GET {{baseUrl}}/pay-methods HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json

###############################################################################
### 更新推播 token 設定
###############################################################################

POST {{baseUrl}}/setting/firebase HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

machine_no=123
&token={{fcm_token}}

###############################################################################
### FCM
###############################################################################

POST https://fcm.googleapis.com/fcm/send HTTP/1.1
Content-Type: application/json
Authorization: key={{fcm_key}}

{
  "to": "{{fcm_token2}}",
  "notification": {
    "title": "title123",
    "body": "body456",
    "sound": "new_order.wav",
    "icon": "ic_notification"
  },
  "content_available": true,
  "data": {
    "Nick" : "Mario",
    "body" : "great match!",
    "Room" : "PortugalVSDenmark"
  }
}

###############################################################################
### FCM without notification
###############################################################################

POST https://fcm.googleapis.com/fcm/send HTTP/1.1
Content-Type: application/json
Authorization: key={{fcm_key}}

{
  "to": "{{fcm_token}}",
  "data": {
    "type": "order",
    "order_id": 13525,
    "order_number": "dL20211005000007"
  },
  "content_available": true
}

###############################################################################
### 取得APP付款設定
###############################################################################

GET {{baseUrl}}/setting/pay HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 批次更新APP付款設定（新增/更新/刪除）
###############################################################################

PUT {{baseUrl}}/setting/pay HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

# pays=[{"id":0,"is_custom":true,"name":"信用卡","sort":1,"status":1}]
pays=[{"id":1,"is_custom":false,"name":"現金","pay_method_id":1,"sort":1,"status":1},{"id":2,"is_custom":false,"name":"信用卡","pay_method_id":2,"sort":2,"status":1},{"id":3,"is_custom":false,"name":"台灣Pay","pay_method_id":3,"sort":3,"status":1},{"id":10,"is_custom":false,"name":"Line Pay","pay_method_id":10,"sort":4,"status":1},{"id":11,"is_custom":false,"name":"悠遊卡 Easy Card","pay_method_id":11,"sort":5,"status":1},{"id":13,"is_custom":false,"name":"iPass 一卡通","pay_method_id":13,"sort":6,"status":1},{"id":14,"is_custom":false,"name":"Foodpanda","pay_method_id":14,"sort":7,"status":1},{"id":16,"is_custom":false,"name":"UberEats","pay_method_id":16,"sort":8,"status":1},{"id":364,"is_custom":false,"name":"振興五倍券","pay_method_id":21,"sort":9,"status":1},{"id":6,"is_custom":false,"name":"iCash","pay_method_id":6,"sort":11,"status":1},{"id":356,"is_custom":false,"name":"轉帳匯款","pay_method_id":20,"sort":12,"status":0},{"id":12,"is_custom":false,"name":"街口支付","pay_method_id":12,"sort":13,"status":1},{"id":15,"is_custom":false,"name":"歐付寶","pay_method_id":15,"sort":15,"status":1},{"id":8,"is_custom":false,"name":"Happy Cash","pay_method_id":8,"sort":16,"status":1},{"id":4,"is_custom":false,"name":"支付寶","pay_method_id":4,"sort":17,"status":1},{"id":7,"is_custom":false,"name":"微信支付","pay_method_id":7,"sort":18,"status":1},{"id":5,"is_custom":false,"name":"Line Pay Money","pay_method_id":5,"sort":19,"status":1}]

###############################################################################
### 排序APP付款方式
###############################################################################

POST {{baseUrl}}/setting/pay/sort HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

data=[{"id":0,"is_custom":true,"name":"信用卡","sort":1,"status":1}]

###############################################################################
### 每日營收
###############################################################################
# @prompt date 2022-11-1

GET {{baseUrl}}/reports/revenue/{{date}}
?page=1
&limit=50 HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 日結單
###############################################################################

GET {{baseUrl}}/reports/statements/{{date}}?kind=0 HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 日結單-重新計算
###############################################################################
# @prompt date 2021-10-21

GET {{baseUrl}}/reports/statements/{{date}}/refresh HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 紀錄日結單列印時間
###############################################################################

POST {{baseUrl}}/reports/statements/{{date}}/print HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 每日銷售明細
###############################################################################
# @prompt date 2022-11-1
# @prompt kind 0:餐飲, 1:零售

GET {{baseUrl}}/reports/sales/{{date}}?kind={{kind}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 每日銷售明細-重新計算
###############################################################################

GET {{baseUrl}}/reports/sales/{{date}}/refresh HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 紀錄每日銷售明細列印時間
###############################################################################

POST {{baseUrl}}/reports/sales/{{date}}/print HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 品牌會員資料 (O)
###############################################################################

GET {{baseUrl}}/members HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得單一會員資料 (O)
###############################################################################

GET {{baseUrl}}/members/{{member_id}}
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得單一會員基本資料 (O)
###############################################################################

GET {{baseUrl}}/members/{{member_id}}/profile HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 封鎖單一會員資料
###############################################################################

GET {{baseUrl}}/members/{{member_id}}/block HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 解除封鎖單一會員資料
###############################################################################

GET {{baseUrl}}/members/{{member_id}}/unblock HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 將單一會員設定為 vip (O)
###############################################################################

GET {{baseUrl}}/members/{{member_id}}/vip HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 解除單一會員 vip 資格 (O)
###############################################################################

GET {{baseUrl}}/members/{{member_id}}/unvip HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 設定單一會員店家備註 (O)
###############################################################################

PUT {{baseUrl}}/members/{{member_id}}/memo HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

memo=a

###############################################################################
### 傳送訊息給單一會員 (?)
###############################################################################

PUT {{baseUrl}}/members/{member_id}/push_message HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

message=a

###############################################################################
### 取得單一會員圖片列表 (?)
###############################################################################

GET {{baseUrl}}/members/{{member_id}}/images HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新單一會員圖片集 (?)
###############################################################################

PUT {{baseUrl}}/members/{{member_id}}/images HTTP/1.1

images=[{"image_id":309,"sort":1}]

###############################################################################
### 會員積點列表 (O)
###############################################################################

GET {{baseUrl}}/member-points/{{member_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 新增/減少會員積點 (O)
###############################################################################

POST {{baseUrl}}/member-points/{{member_id}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

points=1
&type=0
&expiry_date=2022-1-1
&comment=a

###############################################################################
### 會員總積點 (O)
###############################################################################

GET {{baseUrl}}/member-points/{{member_id}}/total HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 會員到期積點 (O)
###############################################################################

POST {{baseUrl}}/member-points/{{member_id}}/expiry HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 取得點數設定資料
###############################################################################

GET {{baseUrl}}/setting/points HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 更新會員積點設定
###############################################################################

PUT {{baseUrl}}/setting/points HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

type=1
&is_reward_points=1
&cash_ratio=100

###############################################################################
### 產品最後異動時間 (X)
###############################################################################

GET {{baseUrl}}/check/product HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 會員分析 (O)
###############################################################################

GET {{baseUrl}}/member-reports/{{member_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 某會員優惠卷 (O)
###############################################################################

GET {{baseUrl}}/member-coupons/{{member_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 發放會員優惠卷 (O)
###############################################################################

POST {{baseUrl}}/member-coupons/{{member_id}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

coupon_id=[1,2]

###############################################################################
### 優惠卷列表 (O)
###############################################################################

GET {{baseUrl}}/coupons HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 優惠卷詳情 (O)
###############################################################################

GET {{baseUrl}}/coupons/{{coupon_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 標籤機列表
###############################################################################

GET {{baseUrl}}/setting/labels
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 新增標籤機
###############################################################################

POST {{baseUrl}}/setting/labels
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

ip=************
&mac_address=00:1D:9A:08:41:97
&category_ids=[1,2]
&print_count=1
&status=1
&other={}

###############################################################################
### 更新標籤機
###############################################################################

PUT {{baseUrl}}/setting/labels/180 HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

ip=************
&mac_address=00:1D:9A:08:41:97
&print_count=1
&status=1

###############################################################################
### 刪除標籤機
###############################################################################

DELETE {{baseUrl}}/setting/labels/180 HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 批次新增標籤機
###############################################################################

POST {{baseUrl}}/setting/labels/batch HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

labels=[]

###############################################################################
### 批次更新標籤機
###############################################################################

PUT {{baseUrl}}/setting/labels/batch HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

labels=[]

###############################################################################
### 批次新增標籤機(清除原先所有設定）
###############################################################################

POST {{baseUrl}}/setting/labels/batch_with_truncate HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

labels=[]

###############################################################################
###
###############################################################################

PUT {{baseUrl}}/product-categories/sort HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

kind=0
&category_id=171
&product_id=208
&sort=2

###############################################################################
### dm
###############################################################################

GET {{baseUrl}}/brands/dm
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 標籤機列表
###############################################################################

GET {{baseUrl}}/setting/printers HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 折扣列表 (O)
###############################################################################

GET {{baseUrl}}/coupons_like HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 折扣詳情 (O)
###############################################################################

GET {{baseUrl}}/coupons_like/{{coupon_like_id}} HTTP/1.1
Accept: application/json
Authorization: Bearer {{tokenr}}

###############################################################################
### 新增折扣
###############################################################################

POST {{baseUrl}}/coupons_like HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

title=測試
&promotion_type=0
&discount=125
&sort=1

###############################################################################
### 更新折扣
###############################################################################

PUT {{baseUrl}}/coupons_like/{{coupon_like_id}} HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

title=測試126
&promotion_type=0
&discount=126
&sort=2

###############################################################################
# 當日交接班報表
###############################################################################
# @prompt date 日期格式: 2022-11-15

GET {{host}}/reports/realtime_statements/{{date}}
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Authorization: Bearer {{tokenr}}

kind=0

###############################################################################
###
###############################################################################

curl -X POST "https://dev-api-diner-app.omos.tw/setting/labels/batch_with_truncate"
-H"accept:application/json"
-H"Authorization:BearereyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRfaWQiOjEsInJvbGUiOnsibmFtZSI6Ilx1NWU5N1x1OTU3NyIsImlkIjoxfSwidGltZXpvbmUiOiJBc2lhL1RhaXBlaSIsIm5hbWUiOiJ1bTIiLCJicmFuZF9pZCI6MSwibGFzdF9sb2dpbiI6IjIwMjEtMDctMzFUMDI6NDI6MjciLCJjaGFubmVsX2lkIjoxLCJpZCI6NjEsInN5c190eXBlIjoiY2MiLCJpYXQiOjE2MjgwNDU2MTEsImV4cCI6MTYyODA4MTYxMX0.X0JckQyuzuZJF55gydFF_MOKMpED1i6gjJqK9LSUDpI"
-H"Content-Type:application/x-www-form-urlencoded"
-d"labels=\"[{
  "name": "GVT-123",
  "print_count": 1,
  "status": 0
}]\""
