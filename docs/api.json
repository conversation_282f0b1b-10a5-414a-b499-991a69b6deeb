{"version": 6, "entities": [{"entity": {"type": "Project", "id": "312b95de-81d4-4609-a0b4-d5eba7f1bb05", "name": "三筋豬排"}, "children": [{"entity": {"type": "Service", "id": "d1d16ca1-bb92-49d3-9647-0ee9f66d5b6c", "name": "addition-categories 附加品分類"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/addition-categories"}, "id": "6211a1cd-8246-4561-b366-c5fe63ccb8b8", "name": "分類附加品列表", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.5", "name": "DELETE"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/addition-categories​/${addition_category_id}"}, "id": "d2f55925-63bf-4fa8-8e52-a55d8baea8be", "name": "刪除附加品分類", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "data", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/addition-categories​/sort"}, "id": "4e9baea3-0ec1-4907-9d9b-61cfdb844ce8", "name": "排序附加品分類", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "kind", "value": ""}, {"enabled": true, "type": "Text", "name": "name", "value": ""}, {"enabled": true, "type": "Text", "name": "sort", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/addition-categories"}, "id": "00e1f35e-679f-4d2f-8022-604bdd391169", "name": "新增附加品分類", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "name", "value": ""}, {"enabled": true, "type": "Text", "name": "sort", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/addition-categories​/${addition_category_id}"}, "id": "7bd4c3da-0c6c-4205-aacf-2d9320e5f064", "name": "更新附加品分類", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}, {"entity": {"type": "Service", "id": "a8432db9-e524-4f1d-8085-25e7020b215a", "name": "addition-products 附加品"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.5", "name": "DELETE"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/addition-products​/${addition_product_id}"}, "id": "cf237f29-9255-4668-9397-a8308dff36af", "name": "刪除附加品", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "data", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/addition-products​/sort"}, "id": "4f4ad999-a0be-4270-a913-92e17e73f435", "name": "排序附加品", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "kind", "value": ""}, {"enabled": true, "type": "Text", "name": "addition_category_id", "value": ""}, {"enabled": true, "type": "Text", "name": "string", "value": ""}, {"enabled": true, "type": "Text", "name": "price", "value": ""}, {"enabled": true, "type": "Text", "name": "sort", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/addition-products"}, "id": "8f3e3b60-8c34-43da-9274-e9c8ff9fb8d4", "name": "新增附加品", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "addition_category_id", "value": ""}, {"enabled": true, "type": "Text", "name": "name", "value": ""}, {"enabled": true, "type": "Text", "name": "price", "value": ""}, {"enabled": true, "type": "Text", "name": "sort", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/addition-products​/${addition_product_id}"}, "id": "0531b262-41f8-4084-9b7d-b5ed44faea6d", "name": "更新附加品", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/addition-products"}, "id": "aa0d0d1a-599c-4fdb-a108-79a0876c7aab", "name": "附加品列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}, {"entity": {"type": "Service", "id": "d247bc0f-eea6-402c-96fc-a3d3057bae76", "name": "auth 登入相關"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/renew"}, "id": "95ad5a07-77bc-40be-81b4-67515601134e", "name": "token 更新 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "code", "value": "omos"}, {"enabled": true, "type": "Text", "name": "username", "value": "um_mg"}, {"enabled": true, "type": "Text", "name": "password", "value": "123456"}, {"type": "Text", "name": "channel_code", "value": "zhōngxiào"}, {"type": "Text", "name": "client_code", "value": "omos"}, {"type": "Text", "name": "code", "value": "zhōngxiào"}]}, "bodyType": "Form", "textBody": "{\n  \"code\": \"omos\",\n  \"username\": \"um_mg\",\n  \"password\": \"123456\"\n}"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/login"}, "id": "8f7640b7-622b-410a-a26f-27c114f7bfb9", "name": "店長、店員登入 (O)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/logout"}, "id": "6e3e464a-5c2f-43fc-a141-718d18f8aaca", "name": "店長、店員登出 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}, {"entity": {"type": "Service", "id": "c507ff83-8053-49d2-ad5a-d51773339ce3", "name": "brands 品牌"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/brands/info"}, "id": "9865341e-c6c7-40ec-a403-ba8c60bd1084", "name": "取得單一品牌資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "name", "value": ""}, {"enabled": true, "type": "Text", "name": "phone", "value": ""}, {"enabled": true, "type": "Text", "name": "tax_id", "value": ""}, {"enabled": true, "type": "Text", "name": "post_code", "value": ""}, {"enabled": true, "type": "Text", "name": "city_id", "value": ""}, {"enabled": true, "type": "Text", "name": "cityarea_id", "value": ""}, {"enabled": true, "type": "Text", "name": "business_hours", "value": ""}, {"enabled": true, "type": "Text", "name": "point_ratio", "value": ""}, {"enabled": true, "type": "Text", "name": "other", "value": ""}, {"enabled": true, "type": "Text", "name": "line_name", "value": ""}, {"enabled": true, "type": "Text", "name": "line_channel_id", "value": ""}, {"enabled": true, "type": "Text", "name": "line_secret_code", "value": ""}, {"enabled": true, "type": "Text", "name": "line_access_token", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/brands"}, "id": "4637660f-315d-4c9d-93d0-ad7a476f4c90", "name": "更新品牌資料 (?)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}, {"entity": {"type": "Service", "id": "d0b95170-7f03-4759-9d58-dc0be92e271d", "name": "categories 分類"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/categories"}, "id": "6d826e8a-e46c-47b8-8b0d-50bbfb2dfe5a", "name": "分類列表 (X)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.5", "name": "DELETE"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/categories/${category_id}"}, "id": "720cc40d-17cb-4553-95b0-d0ee5a8086c7", "name": "刪除分類 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "data", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/categories/sort"}, "id": "a0664144-6ce7-465d-b30b-289e41c1ea82", "name": "排序分類", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "kind", "value": "0"}, {"enabled": true, "type": "Text", "name": "name", "value": "我的第2個分類"}, {"enabled": true, "type": "Text", "name": "sort", "value": "999"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/categories"}, "id": "3c801e48-8415-46fe-8353-fb6c3f45e48f", "name": "新增分類 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "name", "value": "123"}, {"enabled": true, "type": "Text", "name": "sort", "value": "999"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/categories/${category_id}"}, "id": "63ffde46-ce68-466c-8522-f05f47d28a8a", "name": "更新分類 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}]}, {"entity": {"type": "Service", "id": "f3e0b497-9089-4092-aeb9-bf35863a579e", "name": "members 會員"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/members/${member_id}/push_message"}, "id": "9b8e1204-b680-48e7-9ab5-3d276cd05cb2", "name": "傳送訊息給單一會員 (O)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/members/${member_id}"}, "id": "4dea755b-b2fa-48c9-adce-db247d7c3dc2", "name": "取得單一會員資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/members"}, "id": "f4851d6f-5059-4dc6-9f22-7dd4b1e4809b", "name": "品牌會員資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/members/${member_id}/block"}, "id": "725c4e87-b9b6-4c44-b111-a6ed8563f49a", "name": "封鎖單一會員資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/members/${member_id}/unblock"}, "id": "0aef0453-272f-415a-81a7-c892bfef5080", "name": "解除封鎖單一會員資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}, {"entity": {"type": "Service", "id": "a973e218-26f3-488b-a501-8468e398cc98", "name": "orders 消費紀錄"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/orders​/${order_id}"}, "id": "408c163a-2697-4fb3-a88f-bc96877b36ad", "name": "取得單一消費訂單資料 (X)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "member_id", "value": ""}, {"enabled": true, "type": "Text", "name": "table1_id", "value": ""}, {"enabled": true, "type": "Text", "name": "table2_id", "value": ""}, {"enabled": true, "type": "Text", "name": "adult", "value": ""}, {"enabled": true, "type": "Text", "name": "child", "value": ""}, {"enabled": true, "type": "Text", "name": "type", "value": ""}, {"enabled": true, "type": "Text", "name": "source", "value": ""}, {"enabled": true, "type": "Text", "name": "subtotal", "value": ""}, {"enabled": true, "type": "Text", "name": "fee", "value": ""}, {"enabled": true, "type": "Text", "name": "discount", "value": ""}, {"enabled": true, "type": "Text", "name": "additional_charges", "value": ""}, {"enabled": true, "type": "Text", "name": "total", "value": ""}, {"enabled": true, "type": "Text", "name": "paid", "value": ""}, {"enabled": true, "type": "Text", "name": "change", "value": ""}, {"enabled": true, "type": "Text", "name": "payment_status", "value": ""}, {"enabled": true, "type": "Text", "name": "status", "value": ""}, {"enabled": true, "type": "Text", "name": "invoice", "value": ""}, {"enabled": true, "type": "Text", "name": "invoice_number", "value": ""}, {"enabled": true, "type": "Text", "name": "random_number", "value": ""}, {"enabled": true, "type": "Text", "name": "invoice_paper", "value": ""}, {"enabled": true, "type": "Text", "name": "vat_number", "value": ""}, {"enabled": true, "type": "Text", "name": "carrier_type", "value": ""}, {"enabled": true, "type": "Text", "name": "carrier_id", "value": ""}, {"enabled": true, "type": "Text", "name": "npo_ban", "value": ""}, {"enabled": true, "type": "Text", "name": "order_ids", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/orders​/combining"}, "id": "c62c355b-b749-48dd-bfe4-c7b032f79e57", "name": "合併訂單", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "member_id", "value": ""}, {"enabled": true, "type": "Text", "name": "table1_id", "value": ""}, {"enabled": true, "type": "Text", "name": "table2_id", "value": ""}, {"enabled": true, "type": "Text", "name": "adult", "value": ""}, {"enabled": true, "type": "Text", "name": "child", "value": ""}, {"enabled": true, "type": "Text", "name": "type", "value": ""}, {"enabled": true, "type": "Text", "name": "source", "value": ""}, {"enabled": true, "type": "Text", "name": "subtotal", "value": ""}, {"enabled": true, "type": "Text", "name": "fee", "value": ""}, {"enabled": true, "type": "Text", "name": "discount", "value": ""}, {"enabled": true, "type": "Text", "name": "additional_charges", "value": ""}, {"enabled": true, "type": "Text", "name": "total", "value": ""}, {"enabled": true, "type": "Text", "name": "paid", "value": ""}, {"enabled": true, "type": "Text", "name": "change", "value": ""}, {"enabled": true, "type": "Text", "name": "payment_status", "value": ""}, {"enabled": true, "type": "Text", "name": "status", "value": ""}, {"enabled": true, "type": "Text", "name": "meal_at", "value": ""}, {"enabled": true, "type": "Text", "name": "memo", "value": ""}, {"enabled": true, "type": "Text", "name": "comment", "value": ""}, {"enabled": true, "type": "Text", "name": "invoice", "value": ""}, {"enabled": true, "type": "Text", "name": "invoice_number", "value": ""}, {"enabled": true, "type": "Text", "name": "random_number", "value": ""}, {"enabled": true, "type": "Text", "name": "invoice_paper", "value": ""}, {"enabled": true, "type": "Text", "name": "vat_number", "value": ""}, {"enabled": true, "type": "Text", "name": "carrier_type", "value": ""}, {"enabled": true, "type": "Text", "name": "carrier_id", "value": ""}, {"enabled": true, "type": "Text", "name": "npo_ban", "value": ""}, {"enabled": true, "type": "Text", "name": "items", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/orders"}, "id": "97364c0a-093b-4979-be3b-cd148281476a", "name": "新增訂單", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "member_id", "value": ""}, {"enabled": true, "type": "Text", "name": "table1_id", "value": ""}, {"enabled": true, "type": "Text", "name": "table2_id", "value": ""}, {"enabled": true, "type": "Text", "name": "adult", "value": ""}, {"enabled": true, "type": "Text", "name": "child", "value": ""}, {"enabled": true, "type": "Text", "name": "type", "value": ""}, {"enabled": true, "type": "Text", "name": "source", "value": ""}, {"enabled": true, "type": "Text", "name": "subtotal", "value": ""}, {"enabled": true, "type": "Text", "name": "fee", "value": ""}, {"enabled": true, "type": "Text", "name": "discount", "value": ""}, {"enabled": true, "type": "Text", "name": "additional_charges", "value": ""}, {"enabled": true, "type": "Text", "name": "total", "value": ""}, {"enabled": true, "type": "Text", "name": "paid", "value": ""}, {"enabled": true, "type": "Text", "name": "change", "value": ""}, {"enabled": true, "type": "Text", "name": "payment_status", "value": ""}, {"enabled": true, "type": "Text", "name": "status", "value": ""}, {"enabled": true, "type": "Text", "name": "memo", "value": ""}, {"enabled": true, "type": "Text", "name": "comment", "value": ""}, {"enabled": true, "type": "Text", "name": "meal_at", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/orders​/${order_id}"}, "id": "5876ba3e-7230-4032-858b-0d98eb62d01f", "name": "更新訂單", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/orders"}, "id": "a9dbe86d-fd32-4dd2-8a5e-6afe804c0c91", "name": "訂單列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "is_push_msg", "value": "0"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/orders​/${order_id}​/status"}, "id": "4ee177aa-958b-4930-afbb-36fdfde8797a", "name": "訂單狀態變更（棄單、接單、取消、退款）", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}]}, {"entity": {"type": "Service", "id": "84c70789-2392-420a-b36f-54631eac72da", "name": "products 產品"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.5", "name": "DELETE"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/products​/{product_id}"}, "id": "7fb9f47f-304c-4e13-8b84-a0f058f06d06", "name": "刪除產品", "headers": []}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/products​/${product_id}"}, "id": "a4f3e2af-5039-4284-8f0a-429825196b58", "name": "取得單一產品資料 (X)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/products​/${product_id}​/menu"}, "id": "248d1255-d671-4f30-bb4b-c6641a90975f", "name": "取得單一產品資料-點餐介面用", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "kind", "value": "0"}, {"enabled": true, "type": "Text", "name": "title", "value": "AAA"}, {"enabled": true, "type": "Text", "name": "summary", "value": "BBB"}, {"enabled": true, "type": "Text", "name": "price", "value": "10"}, {"enabled": true, "type": "Text", "name": "stock", "value": "2"}, {"enabled": true, "type": "Text", "name": "categories", "value": ""}, {"enabled": true, "type": "Text", "name": "addition_categories", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/products"}, "id": "393cb41c-089d-4099-b11c-508a35346e1d", "name": "新增產品", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "title", "value": ""}, {"enabled": true, "type": "Text", "name": "summary", "value": ""}, {"enabled": true, "type": "Text", "name": "price", "value": ""}, {"enabled": true, "type": "Text", "name": "stock", "value": ""}, {"enabled": true, "type": "Text", "name": "categories", "value": ""}, {"enabled": true, "type": "Text", "name": "addition_categories", "value": ""}, {"enabled": true, "type": "Text", "name": "addition_category_id", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/products​/${product_id}"}, "id": "cd4ec2c7-1d95-4bcb-be53-750563e63af3", "name": "更新產品", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/products"}, "id": "326b4ecb-a5ed-4110-90da-107ce6ff1ba1", "name": "產品列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/products​/${product_id}​/sold"}, "id": "af41eb68-6ef6-472d-b4f4-8437a0863e0d", "name": "產品可販售 (X)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/products​/{product_id}​/sold_out"}, "id": "da451af1-aca7-49af-89c9-e77a3b7f4644", "name": "產品售完", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}, {"entity": {"type": "Service", "id": "890a3574-28e7-44e7-b1f7-10a13087eb5b", "name": "profile 登入者個人資料相關"}, "children": [{"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "old_password", "value": "123456"}, {"enabled": true, "type": "Text", "name": "new_password", "value": "123456"}, {"enabled": true, "type": "Text", "name": "check_password", "value": "123456"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/profile/password-reset"}, "id": "8fa75483-1667-4eb4-b0c4-5a5a71ea8fc2", "name": "變更密碼 (O)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}, {"entity": {"type": "Service", "id": "59ba356b-4d9a-4002-8d61-09c118f09af7", "name": "setting 更新商店資訊"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/setting"}, "id": "e72b5cf9-187d-4d18-89c9-302995d81df7", "name": "取得餐廳設定資料 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "other", "value": "{\\n  \\\"line_order\\\": {\\n    \\\"dine_in\\\": 1,\\n    \\\"to_go\\\": 1,\\n    \\\"delivery\\\": 1\\n  },\\n  \\\"auto\\\": {\\n    \\\"order\\\": 1,\\n    \\\"order_print\\\": 1,\\n    \\\"abandon\\\": 1,\\n    \\\"abandon_min\\\": 10,\\n    \\\"close\\\": 1,\\n    \\\"close_min\\\": 5\\n  },\\n  \\\"fee\\\": {\\n    \\\"type\\\": 0,\\n    \\\"percent\\\": 10\\n  },\\n  \\\"print_order\\\": 1,\\n  \\\"print_detail\\\": 1\\n}"}, {"enabled": true, "type": "Text", "name": "business_hours", "value": "{\\n  \\\"week\\\": [\\n    {\\n      \\\"day\\\": 1,\\n      \\\"status\\\": 1,\\n      \\\"open\\\": [\\n        [\\n          \\\"10:00\\\",\\n          \\\"14:00\\\"\\n        ],\\n        [\\n          \\\"17:00\\\",\\n          \\\"22:00\\\"\\n        ]\\n      ]\\n    },\\n    {\\n      \\\"day\\\": 2,\\n      \\\"status\\\": 1,\\n      \\\"open\\\": [\\n        [\\n          \\\"10:00\\\",\\n          \\\"14:00\\\"\\n        ],\\n        [\\n          \\\"17:00\\\",\\n          \\\"22:00\\\"\\n        ]\\n      ]\\n    },\\n    {\\n      \\\"day\\\": 3,\\n      \\\"status\\\": 1,\\n      \\\"open\\\": [\\n        [\\n          \\\"10:00\\\",\\n          \\\"14:00\\\"\\n        ],\\n        [\\n          \\\"17:00\\\",\\n          \\\"22:00\\\"\\n        ]\\n      ]\\n    },\\n    {\\n      \\\"day\\\": 4,\\n      \\\"status\\\": 1,\\n      \\\"open\\\": [\\n        [\\n          \\\"10:00\\\",\\n          \\\"14:00\\\"\\n        ],\\n        [\\n          \\\"17:00\\\",\\n          \\\"22:00\\\"\\n        ]\\n      ]\\n    },\\n    {\\n      \\\"day\\\": 5,\\n      \\\"status\\\": 1,\\n      \\\"open\\\": [\\n        [\\n          \\\"10:00\\\",\\n          \\\"14:00\\\"\\n        ],\\n        [\\n          \\\"17:00\\\",\\n          \\\"22:00\\\"\\n        ]\\n      ]\\n    },\\n    {\\n      \\\"day\\\": 6,\\n      \\\"status\\\": 1,\\n      \\\"open\\\": [\\n        [\\n          \\\"10:00\\\",\\n          \\\"14:00\\\"\\n        ],\\n        [\\n          \\\"17:00\\\",\\n          \\\"22:00\\\"\\n        ]\\n      ]\\n    },\\n    {\\n      \\\"day\\\": 7,\\n      \\\"status\\\": 1,\\n      \\\"open\\\": [\\n        [\\n          \\\"10:00\\\",\\n          \\\"14:00\\\"\\n        ],\\n        [\\n          \\\"17:00\\\",\\n          \\\"22:00\\\"\\n        ]\\n      ]\\n    }\\n  ],\\n  \\\"comment\\\": \\\"aaa\\\"\\n}"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}", "path": "/setting"}, "id": "0fba9ad5-165d-40dc-ab97-6f50284830a4", "name": "更新餐廳資料 (?)", "headers": [{"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}, {"entity": {"type": "Service", "id": "********-e854-4109-b91d-b68bcc643e98", "name": "store-accounts 門市帳號"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/store-accounts​/${account_id}"}, "id": "5a3c98a8-ff05-49f1-933e-93a2d386ed2c", "name": "取得單一帳號資料 (X)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/store-accounts"}, "id": "b4bc613b-dac4-4b3f-8169-b9f9f5bad846", "name": "店員、店長帳號列表 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "role_id", "value": "2"}, {"enabled": true, "type": "Text", "name": "username", "value": "um123"}, {"enabled": true, "type": "Text", "name": "name", "value": "yuming123"}, {"enabled": true, "type": "Text", "name": "password", "value": "123456"}, {"enabled": true, "type": "Text", "name": "status", "value": "1"}, {"enabled": true, "type": "Text", "name": "comment", "value": "aaaa"}, {"type": "Text", "name": "created_at", "value": ""}, {"type": "Text", "name": "updated_at", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/store-accounts"}, "id": "974ca30d-2008-43f8-959d-5cf8676f6ee7", "name": "新增帳號 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "role_id", "value": "2"}, {"enabled": true, "type": "Text", "name": "name", "value": "yuming123"}, {"enabled": true, "type": "Text", "name": "password", "value": "123456"}, {"enabled": true, "type": "Text", "name": "status", "value": "0"}, {"enabled": true, "type": "Text", "name": "comment", "value": "bbbb"}, {"type": "Text", "name": "updated_at", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/store-accounts​/${account_id}"}, "id": "dedf7ae7-725a-4fe8-85b2-f6e7c87127f8", "name": "更新帳號", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}]}, {"entity": {"type": "Service", "id": "88af6a42-e1cf-418c-b744-db5ea7930c3f", "name": "tables 桌號"}, "children": [{"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.5", "name": "DELETE"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/tables​/${table_id}"}, "id": "63a04c79-22d6-459f-94da-9a5ea88e87db", "name": "刪除區域、桌號", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "data", "value": ""}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/tables​/sort"}, "id": "97dbd2c9-3c17-4114-a033-a35dc66ba79a", "name": "排序區域", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.3", "name": "POST"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "name", "value": "NN"}, {"enabled": true, "type": "Text", "name": "parent_id", "value": "1"}, {"enabled": true, "type": "Text", "name": "sort", "value": "999"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/tables"}, "id": "d9a18aff-94a7-4052-859a-2571eeccedbf", "name": "新增區域、桌號", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"requestBody": true, "link": "http://tools.ietf.org/html/rfc7231#section-4.3.4", "name": "PUT"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": [{"enabled": true, "type": "Text", "name": "name", "value": "A"}, {"enabled": true, "type": "Text", "name": "parent_id", "value": "1"}, {"enabled": true, "type": "Text", "name": "sort", "value": "999"}]}, "bodyType": "Form"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/tables​/${table_id}"}, "id": "14fa6da4-34d2-45ee-b272-9550edb836a7", "name": "更新區域、桌號", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}, {"enabled": true, "name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, {"entity": {"type": "Request", "method": {"link": "http://tools.ietf.org/html/rfc7231#section-4.3.1", "name": "GET"}, "body": {"formBody": {"overrideContentType": true, "encoding": "application/x-www-form-urlencoded", "items": []}, "bodyType": "Text"}, "uri": {"query": {"delimiter": "&", "items": []}, "scheme": {"secure": true, "name": "https", "version": "V11"}, "host": "${baseUrl}​", "path": "/tables"}, "id": "7ae6e33d-8799-45fc-9623-5bdc372f3022", "name": "桌號樹狀清單 (O)", "headers": [{"enabled": true, "name": "Authorization", "value": "Bearer ${token}"}]}}]}]}], "environments": [{"id": "415f572f-0317-4d2d-a1c0-2b43a033f8b2", "name": "dev-diner", "variables": {"e46b0631-c974-49a5-b8b1-29ca10987831": {"createdAt": "2021-04-17T20:59:57.572+08:00", "name": "baseUrl", "value": "dev-api-diner-app.omos.tw", "enabled": true, "private": false}, "e3253afa-be18-4e5e-82ed-65af0981a2ae": {"createdAt": "2021-04-19T12:29:03.381+08:00", "name": "member_id", "value": "2", "enabled": true, "private": false}, "0dc683b3-261d-4974-973c-1c3662b489d5": {"createdAt": "2021-04-19T12:29:03.381+08:00", "name": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.nUYD-PhV9EjreC3ZWq3Cj3te46HypAGulqopLc3uPVQ", "enabled": true, "private": false}, "1e59cd3c-45c2-46d0-b04d-110f4ec944ed": {"createdAt": "2021-04-19T15:34:45.389+08:00", "name": "table_id", "value": "8", "enabled": true, "private": false}, "c1b3e69f-422f-4960-aba0-6df96721afaa": {"createdAt": "2021-04-19T15:34:45.389+08:00", "name": "category_id", "value": "57", "enabled": true, "private": false}, "f9a06740-6c1c-42dd-8219-a31822c2844c": {"createdAt": "2021-04-19T18:30:27.374+08:00", "name": "account_id", "value": "2", "enabled": true, "private": false}, "58ba6ba8-ff79-4d28-9629-9d81eec167e4": {"createdAt": "2021-04-19T18:30:27.374+08:00", "name": "order_id", "value": "59", "enabled": true, "private": false}, "7b59f1a4-cd96-451a-bb10-98845e79df48": {"createdAt": "2021-04-19T19:57:19.296+08:00", "name": "addition_category_id", "value": "1", "enabled": true, "private": false}, "f1fec7a4-0ad2-4a3e-8d85-cc67514640d5": {"createdAt": "2021-04-19T19:57:19.296+08:00", "name": "addition_product_id", "value": "1", "enabled": true, "private": false}, "7c06f279-a54e-4618-80d4-ba1956bf39ba": {"createdAt": "2021-04-19T20:09:34.283+08:00", "name": "", "value": "", "enabled": true, "private": false}, "daf7a83b-3321-405a-97af-09876725971d": {"createdAt": "2021-04-19T20:09:34.283+08:00", "name": "product_id", "value": "1", "enabled": true, "private": false}}}, {"id": "5700659f-6127-469a-9031-1fc796166f9d", "name": "金財通 omos", "variables": {"0ce68555-c41a-4ac1-a24c-9bc82f9873a1": {"createdAt": "2021-04-06T11:44:18.725+08:00", "name": "baseUrl", "value": "*************/SCMWebAPITest/api", "enabled": true, "private": false}, "59a064f8-80d4-4299-bf2e-398205d544f2": {"createdAt": "2021-04-06T12:35:47.526+08:00", "name": "posBAN", "value": "83193989", "enabled": true, "private": false}, "56448228-4cdc-4c04-9b14-5f5469fd5458": {"createdAt": "2021-04-06T12:35:47.526+08:00", "name": "apikey", "value": "S7MTTXEE-S7MT-S7MT-S7MT-S7MTTXEEM8HY", "enabled": true, "private": false}, "29f434b5-09ac-45f2-ba40-f1cb3bfe9679": {"createdAt": "2021-04-06T13:46:46.342+08:00", "name": "host", "value": "*************", "enabled": true, "private": false}}}, {"id": "b0966f00-3e36-4a79-929b-494a85043c88", "name": "dev", "variables": {"c4a54d65-e750-4a30-8e58-09a8f22608ae": {"createdAt": "2021-02-22T17:49:52.214+08:00", "name": "baseUrl", "value": "dev-api-pos.omos.tw", "enabled": true, "private": false}, "e54584d9-fe94-4c4b-a735-9c8f4bff7550": {"createdAt": "2021-03-25T14:23:28.653+08:00", "name": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aW1lem9uZSI6IkFzaWEvVGFpcGVpIiwiY2hhbm5lbF9pZCI6MSwiaWQiOjExLCJuYW1lIjoidW1fbWciLCJicmFuZF9pZCI6MSwibGFzdF9sb2dpbiI6IjIwMjEtMDQtMTRUMTA6MDA6NDUiLCJyb2xlIjp7ImlkIjoxLCJuYW1lIjoiXHU1ZTk3XHU5NTc3In0sImNsaWVudF9pZCI6MSwic3lzX3R5cGUiOiJjYyIsImlhdCI6MTYxODM5NzM2MCwiZXhwIjoxNjE4NDAwOTYwfQ.mqmuH8hDk85_2AimYw97ZsoYzCV7_62msQUXCZ_0hp0", "enabled": true, "private": false}, "0dc3e07c-97ea-48a2-851a-6569bd821a47": {"createdAt": "2021-03-31T15:20:58.943+08:00", "name": "orderId", "value": "25", "enabled": true, "private": false}, "6af3b2d4-4b28-4bc8-9e60-825d94621207": {"createdAt": "2021-03-31T15:20:58.943+08:00", "name": "memberId", "value": "3", "enabled": true, "private": false}, "126b9762-2025-4028-a7af-b77b68ed673e": {"createdAt": "2021-04-04T15:51:22.402+08:00", "name": "couponId", "value": "8", "enabled": true, "private": false}, "877a6ade-53e6-4bb1-9ba3-9678e0142031": {"createdAt": "2021-04-04T15:51:22.402+08:00", "name": "storeAccount", "value": "1", "enabled": true, "private": false}}}, {"id": "ed062758-be78-4f77-a93e-a1729e22ed83", "name": "金財通 okshop", "variables": {"c9c5edba-e612-49a0-a111-fd2c7335c474": {"createdAt": "2021-04-06T18:31:30.931+08:00", "name": "posBAN", "value": "********", "enabled": true, "private": false}, "6f0a1f13-b2e7-4ec5-b147-7b00a4e15cbe": {"createdAt": "2021-04-06T18:31:37.828+08:00", "name": "apikey", "value": "LXTP2UV2-LXTP-LXTP-LXTP-LXTP2UV2IIWS", "enabled": true, "private": false}, "9d8257e0-bae2-4303-b8a6-fbaf47f332ef": {"createdAt": "2021-04-06T18:32:56.348+08:00", "name": "host", "value": "*************", "enabled": true, "private": false}, "bb7efb37-bebf-480a-8d34-610b7a3dca2c": {"createdAt": "2021-04-06T18:32:56.348+08:00", "name": "baseUrl", "value": "*************/SCMWebAPITest/api", "enabled": true, "private": false}}}, {"id": "edae5501-90f5-408e-b67a-9b1542acec6c", "name": "prod", "variables": {"09c3d1ff-74f6-4cf4-93bc-e769fa4202b3": {"createdAt": "2021-02-23T17:39:09.218+08:00", "name": "baseUrl", "value": "api-pos.omos.tw", "enabled": true, "private": false}}}]}