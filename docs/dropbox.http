# var

@host = https://api.dropbox.com
@app_key = lr37a5r60xyx6df
@app_secret = xaqv5x0bw4wqo4g
@refresh_token = skOEPJg6Vv0AAAAAAAAAAXcS5lhb-D5IwNmoOhBOqTtogM3-4xTQyCdEt6v46HS6

### get code (使用網頁開啟，用來取得 code)

GET https://www.dropbox.com/oauth2/authorize
?client_id=lr37a5r60xyx6df
&token_access_type=offline
&response_type=code

### get refresh_token & access_token from code
# @name get_token
# @prompt code 8FkkevIDaa8AAAAAAAAAcHC3MUkmwFirzwKM1vkprKY

curl {{host}}/oauth2/token \
-d code={{code}} \
-d grant_type=authorization_code \
-u {{app_key}}:{{app_secret}}

### get access_toke from refresh_token
# @name get_token

curl {{host}}/oauth2/token \
-d grant_type=refresh_token \
-d refresh_token={{refresh_token}} \
-u {{app_key}}:{{app_secret}}