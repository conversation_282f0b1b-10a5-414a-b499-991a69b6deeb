PODS:
  - charset_converter (0.0.1):
    - Flutter
  - device_info (0.0.1):
    - Flutter
  - Firebase/Analytics (8.11.0):
    - Firebase/Core
  - Firebase/Core (8.11.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 8.11.0)
  - Firebase/CoreOnly (8.11.0):
    - FirebaseCore (= 8.11.0)
  - Firebase/Messaging (8.11.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 8.11.0)
  - firebase_analytics (9.1.1):
    - Firebase/Analytics (= 8.11.0)
    - firebase_core
    - Flutter
  - firebase_core (1.12.0):
    - Firebase/CoreOnly (= 8.11.0)
    - Flutter
  - firebase_messaging (10.0.7):
    - Firebase/Messaging (= 8.11.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (8.11.0):
    - FirebaseAnalytics/AdIdSupport (= 8.11.0)
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (8.11.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleAppMeasurement (= 8.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - FirebaseCore (8.11.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseFirestore (8.6.0):
    - FirebaseFirestore/AutodetectLeveldb (= 8.6.0)
  - FirebaseFirestore/AutodetectLeveldb (8.6.0):
    - FirebaseFirestore/Base
    - FirebaseFirestore/WithLeveldb
  - FirebaseFirestore/Base (8.6.0)
  - FirebaseFirestore/WithLeveldb (8.6.0):
    - FirebaseFirestore/Base
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.11.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Reachability (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - nanopb (~> 2.30908.0)
  - Flutter (1.0.0)
  - flutter_bluetooth_basic (0.0.1):
    - Flutter
  - flutter_godex_printer (0.0.1):
    - Flutter
    - RxSwift
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_star_prnt (0.0.1):
    - Flutter
  - flutter_sunmi_printer (0.0.1):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - GoogleAppMeasurement (8.11.0):
    - GoogleAppMeasurement/AdIdSupport (= 8.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (8.11.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 8.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (8.11.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.0)
  - image_picker (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - ObjectBox (1.7.0)
  - objectbox_flutter_libs (0.0.1):
    - Flutter
    - ObjectBox (= 1.7.0)
  - okshop_esc_pos (0.0.1):
    - Flutter
  - package_info (0.0.1):
    - Flutter
  - path_provider (0.0.1):
    - Flutter
  - "permission_handler (5.1.0+2)":
    - Flutter
  - PromisesObjC (2.4.0)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - RxSwift (6.5.0)
  - shared_preferences (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - TOCropViewController (2.6.0)
  - url_launcher (0.0.1):
    - Flutter
  - wifi (0.0.1):
    - Flutter

DEPENDENCIES:
  - charset_converter (from `.symlinks/plugins/charset_converter/ios`)
  - device_info (from `.symlinks/plugins/device_info/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `8.6.0`)
  - Flutter (from `Flutter`)
  - flutter_bluetooth_basic (from `.symlinks/plugins/flutter_bluetooth_basic/ios`)
  - flutter_godex_printer (from `.symlinks/plugins/flutter_godex_printer/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_star_prnt (from `.symlinks/plugins/flutter_star_prnt/ios`)
  - flutter_sunmi_printer (from `.symlinks/plugins/flutter_sunmi_printer/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker (from `.symlinks/plugins/image_picker/ios`)
  - objectbox_flutter_libs (from `.symlinks/plugins/objectbox_flutter_libs/ios`)
  - okshop_esc_pos (from `.symlinks/plugins/okshop_esc_pos/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - path_provider (from `.symlinks/plugins/path_provider/ios`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - shared_preferences (from `.symlinks/plugins/shared_preferences/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher (from `.symlinks/plugins/url_launcher/ios`)
  - wifi (from `.symlinks/plugins/wifi/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - MTBBarcodeScanner
    - nanopb
    - ObjectBox
    - PromisesObjC
    - RxSwift
    - TOCropViewController

EXTERNAL SOURCES:
  charset_converter:
    :path: ".symlinks/plugins/charset_converter/ios"
  device_info:
    :path: ".symlinks/plugins/device_info/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 8.6.0
  Flutter:
    :path: Flutter
  flutter_bluetooth_basic:
    :path: ".symlinks/plugins/flutter_bluetooth_basic/ios"
  flutter_godex_printer:
    :path: ".symlinks/plugins/flutter_godex_printer/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_star_prnt:
    :path: ".symlinks/plugins/flutter_star_prnt/ios"
  flutter_sunmi_printer:
    :path: ".symlinks/plugins/flutter_sunmi_printer/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker:
    :path: ".symlinks/plugins/image_picker/ios"
  objectbox_flutter_libs:
    :path: ".symlinks/plugins/objectbox_flutter_libs/ios"
  okshop_esc_pos:
    :path: ".symlinks/plugins/okshop_esc_pos/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  path_provider:
    :path: ".symlinks/plugins/path_provider/ios"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  shared_preferences:
    :path: ".symlinks/plugins/shared_preferences/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher:
    :path: ".symlinks/plugins/url_launcher/ios"
  wifi:
    :path: ".symlinks/plugins/wifi/ios"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 8.6.0

SPEC CHECKSUMS:
  charset_converter: 82bc1d2e3c70dcb51bf769e9772e3ae5b2571695
  device_info: 52e8c0c9c61def8d0a92bf175f5f500abbea04bc
  Firebase: 44dd9724c84df18b486639e874f31436eaa9a20c
  firebase_analytics: 42463266bd1da485ab72a63ae1c0431c30d6593a
  firebase_core: 215d851c45f3ba0ad8d92fb927739ebf2efe6db5
  firebase_messaging: 3c1ceb272c4e87f3903dafe7bed31f65e74bf3ef
  FirebaseAnalytics: 4e4b13031034e6561ed3bd1d47b6fdabbd6487c6
  FirebaseCore: 2f4f85b453cc8fea4bb2b37e370007d2bcafe3f0
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseFirestore: 4449a4cee2c80daf179192cdf3f182d501eca567
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseMessaging: 02e248e8997f71fa8cc9d78e9d49ec1a701ba14a
  Flutter: 434fef37c0980e73bb6479ef766c45957d4b510c
  flutter_bluetooth_basic: 0b5a7ff38766503fc8d2cb4380c1ceda051c7b79
  flutter_godex_printer: 8cb8c715e148db3c7aec43b9e8a24c4163b5d331
  flutter_local_notifications: ef18f0537538fcd18e9106b3ddc91cc10b4e579a
  flutter_star_prnt: 72c1b7fdd0f6dcaad31714b2388e3bbd4fa09b90
  flutter_sunmi_printer: c8e1e59c080c755c066d656530b4aa5b5ecd3803
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  GoogleAppMeasurement: aa3cb422fab2b05d2efac543a5720d1a85b9dea5
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  image_cropper: fe0da2dbf8a710e74860b23f4d48c15ad8774d69
  image_picker: 44ff82ebbcddbecd7c839595114536c96b1fb76e
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  ObjectBox: 6410cda891ee71682ea1b0330a9544c52b2ba988
  objectbox_flutter_libs: eace57e73903a4fcf9b3a4d892b8c74ab32a436a
  okshop_esc_pos: 4540015beea4bd8eb01dc3e898ad3285a8880f22
  package_info: cce50adca9873c79f931618469d2114b91d71189
  path_provider: 961ce7cdf0cba062c2f6ac640bcd96d310ec8645
  permission_handler: a6eb0c0a10e733fc3e1dcea4e2d426beb7c91064
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  RxSwift: 5710a9e6b17f3c3d6e40d6e559b9fa1e813b2ef8
  shared_preferences: 47eaded4d5dc0bb45e04e66ce0b93ce876aff8a1
  sqflite: 954affaf2567c73cda074440299a625e3b2cbf8a
  TOCropViewController: 3105367e808b7d3d886a74ff59bf4804e7d3ab38
  url_launcher: 57d0ad20ca4ccf92256bb343ea186dbcf76fc042
  wifi: 4c490d72408b6f8285ede3cae98f08dcfd5bd5a1

PODFILE CHECKSUM: 76c2a92af380af5a92f2a423eef37f7df035c7e3

COCOAPODS: 1.16.2
