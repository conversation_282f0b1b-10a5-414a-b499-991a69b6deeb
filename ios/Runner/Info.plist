<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Payload</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)$(BUILD_NAME_SUFFIX)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>App 需要您的同意, 才能使用定位功能來提供服務</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>App 需要您的同意, 才能訪問媒體資料庫</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>App 需要您的同意, 才能訪問藍牙</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>App 需要您的同意, 才能訪問藍牙</string>
	<key>NSCalendarsUsageDescription</key>
	<string>App 需要您的同意, 才能訪問日曆</string>
	<key>NSCameraUsageDescription</key>
	<string>App 需要您的同意使用照相機取得商品圖片</string>
	<key>NSContactsUsageDescription</key>
	<string>App 需要您的同意, 才能訪問通信錄</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>App 需要您的同意, 才能始終訪問位置</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>App 需要您的同意, 才能在使用期間訪問位置</string>
	<key>NSMotionUsageDescription</key>
	<string>App 需要您的同意, 才能訪問運動與健身</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>App 需要您的同意使用相簿取得商品圖片</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>App 需要您的同意, 才能訪問語音識別</string>
	<key>NSBonjourServicesUsageDescription</key>
	<string>此應用程式需要存取區域網路，以便發現和連線到其他裝置。</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
</dict>
</plist>
