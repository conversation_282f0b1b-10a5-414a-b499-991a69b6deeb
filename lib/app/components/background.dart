import 'package:flutter/widgets.dart';

class Background extends StatelessWidget {
  final Widget background;
  final Widget child;

  const Background({
    Key key,
    @required this.background,
    @required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        background ?? SizedBox(),
        child ?? SizedBox(),
      ],
    );
  }
}
