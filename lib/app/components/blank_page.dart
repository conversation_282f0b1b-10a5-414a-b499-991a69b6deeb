import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

// TODO: remove me, use ListWidget.blank instead
class BlankPage extends StatelessWidget {
  const BlankPage({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 500,
      child: Center(
        child: SvgPicture.asset(
          'assets/images/app_icon.svg',
          width: 142.0,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
