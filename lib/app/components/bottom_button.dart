import 'package:flutter/material.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';

import 'rounded_button.dart';

class BottomButton extends StatelessWidget {
  final String buttonText;
  final VoidCallback onPressed;
  final Widget child;

  const BottomButton({
    Key key,
    this.buttonText,
    this.onPressed,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BottomWrapper(
      child: RoundedButton(
        buttonText: buttonText,
        child: child,
        onPressed: onPressed,
      ),
    );
  }
}
