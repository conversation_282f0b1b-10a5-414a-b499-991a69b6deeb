import 'package:flutter/material.dart';
import 'package:muyipork/app/components/background.dart';

import 'bottom_button.dart';

class BottomWidgetPage extends StatelessWidget {
  final Widget child;
  final Widget bottom;

  const BottomWidgetPage({
    Key key,
    this.child,
    this.bottom,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Background(
      background: SizedBox.expand(
        child: child ?? const SizedBox(),
      ),
      child: Align(
        alignment: Alignment.bottomCenter,
        child: bottom ?? const SizedBox(),
      ),
    );
  }

  factory BottomWidgetPage.save({
    final Key key,
    final Function onPressed,
    final Widget child,
    final String buttonText,
  }) {
    return BottomWidgetPage(
      key: key,
      child: child,
      bottom: BottomButton(
        buttonText: buttonText ?? '儲存',
        onPressed: onPressed,
      ),
    );
  }
}
