import 'package:flutter/material.dart';

class BottomWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry padding;

  const BottomWrapper({
    Key key,
    @required this.child,
    this.padding = const EdgeInsets.symmetric(
      vertical: 8.0,
      horizontal: 20.0,
    ),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Iterable<Widget> children() sync* {
      if (child != null) {
        yield child;
      }
      yield const SizedBox(height: 12.0);
    }
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, -0.45),
          colors: [
            Color(0x00FFFFFF),
            Colors.white,
          ],
          stops: [0.0, 1.0],
        ),
      ),
      padding: padding ??
          const EdgeInsets.symmetric(
            vertical: 8.0,
            horizontal: 20.0,
          ),
      // alignment: Alignment.bottomCenter,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }
}
