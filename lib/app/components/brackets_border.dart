import 'package:flutter/material.dart';

//Referenced from:
//https://stackoverflow.com/questions/59551116/flutter-how-to-draw-custom-borders-around-a-widget-inside-a-container
class BracketsBorder extends StatelessWidget {
  final Widget child;
  final Color borderColor;
  final double borderWidth, leftBorderLength, rightBorderLength;
  final EdgeInsetsGeometry padding;

  //This is just a sample, modify it as your requirement
  //add extra properties like padding,color etc.

  BracketsBorder(
      {Key k,
        @required this.child,
        @required this.borderColor,
        @required this.borderWidth,
        @required this.leftBorderLength,
        @required this.rightBorderLength,
        this.padding})
      : super(key: k);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
                border: Border(
                    left: BorderSide(color: borderColor, width: borderWidth),
                    right:
                    BorderSide(color: borderColor, width: borderWidth)),
                color: Colors.transparent),
          ),
        ),
        Positioned.fill(
          child: Container(
              color: Colors.transparent,
              child: Stack(children: [
                Positioned(
                    top: 0,
                    left: 0,
                    child: Container(
                        color: borderColor,
                        width: leftBorderLength,
                        height: borderWidth)),
                Positioned(
                    bottom: 0,
                    left: 0,
                    child: Container(
                        color: borderColor,
                        width: leftBorderLength,
                        height: borderWidth)),
                Positioned(
                    right: 0,
                    child: Container(
                        color: borderColor,
                        width: rightBorderLength,
                        height: borderWidth)),
                Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                        color: borderColor,
                        width: rightBorderLength,
                        height: borderWidth)),
              ])),
        ),
        Padding(
          padding: padding,
          child: child,
        )
      ],
    );
  }
}
