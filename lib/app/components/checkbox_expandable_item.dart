import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:muyipork/app/components/checkbox_item.dart';

class CheckboxExpandableItem extends StatefulWidget {
  CheckboxExpandableItem({
    @required this.expandedWidget,
    @required this.checkboxTitle,
    @required this.checkboxValue,
    @required this.onCheckboxChanged,
    this.displaySwitch = false,
    this.switchTitle,
    this.switchValue,
    this.onSwitchChanged,
    Key key}) : super(key: key);

  final Widget expandedWidget;

  final String checkboxTitle;
  final bool checkboxValue;
  final Function(bool) onCheckboxChanged;

  final bool displaySwitch;
  final String switchTitle;
  final bool switchValue;
  final Function(bool) onSwitchChanged;

  @override
  _CheckboxExpandableItemState createState() =>
      _CheckboxExpandableItemState();
}

class _CheckboxExpandableItemState
    extends State<CheckboxExpandableItem> {

  ExpandableController expandableController = ExpandableController();

  @override
  Widget build(BuildContext context) {
    expandableController.expanded = widget.checkboxValue;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ExpandableNotifier(
        controller: expandableController,
        child: ScrollOnExpand(
          scrollOnExpand: true,
          scrollOnCollapse: false,
          child: Expandable(
              collapsed: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  CheckboxItem(
                    checkboxTitle: widget.checkboxTitle,
                    checkboxValue: widget.checkboxValue,
                    onCheckboxChanged: widget.onCheckboxChanged,
                    displaySwitch: widget.displaySwitch,
                    switchTitle: widget.switchTitle,
                    switchValue: widget.switchValue,
                    onSwitchChanged: widget.onSwitchChanged,
                    padding: EdgeInsets.all(0),
                  ),

                ],
              ),
              expanded: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  CheckboxItem(
                    checkboxTitle: widget.checkboxTitle,
                    checkboxValue: widget.checkboxValue,
                    onCheckboxChanged: widget.onCheckboxChanged,
                    displaySwitch: widget.displaySwitch,
                    switchTitle: widget.switchTitle,
                    switchValue: widget.switchValue,
                    onSwitchChanged: widget.onSwitchChanged,
                    padding: EdgeInsets.all(0),
                  ),
                  widget.expandedWidget,
                ],
              ),
          ),
        ),
      ),
    );
  }
}