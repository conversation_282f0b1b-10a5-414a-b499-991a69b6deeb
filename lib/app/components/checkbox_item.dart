import 'package:flutter/material.dart';

class CheckboxItem extends StatelessWidget {
  CheckboxItem({
    @required this.checkboxTitle,
    @required this.checkboxValue,
    @required this.onCheckboxChanged,
    this.displaySwitch = false,
    this.switchTitle,
    this.switchValue,
    this.onSwitchChanged,
    this.padding = const EdgeInsets.symmetric(vertical: 4),
  });

  final String checkboxTitle;
  final bool checkboxValue;
  final Function(bool) onCheckboxChanged;

  final bool displaySwitch;
  final String switchTitle;
  final bool switchValue;
  final Function(bool) onSwitchChanged;

  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];

    children.add(Transform.scale(
      scale: 1.3,
      child: IgnorePointer(
        ignoring: true,
        child: Checkbox(
          value: checkboxValue,
          onChanged: (b) {},
        ),
      ),
    ));

    children.add(
      Expanded(
        child: Text(
          checkboxTitle,
          style: Theme.of(context).textTheme.subtitle1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );

    if (displaySwitch) {
      children.add(SizedBox(width: 8));
      children.add(
        Text(
          switchTitle,
          style: Theme.of(context)
              .textTheme
              .bodyText1
              .copyWith(color: Colors.grey),
        ),
      );
      children.add(Switch(
        value: switchValue,
        onChanged: (b) {
          onSwitchChanged?.call(b);
        },
      ));
    }

    children.add(SizedBox(width: 8));

    return InkWell(
      child: Padding(
        padding: padding,
        child: Container(
          color: Colors.white,
          child: Row(
            children: children,
          ),
        ),
      ),
      onTap: () => onCheckboxChanged?.call(!checkboxValue),
    );
  }
}
