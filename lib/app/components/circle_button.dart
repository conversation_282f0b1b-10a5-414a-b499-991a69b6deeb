import 'package:flutter/material.dart';
import 'package:muyipork/constants.dart';

class CircleButton extends StatelessWidget {
  final double size;
  final Widget child;
  final Function onPressed;

  const CircleButton({
    Key key,
    this.size,
    this.child,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox.fromSize(
      size: Size.square(this.size ?? 76.0),
      child: DecoratedBox(
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          gradient: const LinearGradient(
            begin: const Alignment(0.0, -1.0),
            end: const Alignment(0.0, 1.0),
            colors: const [
              kColorPrimary,
              kColorSecondary,
            ],
            stops: const [0.0, 1.0],
          ),
          boxShadow: const [
            const BoxShadow(
              color: const Color(0x40000000),
              offset: const Offset(0.0, 0.0),
              blurRadius: 10.0,
            ),
          ],
        ),
        child: TextButton(
          onPressed: this.onPressed,
          style: TextButton.styleFrom(
            shape: const CircleBorder(),
          ),
          child: this.child ?? const SizedBox(),
        ),
      ),
    );
  }
}
