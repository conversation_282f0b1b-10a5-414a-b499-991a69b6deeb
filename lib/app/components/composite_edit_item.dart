import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';

import 'rounded_shadow_container.dart';

//中間主要內容顯示模式
enum CompositeContentMode {
  OneTextField, //設定為可編輯模式，TextField形式
  TextFieldAndNumField, //設定為可編輯模式，TextField形式
  ParentButton //設定為上層選單項，按鈕形式，右邊會有一個箭頭
}

//右方按鈕顯示模式
enum CompositeRightButtonMode { Add, Remove }

//設定功能用參數
class CompositeEditItemArgs {
  CompositeEditItemArgs(
    this.contentMode,
    this.rightButtonMode, {
    this.mainButtonText,
    this.onMainButtonPressed,
    this.mainTextFieldInit,
    this.mainTextFieldHint,
    this.mainTextFieldChanged,
    this.secondaryTextFieldInit,
    this.secondaryTextFieldHint,
    this.secondaryTextFieldChanged,
    this.onRightButtonPressed,
    this.showDragHandle = true,
    this.index,
  });

  final num index;

  //中間的主要內容為何種模式
  final CompositeContentMode contentMode;

  //選單按鈕
  final String mainButtonText;
  final Function onMainButtonPressed;

  //主要輸入框
  final String mainTextFieldInit;
  final String mainTextFieldHint;
  final ValueChanged<String> mainTextFieldChanged; // realtime

  //次要輸入框
  final String secondaryTextFieldInit;
  final String secondaryTextFieldHint;
  final ValueChanged<String> secondaryTextFieldChanged; // realtime

  //右邊的按鈕顯示模式
  final CompositeRightButtonMode rightButtonMode;
  //注意這邊可以客製化為傳出當前編輯好的字串讓外面方便作業
  final AsyncValueGetter<bool> onRightButtonPressed;

  //左方是否需要顯示 handle
  final bool showDragHandle;
}

// 複合式編輯選單項 (區域、桌號、菜單都會用到)
class CompositeEditItem extends StatelessWidget {
  static const _LENGTH_LIMITATION = 8;
  //Main TextField stuffs
  final mainTextEditingController = TextEditingController();
  //Secondary TextField stuffs.
  final secondaryTextEditingController = TextEditingController();
  final CompositeEditItemArgs args;

  CompositeEditItem(
    this.args, {
    Key key,
  }) : super(key: key) {
    mainTextEditingController.text = args.mainTextFieldInit ?? '';
    secondaryTextEditingController.text = args.secondaryTextFieldInit ?? '';
  }

  @override
  Widget build(BuildContext context) {
    final rowChildren = <Widget>[];
    // Drag handle display.
    rowChildren.addIf(
      true == args.showDragHandle,
      Padding(
        padding: EdgeInsets.symmetric(horizontal: 10),
        child: ReorderableDragStartListener(
          index: args.index ?? 0,
          child: Icon(
            Icons.drag_handle,
            color: Colors.grey,
            size: 28,
          ),
        ),
      ),
    );
    rowChildren.addIf(
      false == args.showDragHandle,
      SizedBox(width: 48),
    );
    // Parent 按鈕
    rowChildren.addIf(
      CompositeContentMode.ParentButton == args.contentMode,
      _parentButton(),
    );
    // 一個主文字輸入框
    rowChildren.addIf(
      [
        CompositeContentMode.OneTextField,
        CompositeContentMode.TextFieldAndNumField,
      ].contains(args.contentMode),
      _textField(),
    );
    rowChildren.addIf(
      CompositeContentMode.TextFieldAndNumField == args.contentMode,
      SizedBox(width: 8),
    );
    rowChildren.addIf(
      CompositeContentMode.TextFieldAndNumField == args.contentMode,
      _textFieldAndNumField(),
    );
    // 決定右方按鈕樣式
    Widget rightButtonIcon = Container();

    if (args.rightButtonMode == CompositeRightButtonMode.Add) {
      // 新增按鈕
      rightButtonIcon = Container(
        padding: EdgeInsets.all(4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          border: Border.all(color: kColorPrimary, width: 2),
        ),
        child: Icon(
          Icons.add,
          size: 20,
          color: kColorPrimary,
        ),
      );
    } else if (args.rightButtonMode == CompositeRightButtonMode.Remove) {
      //移除按鈕
      rightButtonIcon = Container(
        padding: EdgeInsets.all(6),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.15),
                blurRadius: 5,
                spreadRadius: 5,
              )
            ]),
        child: Icon(
          Icons.close,
          size: 20,
          color: Colors.grey,
        ),
      );
    }
    rowChildren.add(InkWell(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Center(
          child: rightButtonIcon,
        ),
      ),
      onTap: args.onRightButtonPressed,
    ));

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Container(
        height: 44,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: rowChildren,
        ),
      ),
    );
  }

  // Parent 按鈕
  Widget _parentButton() {
    return Expanded(
      child: RoundedShadowContainer(
          child: InkWell(
        child: Row(
          children: [
            Expanded(
                child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                args?.mainButtonText ?? '',
                style: Get.textTheme.subtitle1,
              ),
            )),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey,
                size: 16,
              ),
            ),
          ],
        ),
        onTap: args?.onMainButtonPressed,
      )),
    );
  }

  Widget _textField() {
    return Expanded(
      child: RoundedShadowContainer(
        child: TextFormField(
          // initialValue: args.mainTextFieldInit ?? '',
          controller: mainTextEditingController,
          autocorrect: false,
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          inputFormatters: [
            FilteringTextInputFormatter.singleLineFormatter,
            LengthLimitingTextInputFormatter(_LENGTH_LIMITATION),
          ],
          decoration: InputDecoration(
            hintText: args.mainTextFieldHint,
            hintStyle: Get.textTheme.bodyText1.copyWith(color: Colors.grey),
            border: InputBorder.none,
            focusedBorder: InputBorder.none,
            enabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            isDense: true,
          ),
          textInputAction: TextInputAction.done,
          onChanged: args?.mainTextFieldChanged,
        ),
      ),
    );
  }

  Widget _textFieldAndNumField() {
    return Expanded(
      child: RoundedShadowContainer(
        child: TextFormField(
          // initialValue: args.secondaryTextFieldInit ?? '',
          controller: secondaryTextEditingController,
          autocorrect: false,
          decoration: InputDecoration(
            hintText: args.secondaryTextFieldHint,
            hintStyle: Get.textTheme.bodyText1.copyWith(color: Colors.grey),
            border: InputBorder.none,
            focusedBorder: InputBorder.none,
            enabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            isDense: true,
          ),
          textInputAction: TextInputAction.done,
          onChanged: args?.secondaryTextFieldChanged,
          keyboardType: TextInputType.number,
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.deny(RegExp(r'[, ]')),
            FilteringTextInputFormatter.singleLineFormatter,
            LengthLimitingTextInputFormatter(_LENGTH_LIMITATION),
          ],
        ),
      ),
    );
  }
}
