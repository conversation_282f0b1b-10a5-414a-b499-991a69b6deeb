import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/shapes/coupon_shape_border.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

class CouponItem extends StatelessWidget {
  static const _SIZE = 115.0;
  final Coupon data;
  final Function onPressed;

  const CouponItem({
    Key key,
    this.data,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          const BoxShadow(
            color: const Color(0x29000000),
            offset: const Offset(0, 3),
            blurRadius: 6,
          ),
        ],
      ),
      constraints: BoxConstraints(
        maxWidth: 350.0,
      ),
      child: ClipPath(
        clipper: ShapeBorderClipper(
          shape: CouponShapeBorder(),
        ),
        child: ColoredBox(
          color: const Color(0xFFFFF9F4),
          child: Stack(
            alignment: Alignment.centerRight,
            children: [
              Row(
                children: [
                  _image(),
                  SizedBox(width: 12),
                  Expanded(child: _main()),
                  // Visibility(
                  //   visible: onPressed != null,
                  //   child: _button(),
                  // ),
                  // Visibility(
                  //   visible: onPressed != null,
                  //   child: SizedBox(width: 12),
                  // ),
                ],
              ),
              Row(
                children: [
                  Spacer(),
                  Visibility(
                    visible: onPressed != null,
                    child: _button(),
                  ),
                  Visibility(
                    visible: onPressed != null,
                    child: SizedBox(width: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _image() {
    return SizedBox.fromSize(
      size: Size.square(_SIZE),
      child: CachedNetworkImage(
        width: _SIZE,
        height: _SIZE,
        imageUrl: data.imageUrl,
        imageBuilder: (context, imageProvider) {
          // return _placeholder();
          return DecoratedBox(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: imageProvider,
              ),
              gradient: const LinearGradient(
                begin: const Alignment(0.0, 0.0),
                end: const Alignment(1.0, 1.0),
                colors: const [
                  OKColor.Primary,
                  OKColor.Vip,
                ],
                stops: const [0.0, 1.0],
              ),
            ),
          );
        },
        placeholder: (context, url) => _placeholder(),
      ),
    );
  }

  String get _displayPromotionType {
    final name = data.promotionType?.promotionType?.name ?? '優惠';
    return '$name券';
  }

  Widget _placeholder() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        gradient: const LinearGradient(
          begin: const Alignment(0.0, 0.0),
          end: const Alignment(1.0, 1.0),
          colors: const [
            OKColor.Primary,
            OKColor.Vip,
          ],
          stops: const [0.0, 1.0],
        ),
      ),
      child: Center(
        child: Text(
          // '升級商品券',
          // (data.promotionType?.promotionType?.name ?? '優惠') + '券',
          _displayPromotionType,
          style: TextStyle(
            fontSize: 17,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _button() {
    return TextButton(
      style: TextButton.styleFrom(
        backgroundColor: OKColor.Primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
      ),
      onPressed: onPressed,
      child: Text(
        '查看',
        style: TextStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w700,
          // height: 1.125,
        ),
        // textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        // textAlign: TextAlign.center,
      ),
    );
  }

  Widget _main() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                // mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _title(),
                  SizedBox(height: 4),
                  _type(),
                  SizedBox(height: 4),
                  Visibility(
                    // visible: data.lastCount != null && data.lastCount >= 0,
                    visible:
                        data.quantityLimit != null && data.quantityLimit > 0,
                    child: _count(),
                    replacement: Text(''),
                  ),
                ],
              ),
            ),
            // Visibility(
            //   visible: onPressed != null,
            //   child: _button(),
            // ),
            // SizedBox(width: 12),
          ],
        ),
        SizedBox(height: 4),
        expiry(),
      ],
    );
  }

  Widget expiry() {
    return Text(
      // '使用期限：2021/12/31 00:00止',
      '使用期限：${data.displayLastUseDate}',
      style: TextStyle(
        fontSize: 12,
        color: OKColor.Gray66,
      ),
      textAlign: TextAlign.left,
    );
  }

  Widget _count() {
    return Row(
      children: [
        DecoratedBox(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.0),
            border: Border.all(
              width: 1.0,
              color: OKColor.Error,
            ),
          ),
          child: Padding(
            padding: kChipPadding,
            child: Text(
              '限量',
              style: TextStyle(
                fontSize: 12,
                color: OKColor.Error,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ),
        SizedBox(width: 4),
        Text(
          '尚有${data?.lastCount ?? data?.quantityLimit ?? 0}份可兌換',
          style: TextStyle(
            fontSize: 12,
            color: OKColor.Error,
          ),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  Widget _type() {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.0),
        border: Border.all(
          width: 1.0,
          color: OKColor.Primary,
        ),
      ),
      child: Padding(
        padding: kChipPadding,
        child: Text(
          // '餐飲門市券',
          data.displayType ?? '',
          style: TextStyle(
            fontSize: 12,
            color: OKColor.Primary,
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
  }

  Widget _title() {
    return Text(
      // '中杯美式咖啡兌換券',
      data.title ?? '',
      style: TextStyle(
        fontSize: 17,
        color: OKColor.Gray22,
      ),
      textAlign: TextAlign.left,
    );
  }
}
