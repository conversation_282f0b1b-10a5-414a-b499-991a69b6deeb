import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/extension.dart';

class CustomEditor extends TextFormField {
  // final String initialValue;
  // final String labelText;
  // final String hintText;
  // final bool obscureText;
  // final Color labelColor;
  // final bool enabled;
  // final ValueChanged<String> onChanged;
  // final ValueChanged<String> validator;

  // factory CustomEditor({
  //   final String initialValue,
  //   final String labelText,
  //   final String hintText,
  //   final bool obscureText,
  //   final Color labelColor,
  //   final bool enabled,
  //   final ValueChanged<String> onChanged,
  //   final ValueChanged<String> validator,
  // }) {
  //   return CustomEditor();
  // }

  CustomEditor({
    Key key,
    // this.initialValue,
    // this.enabled,
    // this.labelText,
    // this.hintText,
    // this.labelColor,
    // this.obscureText,
    // this.onChanged,
    // this.validator,
    final String initialValue,
    final String labelText,
    final String hintText,
    final bool obscureText,
    final Color labelColor,
    final Color valueColor,
    final bool enabled,
    final bool readonly,
    final int maxLines,
    final ValueChanged<String> onChanged,
    final String Function(String) validator,
    final TextInputType keyboardType,
    final List<TextInputFormatter> inputFormatters,
    final Function onTap,
    final TextEditingController controller,
    final Widget suffixIcon,
    final Widget suffix,
    final InputDecoration decoration,
    final bool isCollapsed,
    final EdgeInsets scrollPadding,
  }) : super(
          scrollPadding: scrollPadding ?? EdgeInsets.zero,
          autocorrect: false,
          controller: controller,
          onTap: onTap,
          key: key,
          keyboardType: keyboardType ?? TextInputType.text,
          enabled: enabled ?? true,
          readOnly: readonly ?? false,
          validator: validator,
          onChanged: onChanged,
          obscureText: obscureText ?? false,
          initialValue: initialValue,
          style: TextStyle(
            fontSize: 16,
            color: valueColor ?? const Color(0xff666666),
          ),
          inputFormatters: inputFormatters ??
              [
                FilteringTextInputFormatter.singleLineFormatter,
              ],
          maxLines: maxLines ?? 1,
          minLines: 1,
          decoration: decoration ??
              InputDecoration(
                isCollapsed: isCollapsed ?? false,
                isDense: true,
                suffix: suffix,
                suffixIcon: suffixIcon,
                contentPadding: EdgeInsets.zero,
                labelStyle: TextStyle(
                  fontSize: 16,
                  color: labelColor ?? Colors.black,
                  fontWeight: FontWeight.w500,
                ),
                labelText: labelText ?? '',
                hintText: hintText ?? '',
                hintStyle: const TextStyle(
                  fontSize: 16,
                  color: const Color(0xffb9b9b9),
                ),
              ),
        );

  CustomEditor.number({
    // String initialValue,
    final InputDecoration decoration,
    final TextEditingController controller,
    final ValueChanged<String> onChanged,
    final String Function(String) validator,
    final List<TextInputFormatter> inputFormatters = const [],
    final EdgeInsets scrollPadding,
    final TextAlign textAlign,
  }) : super(
          // initialValue: initialValue,
          autocorrect: false,
          validator: validator,
          onChanged: onChanged,
          onTap: controller?.selectAll,
          controller: controller,
          decoration: decoration,
          enableSuggestions: false,
          keyboardType: TextInputType.number,
          textInputAction: TextInputAction.done,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            FilteringTextInputFormatter.singleLineFormatter,
            ...inputFormatters,
          ],
          textAlign: textAlign ?? TextAlign.end,
          style: const TextStyle(
            fontSize: 24.0,
            color: OKColor.Gray33,
          ),
          scrollPadding: scrollPadding ?? EdgeInsets.zero,
        );

  // @override
  // Widget build(BuildContext context) {
  //   return TextFormField(
  //     enabled: this.enabled ?? true,
  //     validator: this.validator,
  //     onChanged: this.onChanged,
  //     obscureText: this.obscureText ?? false,
  //     initialValue: this.initialValue ?? '',
  //     style: TextStyle(
  //       fontSize: 16,
  //       color: const Color(0xff666666),
  //     ),
  //     inputFormatters: [
  //       FilteringTextInputFormatter.singleLineFormatter,
  //     ],
  //     maxLines: 1,
  //     minLines: 1,
  //     decoration: InputDecoration(
  //       contentPadding: EdgeInsets.zero,
  //       labelStyle: TextStyle(
  //         fontSize: 16,
  //         color: this.labelColor ?? Colors.black,
  //         fontWeight: FontWeight.w500,
  //       ),
  //       labelText: this.labelText ?? '',
  //       hintText: this.hintText ?? '',
  //       hintStyle: TextStyle(
  //         fontSize: 16,
  //         color: const Color(0xffb9b9b9),
  //       ),
  //     ),
  //   );
  // }
}
