import 'package:flutter/material.dart';
import 'package:muyipork/constants.dart';

import 'bg_default.dart';

class CustomScaffold extends StatelessWidget {
  final Widget child;
  final Widget title;
  final String titleText;

  const CustomScaffold({
    Key key,
    this.child,
    this.title,
    this.titleText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BgDefault(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          brightness: Brightness.dark,
          elevation: 0.0,
          backgroundColor: Colors.transparent,
          title: Visibility(
            visible: this.title != null,
            child: this.title ?? SizedBox.shrink(),
            replacement: Text(
              this.titleText ?? '',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          centerTitle: true,
        ),
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(kRadius),
            ),
            color: kColorBackground,
            // boxShadow: [
            //   BoxShadow(
            //     color: kColorBackground,
            //     offset: Offset(0, 0),
            //     blurRadius: 6,
            //   ),
            // ],
          ),
          child: this.child,
        ),
      ),
    );
  }
}
