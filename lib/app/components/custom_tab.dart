import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/colors.dart';

class CustomTab extends StatelessWidget {
  final String titleText;
  final bool selected;

  const CustomTab({
    Key key,
    this.titleText,
    this.selected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ls = <Widget>[];
    ls.addIf(
      true,
      Text(
        titleText ?? '',
        style: TextStyle(
          fontSize: 17,
        ),
      ).paddingOnly(
        right: true == selected ? 4 : 0,
      ),
    );
    ls.addIf(
      true == selected,
      Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: OKColor.Error,
        ),
      ),
    );
    return Stack(
      alignment: Alignment.topRight,
      children: ls,
    );
  }
}
