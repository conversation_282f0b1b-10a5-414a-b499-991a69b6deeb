import 'package:flutter/material.dart';
import 'package:muyipork/colors.dart';

class CustomTabBar extends TabBar {
  CustomTabBar({
    Key key,
    final ValueChanged<int> onTap,
    final bool isScrollable,
    @required final List<Widget> tabs,
    final EdgeInsetsGeometry indicatorPadding,
    final EdgeInsetsGeometry labelPadding,
    final Color themeColor,
    final TabController controller,
  }) : super(
          key: key,
          controller: controller,
          tabs: tabs ?? <Widget>[],
          isScrollable: isScrollable ?? false,
          // indicatorWeight: 2,
          indicatorColor: OKColor.Primary,
          // indicator: BoxDecoration(
          //   gradient: LinearGradient(
          //     begin: const Alignment(-1.0, -1.0),
          //     end: const Alignment(1.0, 1.0),
          //     colors: [
          //       themeColor ?? OKColor.Primary,
          //       OKColor.Accent,
          //     ],
          //     stops: const [0.0, 1.0],
          //   ),
          // ),
          // indicatorPadding: indicatorPadding ??
          //     EdgeInsets.only(
          //       top: 32,
          //       left: 8,
          //       right: 8,
          //       bottom: 4,
          //     ),
          indicatorSize: TabBarIndicatorSize.label,
          unselectedLabelColor: const Color(0xd9ffffff),
          unselectedLabelStyle: TextStyle(
            fontSize: 17,
          ),
          labelPadding: labelPadding ??
              EdgeInsets.symmetric(
                vertical: 8,
              ),
          labelColor: themeColor ?? OKColor.Primary,
          labelStyle: TextStyle(
            fontSize: 17,
          ),
          onTap: onTap,
        );
}
