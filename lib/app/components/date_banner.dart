import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

class DateBanner extends StatelessWidget {
  final _dateTime = Rx<DateTime>(null);
  final ValueChanged<DateTime> dateChanged;

  DateBanner({
    Key key,
    this.dateChanged,
    final DateTime initialDate,
  }) : super(key: key) {
    _dateTime.value = initialDate;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Spacer(),
        IconButton(
          icon: Icon(
            Icons.keyboard_arrow_left,
            color: kColorPrimary,
          ),
          onPressed: () {
            _dateTime.value = _dateTime.value.subtract(1.days);
            dateChanged?.call(_dateTime.value);
          },
        ),
        Expanded(
          flex: 2,
          child: TextButton(
            onPressed: () {
              showDatePicker(
                context: context,
                initialDate: _dateTime.value,
                firstDate: DateTime(1970, 01),
                lastDate: DateTime(2100, 12),
              ).then(
                (value) {
                  if (value != null) {
                    _dateTime.value = value;
                    dateChanged?.call(_dateTime.value);
                  }
                },
              );
            },
            child: Obx(() {
              return Text(
                // '4月1日 週四',
                _dateTime.value.MMMEd ?? '',
                style: TextStyle(
                  fontSize: 16,
                  color: kColorPrimary,
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              );
            }),
          ),
        ),
        IconButton(
          icon: Icon(
            Icons.keyboard_arrow_right,
            color: kColorPrimary,
          ),
          onPressed: () {
            _dateTime.value = _dateTime.value.add(1.days);
            dateChanged?.call(_dateTime.value);
          },
        ),
        Spacer(),
      ],
    );
  }
}
