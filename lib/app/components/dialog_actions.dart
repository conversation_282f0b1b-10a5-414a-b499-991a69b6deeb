import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DialogActions extends StatelessWidget {
  final String titleText;
  final Iterable<String> actions;

  const DialogActions({
    Key key,
    this.titleText,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final list = actions ?? <String>[];

    final children = List.generate(list.length, (index) {
      final element = actions.elementAt(index);
      return SimpleDialogOption(
        onPressed: () {
          Get.back<num>(result: index);
        },
        child: Text(
          element ?? '',
          style: TextStyle(
            fontSize: 16,
          ),
        ),
      );
    });

    return SimpleDialog(
      title: Text(titleText ?? ''),
      titleTextStyle: const TextStyle(
        fontSize: 20,
        color: const Color(0xff333333),
        fontWeight: FontWeight.w700,
      ),
      children: children,
    );
  }
}
