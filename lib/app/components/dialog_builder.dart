import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DialogBuilder extends StatelessWidget {
  final WidgetBuilder builder;

  const DialogBuilder({
    Key key,
    @required this.builder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: this.builder != null,
      child: this.builder?.call(context) ?? SizedBox.shrink(),
    );
  }
}
