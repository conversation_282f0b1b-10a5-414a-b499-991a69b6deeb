import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/components/rounded_button.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';

// 指定要顯示哪個 Icon 在 Content 之上。
enum DialogContentIcon {
  None,
  Alert,
  Okay,
  Error,
  Question,
}

// 設定 Dialog 用的參數結構
class DialogArgs {
  const DialogArgs({
    Key key,
    this.header,
    this.content,
    this.contentIcon = DialogContentIcon.None,
    this.mainButtonText,
    this.onMainButtonPress,
    this.enableMainButton = true,
    this.secondaryButtonText,
    this.onSecondaryButtonPress,
    this.enableSecondaryButton = true,
    this.contentPadding,
  });

  final EdgeInsetsGeometry contentPadding;

  // All of these are optional!
  final Widget header;
  final Widget content;

  // The content icon?
  final DialogContentIcon contentIcon;

  //If the button text is left empty. No button will be shown.
  final String mainButtonText;
  final Function onMainButtonPress;
  final bool enableMainButton;
  final String secondaryButtonText;
  final Function onSecondaryButtonPress;
  final bool enableSecondaryButton;
}

class DialogGeneral extends StatelessWidget {
  final DialogArgs args;

  const DialogGeneral(
    this.args, {
    Key key,
  }) : super(key: key);

  DialogGeneral.quest(
    final String message, {
    final DialogContentIcon icon,
    final Function onMainButtonPressed,
    final Function onSecondaryButtonPress,
    final String mainButtonText,
    final String secondaryButtonText,
  }) : args = DialogArgs(
          contentIcon: icon ?? DialogContentIcon.Question,
          content: centerContentText(message ?? ''),
          mainButtonText: mainButtonText ?? '確定',
          secondaryButtonText: secondaryButtonText ?? '取消',
          onMainButtonPress: onMainButtonPressed,
          onSecondaryButtonPress: onSecondaryButtonPress,
        );

  DialogGeneral.alert(
    final String message, {
    final DialogContentIcon icon,
  }) : args = DialogArgs(
          contentIcon: icon ?? DialogContentIcon.Alert,
          content: centerContentText(message ?? ''),
          mainButtonText: '確定',
        );

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(20.0)),
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Color(0x4d000000),
              offset: Offset(0.0, 0.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: _children().column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    if (args.header != null) {
      yield Container(
        height: 58.0,
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Color(0x4d000000),
              offset: Offset(0.0, 0.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: args.header ?? const SizedBox.shrink(),
      );
    }
    yield _content();
    final footer = _footer();
    if (footer != null) {
      yield Container(
        padding: const EdgeInsets.all(2.0),
        decoration: const BoxDecoration(
          color: Color(0xffeeeef3),
        ),
        child: SizedBox(
          height: 40.0,
          child: footer ?? const SizedBox.shrink(),
        ),
      );
    }
  }

  Widget _content() {
    Iterable<Widget> children() sync* {
      var needSeparator = false;
      final icon = _icon();
      if (icon != null) {
        yield icon;
        needSeparator = true;
      }
      if (args.content != null) {
        if (needSeparator == true) {
          yield const SizedBox(height: kPadding);
        }
        yield args.content;
        needSeparator = true;
      }
    }

    return Flexible(
      child: SingleChildScrollView(
        padding: args.contentPadding ??
            const EdgeInsets.symmetric(
              vertical: kPadding,
              horizontal: kPadding,
            ),
        child: children().column(),
      ),
    );
  }

  Widget _footer() {
    if (args.mainButtonText != null &&
        args.mainButtonText.isNotEmpty &&
        args.secondaryButtonText != null &&
        args.secondaryButtonText.isNotEmpty) {
      // 兩個按鈕
      return YesNoButton(
        leftButtonText: args.secondaryButtonText,
        rightButtonText: args.mainButtonText,
        onLeftPressed: () {
          if (args.enableSecondaryButton == true) {
            Get.back();
            // Get.back(result: Button.Negative);
            args.onSecondaryButtonPress?.call();
          }
        },
        onRightPressed: () {
          if (args.enableMainButton == true) {
            Get.back();
            // Get.back(result: Button.Positive);
            args.onMainButtonPress?.call();
          }
        },
      );
    }
    if (args.mainButtonText != null && args.mainButtonText.isNotEmpty) {
      // 一個 positive 按鈕
      return RoundedButton(
        buttonText: args.mainButtonText,
        onPressed: () {
          if (args.enableMainButton == true) {
            Get.back();
            // Get.back(result: Button.Positive);
            args.onMainButtonPress?.call();
          }
        },
      );
    }
    if (args.secondaryButtonText != null &&
        args.secondaryButtonText.isNotEmpty) {
      // 一個 negative 按鈕
      return RoundedButton(
        buttonText: args.secondaryButtonText,
        onPressed: () {
          if (args.enableSecondaryButton == true) {
            Get.back();
            // Get.back(result: Button.Negative);
            args.onSecondaryButtonPress?.call();
          }
        },
      );
    }
    return null;
  }

  Widget _icon() {
    switch (args.contentIcon) {
      case DialogContentIcon.Alert:
        return Icon(
          Icons.error_outline,
          color: OKColor.Primary,
          size: 72.0,
        );
        break;
      case DialogContentIcon.Okay:
        return Icon(
          Icons.check_circle_outline,
          color: OKColor.Primary,
          size: 72.0,
        );
        break;
      case DialogContentIcon.Error:
        return Icon(
          Icons.error_outline,
          color: OKColor.Error,
          size: 72.0,
        );
        break;
      case DialogContentIcon.Question:
        return Icon(
          Icons.help_outline,
          color: OKColor.Primary,
          size: 72.0,
        );
        break;
      case DialogContentIcon.None:
        break;
      default:
        break;
    }
    return null;
  }

  // Static methods

  // Define the title text style here.
  // Use this to make a correct styled title for Dialog.
  static Widget titleText(String text) {
    return Text(
      text ?? '',
      style: Get.textTheme.headline6.copyWith(fontWeight: FontWeight.bold),
      textAlign: TextAlign.center,
    );
  }

  // A standard content text.
  static Widget centerContentText(String text) {
    return Text(
      text ?? '',
      style: Get.textTheme.subtitle1,
      textAlign: TextAlign.center,
    );
  }
}
