import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/rounded_button.dart';
import 'package:muyipork/constants.dart';

class DialogOkay extends StatelessWidget {
  final String titleText;
  final String contextText;
  final String buttonText;
  final Function onPressed;
  final Widget content;

  const DialogOkay({
    Key key,
    this.titleText,
    this.contextText,
    this.buttonText,
    this.onPressed,
    this.content,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300.0,
      decoration: BoxDecoration(
        borderRadius: kBorderRadius20,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0x4d000000),
            offset: Offset(0, 0),
            blurRadius: 6,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Visibility(
            visible: this.titleText?.isNotEmpty ?? false,
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(
                vertical: 14.0,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(
                  top: kRadius20,
                ),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 0),
                    blurRadius: 6,
                  ),
                ],
              ),
              child: Text(
                this.titleText ?? '',
                style: TextStyle(
                  fontSize: 20,
                  color: const Color(0xff333333),
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            replacement: SizedBox(
              height: 20.0,
            ),
          ),
          Visibility(
            visible: this.content != null,
            child: this.content ?? SizedBox.shrink(),
            replacement: Padding(
              padding: const EdgeInsets.all(26.0),
              child: Text(
                this.contextText ?? '',
                style: TextStyle(
                  fontSize: 16,
                  color: const Color(0xff666666),
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.all(2.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(
                bottom: kRadius20,
              ),
              color: const Color(0xffeeeef3),
            ),
            child: RoundedButton(
              buttonText: this.buttonText,
              onPressed: () {
                if (this.onPressed != null) {
                  this.onPressed.call();
                } else {
                  Get.back();
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
