import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';

import 'yes_no_button.dart';

class DialogYesNo extends StatelessWidget {
  final String titleText;
  final String contentText;
  final Widget content;
  //
  final Function onLeftPressed;
  final Function onRightPressed;
  final String leftButtonText;
  final String rightButtonText;
  final Color leftColor;
  final Color rightColor;

  const DialogYesNo({
    Key key,
    this.titleText,
    this.contentText,
    this.content,
    this.onLeftPressed,
    this.onRightPressed,
    this.leftButtonText,
    this.rightButtonText,
    this.leftColor,
    this.rightColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300.0,
      decoration: BoxDecoration(
        borderRadius: kBorderRadius20,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0x4d000000),
            offset: Offset(0, 0),
            blurRadius: 6,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Visibility(
            visible: this.titleText?.isNotEmpty ?? false,
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(
                vertical: 14.0,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(
                  top: kRadius20,
                ),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 0),
                    blurRadius: 6,
                  ),
                ],
              ),
              child: Text(
                this.titleText ?? '',
                style: TextStyle(
                  fontSize: 20,
                  color: const Color(0xff333333),
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            replacement: SizedBox(
              height: 20.0,
            ),
          ),
          Visibility(
            visible: this.content != null,
            child: this.content ?? SizedBox.shrink(),
            replacement: Padding(
              padding: const EdgeInsets.all(26.0),
              child: Text(
                this.contentText ?? '',
                style: TextStyle(
                  fontSize: 16,
                  color: const Color(0xff666666),
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.all(2.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(
                bottom: kRadius20,
              ),
              color: const Color(0xffeeeef3),
            ),
            child: YesNoButton(
              onLeftPressed: () {
                if (this.onLeftPressed != null) {
                  this.onLeftPressed.call();
                } else {
                  Get.back(result: 0);
                }
              },
              onRightPressed: () {
                if (this.onRightPressed != null) {
                  this.onRightPressed.call();
                } else {
                  Get.back(result: 1);
                }
              },
              leftButtonText: this.leftButtonText,
              rightButtonText: this.rightButtonText,
              leftColor: this.leftColor,
              rightColor: this.rightColor,
            ),
          ),
        ],
      ),
    );
  }
}
