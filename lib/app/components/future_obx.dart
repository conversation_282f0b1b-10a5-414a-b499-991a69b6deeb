import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'dialog_general.dart';

class FutureObx<T> extends StatelessWidget {
  final Future<T> future;
  final NotifierBuilder<T> onData;
  final Widget Function(Object error) onError;
  final Widget onLoading;
  // final Widget onEmpty;

  const FutureObx(
    this.future,
    this.onData, {
    Key key,
    this.onError,
    this.onLoading,
    // this.onEmpty,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<T>(
      future: this.future,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          final errorWidget = onError?.call(snapshot.error);
          return errorWidget ?? DialogGeneral.alert('${snapshot.error}');
        }
        if (snapshot.hasData) {
          return onData(snapshot.data);
        }
        return onLoading ?? const Center(child: CircularProgressIndicator());
      },
    );
  }
}
