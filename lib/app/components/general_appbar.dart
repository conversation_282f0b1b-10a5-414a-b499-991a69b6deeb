import 'package:flutter/material.dart';
import 'package:get/get.dart';

// 通用AppBar, 因為App內的Appbar型態幾乎一致，所以直接做一條訂好Style讓所有 View 都可以方便使用。
class GeneralAppbar extends StatelessWidget implements PreferredSizeWidget {
  final Widget title;
  final Widget leading;
  final double leadingWidth;
  // 客製化離開按鈕呼叫方法。留空則會有預設返回上一頁行為。
  final Function onBackPress;
  final List<Widget> actions;

  const GeneralAppbar({
    Key key,
    this.title,
    this.leading,
    this.leadingWidth = 56.0,
    this.onBackPress,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: leading ?? _defaultBackButton(context),
      leadingWidth: leadingWidth,
      title: title,
      centerTitle: true,
      actions: actions,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight); // default is 56.0

  // void _defaultOnBackPress() {
  //   Get.back();
  // }

  Widget _defaultBackButton(BuildContext context) {
    return InkWell(
      customBorder: CircleBorder(),
      child: Center(
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(18),
            color: Colors.white.withOpacity(0.33),
          ),
          child: Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
        ),
      ),
      onTap: () {
        if (onBackPress != null) {
          onBackPress();
        } else {
          Navigator.maybePop(context);
        }
      },
    );
  }
}
