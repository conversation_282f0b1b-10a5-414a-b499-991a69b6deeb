import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';

import 'underline_divider.dart';

// 結帳畫面選項 (OrdersSumUp會用到)
class IconMenuItem extends StatelessWidget {
  final IconData icon;
  final Widget leading;
  final Widget middle;
  final String titleText;
  final Widget trailing;
  final Widget subtitle;
  final double minHeight;

  IconMenuItem({
    this.icon,
    this.titleText,
    this.trailing,
    this.leading,
    this.middle,
    this.subtitle,
    this.minHeight,
  });

  @override
  Widget build(BuildContext context) {
    return UnderlineDivider(
      insets: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      child: _main().paddingSymmetric(
        vertical: 8.0,
      ),
    ).paddingSymmetric(
      horizontal: kPadding,
    );
  }

  Widget _main() {
    final ls = <Widget>[];
    ls.addIf(
      true,
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              constraints: BoxConstraints(
                minHeight: minHeight ?? 0,
              ),
              child: _leading(),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: _trailing(),
            ),
          ),
        ],
      ),
    );
    ls.addIf(subtitle != null, subtitle);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: ls,
    );
  }

  Widget _trailing() {
    return trailing ?? SizedBox.shrink();
  }

  Widget _leading() {
    return Row(
      children: [
        _icon(),
        const SizedBox(
          width: 12.0,
        ),
        Expanded(
          child: Text(
            this.titleText ?? '',
            style: const TextStyle(
              fontSize: 14.0,
              color: const Color(0xff6d7278),
            ),
            textAlign: TextAlign.left,
          ),
        ),
        this.middle ?? SizedBox.shrink(),
      ],
    );
  }

  Widget _icon() {
    return SizedBox.fromSize(
      size: Size.square(24.0),
      child: leading ??
          Icon(
            this.icon,
            color: const Color(0xFF6D7278),
          ),
    );
  }
}
