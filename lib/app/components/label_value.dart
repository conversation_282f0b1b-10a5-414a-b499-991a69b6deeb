import 'package:flutter/material.dart';

// TODO: remove me, use LabelValueText instead
class LabelValue extends StatelessWidget {
  final String labelText;
  final String valueText;
  final Color labelColor;
  final Color valueColor;
  final TextStyle labelStyle;
  final TextStyle valueStyle;

  const LabelValue({
    Key key,
    this.labelText,
    this.valueText,
    this.labelColor,
    this.valueColor,
    this.labelStyle,
    this.valueStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        style: this.labelStyle ??
            TextStyle(
              fontSize: 16,
              height: 1.0,
              color: this.labelColor ?? const Color(0xff6d7278),
            ),
        children: [
          TextSpan(
            text: this.labelText ?? '',
          ),
          TextSpan(
            text: this.valueText ?? '',
            style: this.valueStyle ??
                TextStyle(
                  color: this.valueColor ?? Colors.black,
                ),
          ),
        ],
      ),
      softWrap: true,
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.left,
    );
  }
}
