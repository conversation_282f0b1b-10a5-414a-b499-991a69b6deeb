import 'package:flutter/material.dart';

import 'left_center_right.dart';

class TextArgs {
  final String text;
  final double fontSize;
  final Color color;
  final TextAlign textAlign;

  TextArgs(
    this.text, {
    this.fontSize = 19.0,
    this.color = Colors.black,
  }) : textAlign = TextAlign.center;

  TextArgs.left(
    this.text, {
    this.fontSize = 19.0,
    this.color = Colors.black,
  }) : textAlign = TextAlign.left;

  TextArgs.right(
    this.text, {
    this.fontSize = 19.0,
    this.color = Colors.black,
  }) : textAlign = TextAlign.right;
}

class LabelValueText extends StatelessWidget {
  final TextArgs left;
  final TextArgs right;

  const LabelValueText({
    Key key,
    this.left,
    this.right,
  }) : super(key: key);

  LabelValueText.text(String leftText, String rightText)
      : left = TextArgs(leftText),
        right = TextArgs(rightText);

  @override
  Widget build(BuildContext context) {
    return LeftCenterRight(
      left: left == null
          ? null
          : Text(
              left?.text ?? '',
              style: TextStyle(
                fontSize: left?.fontSize,
                color: left?.color ?? Colors.black,
              ),
              textAlign: left?.textAlign,
              maxLines: 1,
              softWrap: false,
              overflow: TextOverflow.ellipsis,
            ),
      right: right == null
          ? null
          : Text(
              right?.text ?? '',
              style: TextStyle(
                fontSize: right?.fontSize,
                color: right?.color ?? Colors.black,
              ),
              textAlign: right.textAlign ?? TextAlign.right,
              maxLines: 1,
              softWrap: false,
              overflow: TextOverflow.ellipsis,
            ),
    );
  }
}
