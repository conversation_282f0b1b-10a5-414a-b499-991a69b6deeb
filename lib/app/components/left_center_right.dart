import 'package:flutter/material.dart';

class LeftCenterRight extends StatelessWidget {
  final Widget left;
  final Widget right;
  final Widget center;

  const LeftCenterRight({
    Key key,
    this.left,
    this.center,
    this.right,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Visibility(
          visible: left != null,
          child: Expanded(
            child: left ?? SizedBox.shrink(),
          ),
        ),
        Visibility(
          visible: center != null,
          child: center ?? SizedBox.shrink(),
          replacement: SizedBox(
            width: 12,
          ),
        ),
        Visibility(
          visible: right != null,
          child: Expanded(
            child: right ?? SizedBox.shrink(),
          ),
        ),
      ],
    );
  }
}
