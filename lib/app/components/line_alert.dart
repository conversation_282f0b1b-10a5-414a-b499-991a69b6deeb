import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';

class LineAlert extends StatelessWidget {
  const LineAlert({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: const BoxDecoration(
        borderRadius: kBorderRadius20,
        color: Colors.white,
        boxShadow: const [
          const BoxShadow(
            color: const Color(0x4d000000),
            offset: const Offset(0.0, 0.0),
            blurRadius: 6.0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const _Header(),
          const Text(
            '主動與消費者聯繫將扣除官方帳號的訊息則數，請檢查則數可用數量再發送訊息，謝謝!',
            style: const TextStyle(
              fontSize: 16,
              color: const Color(0xff666666),
            ),
            textAlign: TextAlign.center,
          ).paddingSymmetric(
            vertical: 28.0,
            horizontal: 26.0,
          )
        ],
      ),
    );
  }
}

class _Header extends StatelessWidget {
  const _Header({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          borderRadius: const BorderRadius.vertical(
            top: kRadius20,
          ),
          color: Colors.white,
          boxShadow: const [
            const BoxShadow(
              color: const Color(0x4d000000),
              offset: const Offset(0.0, 0.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 20.0,
                height: 20.0,
                child: SvgPicture.asset(
                  'assets/images/icon_bell.svg',
                ),
              ),
              const SizedBox(
                width: 12.0,
              ),
              Text(
                '貼心小提醒',
                style: const TextStyle(
                  fontSize: 20,
                  color: kColorLine,
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ).paddingSymmetric(
                vertical: 12.0,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
