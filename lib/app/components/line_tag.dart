import 'package:flutter/material.dart';
import 'package:muyipork/constants.dart';

class LineTag extends StatelessWidget {
  final String text;
  final Color textColor;

  const LineTag({
    @required this.text,
    @required this.textColor,
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[];
    children.add(
      SizedBox(
        height: kPadding,
        child: VerticalDivider(),
      ),
    );
    children.add(
      Text(
        text ?? '',
        style: TextStyle(
          fontSize: 13,
          color: textColor,
        ),
        textAlign: TextAlign.center,
      ),
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
