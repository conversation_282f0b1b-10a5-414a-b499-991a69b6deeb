import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import 'settings_widget.dart';

class MenuColumn extends StatelessWidget {
  final Widget header;
  final Widget child;

  const MenuColumn({
    Key key,
    this.header,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        this.header ?? MenuHeader(),
        Expanded(
          child: child ?? SizedBox.shrink(),
          // child: this.child ??
          //     ListView.separated(
          //       padding: EdgeInsets.zero,
          //       shrinkWrap: true,
          //       itemBuilder: (context, index) {
          //         if (index >= (10 - 1)) {
          //           return MenuBottom();
          //         }
          //         return ColoredBox(
          //           color: Colors.white,
          //           child: MenuCard().paddingSymmetric(
          //             horizontal: 12.0,
          //           ),
          //         );
          //       },
          //       separatorBuilder: (context, index) {
          //         return SettingsWidget.divider();
          //       },
          //       // itemCount: 2,
          //       itemCount: 10,
          //     ),
        ),
      ],
    );
  }
}

class MenuHeader extends StatelessWidget {
  final String titleText;
  final bool indicator;

  bool get _indicator {
    final isNotEmpty = this.titleText?.isNotEmpty ?? false;
    final showIndicator = this.indicator ?? false;
    return showIndicator && isNotEmpty;
  }

  const MenuHeader({
    Key key,
    this.titleText,
    this.indicator,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80.0,
      decoration: const BoxDecoration(
        borderRadius: const BorderRadius.vertical(
          top: const Radius.circular(30.0),
        ),
        color: kColorTabBar,
        boxShadow: const [
          const BoxShadow(
            color: const Color(0x29000000),
            offset: const Offset(0.0, 3.0),
            blurRadius: 6.0,
          ),
        ],
      ),
      child: Center(
        child: Stack(
          alignment: Alignment.topRight,
          children: [
            Text(
              titleText ?? '',
              style: const TextStyle(
                fontSize: 24.0,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ).paddingSymmetric(
              horizontal: 0,
            ),
            Visibility(
              visible: this._indicator,
              child: Transform.translate(
                offset: Offset(16, 0),
                child: TabPageSelectorIndicator(
                  backgroundColor: kColorError,
                  borderColor: kColorError,
                  size: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// class MenuCard extends StatelessWidget {
//   final String titleText;
//   final String subtitleText;
//   final num price;
//   final num count;

//   bool get _badgeVisible => (count ?? 0) > 0;

//   const MenuCard({
//     Key key,
//     this.titleText,
//     this.subtitleText,
//     this.price,
//     this.count,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         const SizedBox(
//           height: 20.0,
//         ),
//         Row(
//           children: [
//             Expanded(
//               child: Text(
//                 // '厚切腰內豬排',
//                 this.titleText ?? '',
//                 style: const TextStyle(
//                   fontSize: 24,
//                   color: const Color(0xff222222),
//                 ),
//                 textAlign: TextAlign.left,
//               ),
//             ),
//             Visibility(
//               visible: this._badgeVisible,
//               child: _MenuBadge(
//                 text: '${this.count}',
//               ),
//             ),
//           ],
//         ),
//         const SizedBox(
//           height: 6.0,
//         ),
//         Text(
//           // '產品簡介文字',
//           this.subtitleText ?? '',
//           style: const TextStyle(
//             fontSize: 17,
//             color: const Color(0xFF6D7278),
//           ),
//           textAlign: TextAlign.left,
//         ),
//         const SizedBox(
//           height: 6.0,
//         ),
//         Text(
//           // '\$408',
//           this.price?.displayCurrency ?? '',
//           style: const TextStyle(
//             fontSize: 24.0,
//             color: kColorPrimary,
//           ),
//           textAlign: TextAlign.left,
//         ),
//         const SizedBox(
//           height: 12.0,
//         ),
//       ],
//     );
//   }
// }

class MenuBottom extends StatelessWidget {
  const MenuBottom({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        border: const Border(
          bottom: const BorderSide(
            width: 12.0,
            color: Colors.transparent,
          ),
        ),
      ),
      child: Container(
        height: 40.0,
        decoration: const BoxDecoration(
          borderRadius: const BorderRadius.vertical(
            bottom: const Radius.circular(30.0),
          ),
          color: Colors.white,
          boxShadow: const [
            const BoxShadow(
              color: const Color(0x29000000),
              offset: const Offset(0.0, 3.0),
              blurRadius: 6.0,
            ),
          ],
        ),
      ),
    );
  }
}

// class _MenuBadge extends StatelessWidget {
//   final String text;

//   const _MenuBadge({
//     Key key,
//     this.text,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     // return ElevatedButton(
//     //   style: TextButton.styleFrom(
//     //     backgroundColor: kColorPrimary,
//     //     padding: EdgeInsets.symmetric(
//     //       vertical: 4.0,
//     //       horizontal: 10.0,
//     //     ),
//     //     shape: StadiumBorder(),
//     //     minimumSize: Size.zero,
//     //   ),
//     //   onPressed: () {
//     //     //
//     //   },
//     //   child: Text(
//     //     this.text ?? '0',
//     //     style: const TextStyle(
//     //       fontSize: 17,
//     //       color: Colors.white,
//     //     ),
//     //     textAlign: TextAlign.center,
//     //   ),
//     // );
//     return Container(
//       padding: const EdgeInsets.symmetric(
//         vertical: 4.0,
//         horizontal: 10.0,
//       ),
//       decoration: const BoxDecoration(
//         borderRadius: const BorderRadius.all(
//           const Radius.elliptical(9999.0, 9999.0),
//         ),
//         color: kColorPrimary,
//       ),
//       child: Text(
//         this.text ?? '0',
//         style: const TextStyle(
//           fontSize: 17,
//           color: Colors.white,
//         ),
//         textAlign: TextAlign.center,
//       ),
//     );
//   }
// }
