import 'package:flutter/material.dart';
import 'package:muyipork/colors.dart';

import 'dialog_general.dart';

class MessagePage extends StatelessWidget {
  final DialogContentIcon icon;
  final String message;
  final String buttonText;
  final Function onPressed;

  const MessagePage({
    Key key,
    this.message,
    this.icon = DialogContentIcon.None,
    this.onPressed,
    this.buttonText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[];
    children.add(
      Padding(
        padding: const EdgeInsets.all(8.0),
        child: _icon(),
      ),
    );
    children.add(
      Text(
        message ?? "",
        style: const TextStyle(
          fontSize: 16,
        ),
      ),
    );
    if (buttonText != null && buttonText.isNotEmpty) {
      children.add(Padding(
        padding: const EdgeInsets.all(8.0),
        child: OutlinedButton(
          child: Text(
            buttonText ?? '',
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
          onPressed: onPressed,
        ),
      ));
    }
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  Widget _icon() {
    switch (icon) {
      case DialogContentIcon.Alert:
        return Icon(
          Icons.error_outline,
          color: OKColor.Primary,
          size: 72.0,
        );
      case DialogContentIcon.Okay:
        return Icon(
          Icons.check_circle_outline,
          color: OKColor.Primary,
          size: 72.0,
        );
      case DialogContentIcon.Error:
        return Icon(
          Icons.error_outline,
          color: OKColor.Error,
          size: 72.0,
        );
      default:
        return SizedBox.shrink();
    }
  }
}
