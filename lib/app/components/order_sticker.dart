import 'package:flutter/material.dart';
import 'package:muyipork/extension.dart';

import 'sticker_widget.dart';

class OrderSticker extends StatelessWidget {
  final Sticker data;

  const OrderSticker(this.data);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25.0),
        boxShadow: [
          BoxShadow(
            color: const Color(0x29000000),
            offset: Offset(0, 3),
            blurRadius: 6,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25.0),
        child: SizedBox(
          width: StickerWidget.DEFAULT_WIDTH,
          child: AspectRatio(
            aspectRatio: 4.0 / 3.0,
            child: StickerWidget(data: data),
          ),
        ),
      ),
    );
  }
}
