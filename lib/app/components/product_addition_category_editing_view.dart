import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/products_post_req.dart';

import 'dialog_general.dart';
import 'radio_button.dart';

//這東西已經特化成要編輯 Addition Category, 所以直接把 modal 丟進來編了
class ProductAdditionCategoryEditingView extends StatefulWidget {
  ProductAdditionCategoryEditingView(
      {@required this.additionCategorySetting,
      @required this.onOptionMinChanged,
      Key key})
      : super(key: key);

  final AdditionCategorySetting additionCategorySetting;
  final Function onOptionMinChanged;

  @override
  _ProductAdditionCategoryEditingViewState createState() =>
      _ProductAdditionCategoryEditingViewState();
}

class _ProductAdditionCategoryEditingViewState
    extends State<ProductAdditionCategoryEditingView> {
  TextEditingController titleEditingController = TextEditingController();

  TextEditingController optionMinEditingController = TextEditingController();
  TextEditingController optionMaxEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();

    if (widget.additionCategorySetting != null) {
      titleEditingController.text = widget.additionCategorySetting.title;
      optionMinEditingController.text =
          widget.additionCategorySetting.optionMin.toString();
      optionMaxEditingController.text =
          widget.additionCategorySetting.optionMax.toString();
    }
  }

  @override
  void didUpdateWidget(covariant ProductAdditionCategoryEditingView oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.additionCategorySetting != null) {
      titleEditingController.text = widget.additionCategorySetting.title;
      optionMinEditingController.text =
          widget.additionCategorySetting.optionMin.toString();
      optionMaxEditingController.text =
          widget.additionCategorySetting.optionMax.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget mainWidget = Container();
    if (widget.additionCategorySetting != null) {
      mainWidget = Container(
        padding: EdgeInsets.all(10),
        color: Colors.grey.shade200,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //Edit title
            Focus(
              child: TextField(
                controller: titleEditingController,
                decoration: InputDecoration(
                  // fillColor: Colors.white,
                  // filled: true,
                  labelText: '前台顯示名稱*',
                  hintText: '前台顯示名稱',
                  hintStyle:
                      Get.textTheme.bodyText1.copyWith(color: Colors.grey),
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 4, horizontal: 10),
                  isDense: true,
                ),
                textInputAction: TextInputAction.done,
                onChanged: (text) {
                  //Set title? Nope, We'll do it in onFocusChange.
                },
              ),
              onFocusChange: (hasFocus) async {
                if (!hasFocus) {
                  tryStoreTitle();
                }
              },
            ),

            RadioButton<int>(
              titleText: '單選',
              value: 0,
              groupValue: widget.additionCategorySetting.option,
              onChanged: (value) {
                setState(() {
                  widget.additionCategorySetting.option = value;
                });
              },
            ),
            RadioButton<int>(
              child: Row(
                children: [
                  Text('複選 (最少'),

                  //Option Min
                  SizedBox(
                    width: 60,
                    child: Focus(
                      child: TextField(
                        textAlign: TextAlign.center,
                        controller: optionMinEditingController,
                        decoration: InputDecoration(
                          // fillColor: Colors.white,
                          // filled: true,
                          contentPadding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        textInputAction: TextInputAction.done,
                        onChanged: (text) {
                          //Set title? Nope, We'll do it in onFocusChange.
                        },
                      ),
                      onFocusChange: (hasFocus) async {
                        if (!hasFocus) {
                          tryStoreOptionMin();
                        }
                      },
                    ),
                  ),

                  Text(' ; 最多'),

                  //Option Max
                  SizedBox(
                    width: 60,
                    child: Focus(
                      child: TextField(
                        textAlign: TextAlign.center,
                        controller: optionMaxEditingController,
                        decoration: InputDecoration(
                          // fillColor: Colors.white,
                          // filled: true,
                          contentPadding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        textInputAction: TextInputAction.done,
                        onChanged: (text) {
                          //Set title? Nope, We'll do it in onFocusChange.
                        },
                      ),
                      onFocusChange: (hasFocus) async {
                        if (!hasFocus) {
                          tryStoreOptionMax();
                        }
                      },
                    ),
                  ),
                  Text(' )'),
                ],
              ),
              value: 1,
              groupValue: widget.additionCategorySetting.option,
              onChanged: (value) {
                setState(() {
                  widget.additionCategorySetting.option = value;
                });
              },
            ),
          ],
        ),
      );
    }

    return mainWidget;
  }

  tryStoreTitle() {
    //Check result to decide if restore to init.
    if (titleEditingController.text.isEmpty) {
      //Display error and restore.
      //不可留空。阻止這個行為
      // DialogGeneral.show(
      //     DialogArgs(
      //       header: DialogGeneral.titleText('提醒'),
      //       contentIcon: DialogContentIcon.Alert,
      //       content: DialogGeneral.centerContentText('名稱不可留空！\n請輸入名稱！'),
      //       mainButtonText: '確認',
      //     ),
      //     barrierDismissible: true);
      titleEditingController.text = widget.additionCategorySetting.title;
    } else {
      widget.additionCategorySetting.title = titleEditingController.text;
    }
  }

  tryStoreOptionMin() {
    //Check result to decide if restore to init.
    if (optionMinEditingController.text.isEmpty) {
      //Display error and restore.
      //不可留空。阻止這個行為
      // DialogGeneral.show(
      //     DialogArgs(
      //       header: DialogGeneral.titleText('提醒'),
      //       contentIcon: DialogContentIcon.Alert,
      //       content: DialogGeneral.centerContentText('數量不可留空！\n請輸入數量！'),
      //       mainButtonText: '確認',
      //     ),
      //     barrierDismissible: true);
      optionMinEditingController.text =
          widget.additionCategorySetting.optionMin.toString();
    } else {
      //這邊要做邏輯限制
      int result = int.parse(optionMinEditingController.text);
      if (result <= widget.additionCategorySetting.optionMax) {
        widget.additionCategorySetting.optionMin = result;
      } else {
        widget.additionCategorySetting.optionMin =
            widget.additionCategorySetting.optionMax;
        optionMinEditingController.text =
            widget.additionCategorySetting.optionMax.toString();
      }
      //通知外面的人 optionMin 變更
      widget.onOptionMinChanged?.call(widget.additionCategorySetting.optionMin);
    }
  }

  tryStoreOptionMax() {
    //Check result to decide if restore to init.
    if (optionMaxEditingController.text.isEmpty) {
      //Display error and restore.
      //不可留空。阻止這個行為
      // DialogGeneral.show(
      //     DialogArgs(
      //       header: DialogGeneral.titleText('提醒'),
      //       contentIcon: DialogContentIcon.Alert,
      //       content: DialogGeneral.centerContentText('數量不可留空！\n請輸入數量！'),
      //       mainButtonText: '確認',
      //     ),
      //     barrierDismissible: true);
      optionMaxEditingController.text =
          widget.additionCategorySetting.optionMax.toString();
    } else {
      //這邊要做邏輯限制
      int result = int.parse(optionMaxEditingController.text);
      if (result >= widget.additionCategorySetting.optionMin) {
        widget.additionCategorySetting.optionMax = result;
      } else {
        widget.additionCategorySetting.optionMax =
            widget.additionCategorySetting.optionMin;
        optionMaxEditingController.text =
            widget.additionCategorySetting.optionMin.toString();
      }
    }
  }
}
