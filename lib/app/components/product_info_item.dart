import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/amount_edit.dart';
import 'package:muyipork/app/components/underline_divider.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

import 'badge.dart';

///
/// 點餐 item
///
class ProductInfoItem extends StatelessWidget {
  final String title;
  final String summary;
  final num price;
  final num vipPrice;
  final num stackPrice;
  final num selectCount;
  final Function onTap;
  final Function onDeleteTap;
  final bool displaySwitch;
  final String switchTitle;
  final bool switchValue;
  final ValueChanged<bool> onSwitchChanged;
  final int editingCount;
  final ValueChanged<num> onEditingCountChanged;
  final Switcher vipOnly;
  final EdgeInsetsGeometry _padding;
  final bool large;
  final Color themeColor;
  final bool vip;
  final bool dragable;
  final bool showSummary;

  ProductInfoItem({
    this.title,
    this.summary,
    this.price,
    this.vipPrice,
    this.stackPrice,
    this.selectCount,
    this.onTap,
    this.onDeleteTap,
    this.displaySwitch = false,
    this.switchTitle = '',
    this.switchValue = true,
    this.onSwitchChanged,
    this.editingCount,
    this.onEditingCountChanged,
    this.vipOnly,
    this.large,
    this.themeColor,
    this.vip,
    this.dragable,
    this.showSummary,
  }) : _padding = EdgeInsets.symmetric(horizontal: 12);

  Widget _vipOnly() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 0,
        horizontal: 2,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.0),
        border: Border.all(
          width: 1.0,
          color: OKColor.Vip,
        ),
      ),
      child: Text(
        '僅限VIP會員商品',
        style: TextStyle(
          fontSize: _large ? 15 : 9,
          color: OKColor.Vip,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[];
    children.addIf(
      dragable ?? false,
      Icon(Icons.drag_handle, color: OKColor.Gray66).paddingOnly(
        left: 12,
      ),
    );
    children.addIf(
        true,
        Expanded(
          child: ListTile(
            contentPadding: _padding,
            onTap: onTap,
            title: _title(),
            subtitle: _subtitle(),
            isThreeLine: false,
            dense: true,
          ),
        ));

    return UnderlineDivider(
      insets: _padding,
      backgroundColor: Colors.transparent,
      child: Row(children: children),
    );
  }

  bool get _large => true == large;

  Widget _title() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Text(
            title ?? '',
            style: TextStyle(
              fontSize: _large ? 23 : 17,
              color: const Color(0xff222222),
            ),
            textAlign: TextAlign.left,
          ),
        ),
        _buttonBar(),
      ],
    );
  }

  Widget _buttonBar() {
    final ls = <Widget>[];
    // badge
    ls.addIf(
      selectCount != null && selectCount > 0,
      Badge(
        fontSize: _large ? 20 : null,
        size: _large ? 28 : null,
        text: '$selectCount',
        backgroundColor: themeColor ?? OKColor.Primary,
        textColor: Colors.white,
      ),
      // Container(
      //   padding: EdgeInsets.symmetric(horizontal: 8),
      //   child: Center(
      //     child: Container(
      //       width: 28,
      //       height: 28,
      //       decoration: BoxDecoration(
      //         color: Colors.red,
      //         borderRadius: BorderRadius.circular(14),
      //       ),
      //       child: Center(
      //         child: Text(
      //           selectCount.toString(),
      //           style: Get.textTheme.bodyText1.copyWith(color: Colors.white),
      //           textAlign: TextAlign.center,
      //         ),
      //       ),
      //     ),
      //   ),
      // ),
    );
    // delete
    ls.addIf(
      onDeleteTap != null,
      Container(
        padding: EdgeInsets.symmetric(horizontal: 8),
        child: Center(
          child: InkWell(
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(18),
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 2,
                      spreadRadius: 2,
                      color: Colors.grey.withOpacity(0.25),
                    )
                  ]),
              child: Icon(Icons.close),
            ),
            onTap: () {
              onDeleteTap?.call();
            },
          ),
        ),
      ),
    );
    // switch
    ls.addIf(
      true == displaySwitch,
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // SizedBox(
          //   width: 8,
          // ),
          // Text(
          //   switchTitle,
          //   style: Get.textTheme.subtitle1.copyWith(color: kColorPrimary),
          // ),
          Text(
            switchTitle ?? '',
            style: TextStyle(
              fontSize: 14,
              color: const Color(0xff6d7278),
            ),
          ),
          SizedBox(
            height: 30,
            child: Switch(
              value: switchValue,
              onChanged: onSwitchChanged,
            ),
          ),
        ],
      ),
    );
    // 數量編輯器
    ls.addIf(
      editingCount != null && onEditingCountChanged != null,
      AmountEdit(
        editingValue: editingCount,
        onChanged: onEditingCountChanged,
      ),
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: ls,
    );
  }

  Widget _price() {
    return Text(
      '\$${(price ?? 0).decimalStyle}' ?? '',
      style: TextStyle(
        fontSize: _large ? 30 : 15,
        color: themeColor ?? OKColor.Primary,
      ),
      textAlign: TextAlign.left,
    );
  }

  Widget _vipPrice() {
    final ls = <Widget>[];
    ls.addIf(
      true,
      Stack(
        alignment: Alignment.centerLeft,
        children: [
          SizedBox(
            width: _large ? 54 : 36,
            height: _large ? 28 : 18,
            child: SvgPicture.asset(
              'assets/images/bg_vip.svg',
              fit: BoxFit.cover,
            ),
          ),
          Container(
            padding: EdgeInsets.only(
              left: 4,
            ),
            child: Text(
              'VIP價',
              style: TextStyle(
                fontSize: _large ? 16 : 10,
                color: Colors.white,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ).paddingOnly(right: 4),
    );
    ls.addIf(
      true,
      Text(
        '\$${(vipPrice ?? 0).decimalStyle}',
        style: TextStyle(
          fontSize: _large ? 30 : 15,
          color: OKColor.Vip,
        ),
        textAlign: TextAlign.left,
      ).paddingOnly(
        right: 4,
      ),
    );
    // ls.addIf(
    //   !vipOnly,
    //   Text(
    //     '(原價:\$${(price ?? 0).displayCurrency})',
    //     style: TextStyle(
    //       fontSize: 12,
    //       color: OKColor.Gray66,
    //     ),
    //     textAlign: TextAlign.left,
    //   ),
    // );
    // ls.addIf(vipOnly, _vipOnly());
    ls.addIf(
      true,
      Visibility(
        visible: Switcher.On == vipOnly,
        child: _vipOnly(),
        replacement: Text(
          '(原價:\$${(price ?? 0).decimalStyle})',
          style: TextStyle(
            fontSize: _large ? 20 : 12,
            color: OKColor.Gray66,
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: ls,
    );
  }

  Widget _subtitle() {
    final ls = <Widget>[];
    ls.addIf(
      // summary != null && summary.isNotEmpty,
      // Alan: POS端，產品說明都不用出現，會比較好
      true == showSummary,
      Text(
        summary ?? '',
        style: TextStyle(
          fontSize: _large ? 17 : 15,
          color: const Color(0xff6d7278),
        ),
        textAlign: TextAlign.left,
      ).paddingSymmetric(vertical: 4),
    );
    ls.addIf(true, _thirdLine().paddingOnly(bottom: 4));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: ls,
    );
  }

  Widget _thirdLine() {
    final ls = <Widget>[];
    final containsVip = true == vip && vipPrice != price;
    ls.addIf(
      true != containsVip,
      _price(),
    );
    ls.addIf(
      true == containsVip,
      _vipPrice(),
    );
    ls.addIf(
      true,
      Spacer(),
    );
    ls.addIf(
      stackPrice != null,
      _subtotal(),
    );
    return Row(
      children: ls,
    );
  }

  Widget _subtotal() {
    return Text(
      '小計 ${(stackPrice ?? 0).currencyStyle}',
      style: TextStyle(
        fontSize: _large ? 30 : 15,
        color: themeColor ?? OKColor.Primary,
      ),
    );
  }
}
