import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class QrCodeScanner extends StatefulWidget {
  const QrCodeScanner({
    Key key,
  }) : super(key: key);

  @override
  _QrCodeScannerState createState() => _QrCodeScannerState();
}

class _QrCodeScannerState extends State<QrCodeScanner>
    with WidgetsBindingObserver {
  final GlobalKey _qrKey = GlobalKey(debugLabel: 'QR');
  final _flashStatus = false.obs;
  final _flashVisiable = false.obs;
  final _facingVisiable = false.obs;
  final _characters = <String>[].obs;
  final _textNode = FocusNode();
  final _disposable = Completer();
  QRViewController _controller;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _characters.stream
        .timeout(
          800.milliseconds,
          onTimeout: (sink) {
            final jsonString = _characters.join();
            if (jsonString != null && jsonString.isNotEmpty) {
              Get.back<String>(result: jsonString);
            }
          },
        )
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _disposable.complete();
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        onResumed();
        break;
      case AppLifecycleState.inactive:
        onInactive();
        break;
      case AppLifecycleState.paused:
        onPaused();
        break;
      case AppLifecycleState.detached:
        onDetached();
        break;
    }
  }

  void onInactive() {}

  void onResumed() {
    _controller?.resumeCamera();
  }

  void onPaused() {
    _controller?.pauseCamera();
  }

  void onDetached() {}

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      _controller?.pauseCamera();
    } else if (Platform.isIOS) {
      _controller?.resumeCamera();
    }
  }

  @override
  void deactivate() {
    _controller?.stopCamera();
    super.deactivate();
  }

  List<Widget> _actions() {
    final ls = <Widget>[];
    ls.addIf(true, Obx(() {
      return Visibility(
        visible: _flashVisiable.value,
        child: IconButton(
          icon: Icon(
            _flashStatus.value ? Icons.flash_on : Icons.flash_off,
            color: Colors.white,
          ),
          onPressed: _onFlashClicked,
        ),
      );
    }));
    ls.addIf(true, Obx(() {
      return Visibility(
        visible: _facingVisiable.value,
        child: IconButton(
          icon: Icon(
            Icons.flip_camera_android,
            color: Colors.white,
          ),
          onPressed: _onFlipClicked,
        ),
      );
    }));
    return ls;
  }

  void handleKey(RawKeyEvent key) {
    if (key.character != null && key.character.isNotEmpty) {
      _characters.add(key.character);
    }
  }

  @override
  Widget build(BuildContext context) {
    FocusScope.of(context).requestFocus(_textNode);
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0.0,
        automaticallyImplyLeading: false,
        actions: _actions(),
      ),
      body: RawKeyboardListener(
        focusNode: _textNode,
        onKey: handleKey,
        child: Stack(
          children: [
            QRView(
              overlay: QrScannerOverlayShape(
                borderColor: Colors.white,
                borderRadius: 20,
                borderWidth: 20,
                borderLength: 40,
                cutOutSize: 280,
              ),
              key: _qrKey,
              onQRViewCreated: _onQRViewCreated,
              formatsAllowed: [
                BarcodeFormat.code39,
                BarcodeFormat.code93,
                BarcodeFormat.code128,
                BarcodeFormat.ean8,
                BarcodeFormat.ean13,
                BarcodeFormat.itf,
                BarcodeFormat.upcA,
                BarcodeFormat.upcE,
                BarcodeFormat.qrcode,
              ],
            ),
            Container(
              alignment: Alignment.bottomCenter,
              padding: EdgeInsets.only(
                bottom: kBottomPadding,
              ),
              child: IconButton(
                iconSize: 40,
                onPressed: () => Get.back(),
                icon: Icon(
                  Icons.cancel,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onFlashClicked() {
    _controller.toggleFlash().then(
      (value) {
        return _controller?.getFlashStatus();
      },
    ).then(
      (value) {
        _flashStatus.value = value;
      },
    );
  }

  void _onFlipClicked() {
    _controller.flipCamera().then(
      (value) {
        switch (value) {
          case CameraFacing.back:
            break;
          case CameraFacing.front:
            break;
          case CameraFacing.unknown:
            break;
          default:
        }
      },
    );
    // controller.getCameraInfo().then(
    //   (value) {
    //     switch (value) {
    //       case CameraFacing.back:
    //         break;
    //       case CameraFacing.front:
    //         break;
    //       case CameraFacing.unknown:
    //         break;
    //       default:
    //     }
    //   },
    // );
  }

  void _onQRViewCreated(QRViewController controller) {
    this._controller = controller;
    controller.getSystemFeatures().then(
      (value) {
        _flashVisiable.value = value.hasFlash;
        _facingVisiable.value = value.hasFrontCamera && value.hasBackCamera;
      },
    );
    controller.scannedDataStream.take(1).takeUntil(_disposable.future).listen(
      (scanData) {
        // Get.back<String>(result: scanData.code);
        _characters.add(scanData.code);
      },
    );
  }
}
