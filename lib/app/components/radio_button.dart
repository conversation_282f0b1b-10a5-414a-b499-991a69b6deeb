import 'package:flutter/material.dart';
import 'package:muyipork/colors.dart';

class RadioButton<T> extends StatelessWidget {
  final String titleText;
  final Widget child;
  final ValueChanged<T> onChanged;
  // 設定值
  final T value;
  // 群組值
  final T groupValue;

  const RadioButton({
    Key key,
    this.value,
    this.groupValue,
    this.titleText,
    this.child,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // V3
    return TextButton.icon(
      style: ButtonStyle(
        foregroundColor: MaterialStateProperty.all(OKColor.Gray33),
      ),
      // style: TextButton.styleFrom(
      //   textStyle: TextStyle(
      //     fontSize: 15,
      //     color: OKColor.Gray33,
      //   ),
      // ),
      onPressed: () {
        onChanged?.call(value);
      },
      icon: SizedBox.fromSize(
        size: Size.square(12),
        child: Radio(
          value: value,
          groupValue: groupValue,
          onChanged: onChanged,
        ),
      ),
      label: child ??
          Text(
            titleText ?? '',
            style: TextStyle(
              fontSize: 15,
              color: OKColor.Gray33,
            ),
            textAlign: TextAlign.left,
            softWrap: false,
          ),
    );
    // V2
    // return Row(
    //   mainAxisSize: MainAxisSize.min,
    //   children: [
    //     SizedBox.fromSize(
    //       size: Size.square(12),
    //       child: Radio(
    //         value: value,
    //         groupValue: groupValue,
    //         onChanged: onChanged,
    //       ),
    //     ),
    //     child ??
    //         TextButton(
    //           onPressed: () {
    //             onChanged?.call(value);
    //           },
    //           child: Text(
    //             titleText ?? '',
    //             style: TextStyle(
    //               fontSize: 15,
    //               color: OKColor.Gray33,
    //             ),
    //             textAlign: TextAlign.left,
    //             softWrap: false,
    //           ),
    //         ),
    //   ],
    // );
    // V1
    // return RadioListTile<T>(
    //   contentPadding: EdgeInsets.zero,
    //   title: Transform.translate(
    //     offset: Offset(-20, 0),
    //     child: child ??
    //         Text(
    //           titleText ?? '',
    //           style: TextStyle(
    //             fontSize: 15,
    //             color: OKColor.Gray33,
    //           ),
    //           textAlign: TextAlign.left,
    //           softWrap: false,
    //         ),
    //   ),
    //   value: value,
    //   groupValue: groupValue,
    //   onChanged: onChanged,
    // );
  }
}
