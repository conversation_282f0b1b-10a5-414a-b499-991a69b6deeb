///
/// 操場形狀的按鈕，固定高度及最大寬度
///
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

class RoundedButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String buttonText;
  final Widget child;
  final Color backgroundColor;

  const RoundedButton({
    Key key,
    this.onPressed,
    this.buttonText,
    this.child,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      // [ref](https://blog.csdn.net/chenlove1/article/details/83627831)
      decoration: ShapeDecoration(
        // decoration: BoxDecoration(
        // borderRadius: BorderRadius.circular(kButtonHeight * 0.5),
        color: backgroundColor,
        shape: StadiumBorder(),
        gradient: backgroundColor != null
            ? null
            : LinearGradient(
                begin: Alignment(-1.0, 1.0),
                end: Alignment(1.0, 1.0),
                colors: [
                  OKColor.Primary,
                  OKColor.PrimaryWeight,
                ],
                stops: [0.0, 1.0],
              ),
      ),
      child: TextButton(
        onPressed: this.onPressed,
        style: TextButton.styleFrom(
          shape: StadiumBorder(),
          textStyle: Get.textTheme.subtitle1,
          minimumSize: Size(double.infinity, kButtonHeight),
          padding: EdgeInsets.zero,
        ),
        child: child ??
            Text(
              buttonText ?? '',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
      ),
    );
  }
}
