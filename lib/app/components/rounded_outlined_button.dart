import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RoundedOutlinedButton extends StatelessWidget {
  RoundedOutlinedButton({this.text, this.color = Colors.white, this.padding = const EdgeInsets.symmetric(vertical: 4, horizontal: 24), this.onPressed});

  final String text;
  final EdgeInsets padding;
  final Color color;
  final Function onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
        customBorder: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        child: Container(
          padding: padding,
          decoration: BoxDecoration(
            border: Border.all(width: 1, color: color),
            borderRadius: BorderRadius.circular(30),
          ),
          child: Center(
            child: Text(
              text,
              style: Get.textTheme.subtitle1.copyWith(color: color),
            ),
          ),
        ),
        onTap: onPressed,
    );
  }
}
