import 'package:flutter/material.dart';

class RoundedShadowContainer extends StatelessWidget {
  RoundedShadowContainer({this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.15),
              offset: Offset(0, 3),
              blurRadius: 1,
              spreadRadius: 1,
            ),
          ]),
      child: child,
    );
  }
}
