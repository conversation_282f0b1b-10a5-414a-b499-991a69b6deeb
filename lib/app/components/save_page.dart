import 'package:flutter/material.dart';

import 'bottom_button.dart';

///
/// TODO: remove me, use BottomWidgetPage.save instead
///
class SavePage extends StatelessWidget {
  final Function onPressed;
  final Widget child;
  final String buttonText;

  const SavePage({
    Key key,
    this.onPressed,
    this.child,
    this.buttonText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox.expand(
          child: this.child ?? SizedBox.shrink(),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: BottomButton(
            buttonText: buttonText ?? '儲存',
            onPressed: this.onPressed,
          ),
        ),
      ],
    );
  }
}
