import 'package:flutter/material.dart';
import 'package:muyipork/constants.dart';

class SquareToggle<T> extends StatelessWidget {
  SquareToggle({
    this.value,
    this.text,
    @required this.checked,
    this.onPressed,
    this.backgroundColor = kColorPrimary,
    this.style,
  });

  final T value;
  final String text;
  final bool checked;
  final ValueChanged<T> onPressed;
  final Color backgroundColor;
  final ButtonStyle style;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: style ??
          TextButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: 4),
            backgroundColor: checked ? backgroundColor : Colors.white,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(2.0)),
            side: BorderSide(color: Color(0xffe2e2e2)),
            minimumSize: Size(0, kButtonHeight),
          ),
      onPressed: () {
        onPressed?.call(value);
      },
      child: Text(
        text ?? '',
        style: TextStyle(
          fontSize: 15,
          color: checked ? Colors.white : Colors.black,
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        softWrap: false,
        maxLines: 2,
      ),
    );
  }
}
