///
/// 操場形狀的按鈕，會緊縮
///
import 'package:flutter/material.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';

class StadiumButton extends StatelessWidget {
  final Function onPressed;
  final String buttonText;
  final Color backgroundColor;
  final Color textColor;
  final EdgeInsetsGeometry padding;
  final Widget child;

  const StadiumButton({
    Key key,
    this.child,
    this.onPressed,
    this.padding,
    this.buttonText,
    this.textColor,
    this.backgroundColor,
  }) : super(key: key);

  const StadiumButton.chip({
    Key key,
    this.child,
    this.onPressed,
    this.buttonText,
    this.textColor,
    this.backgroundColor,
  }) : padding = kChipPadding;

  factory StadiumButton.chipV2({
    Key key,
    final child,
    final onPressed,
    final buttonText,
    final textColor,
    final backgroundColor,
  }) {
    return StadiumButton(
      child: child,
      onPressed: onPressed,
      buttonText: buttonText,
      textColor: textColor,
      backgroundColor: backgroundColor,
      padding: kChipPadding,
    );
  }

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      child: child ??
          Text(
            buttonText ?? '',
            style: TextStyle(
              fontSize: 16,
              color: textColor ?? Colors.white,
            ),
            textAlign: TextAlign.left,
          ),
      style: TextButton.styleFrom(
        shape: const StadiumBorder(),
        minimumSize: Size.zero,
        backgroundColor: backgroundColor ?? OKColor.Primary,
        padding: padding ??
            EdgeInsets.symmetric(
              vertical: 4,
              horizontal: 24,
            ),
      ),
    );
  }
}
