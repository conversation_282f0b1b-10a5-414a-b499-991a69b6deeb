import 'package:flutter/material.dart';
import 'package:muyipork/constants.dart';

import 'bg_default.dart';
import 'general_appbar.dart';

class StandardPage extends StatelessWidget {
  final String titleText;
  final Widget child;
  final Widget title;
  final Widget leading;
  final PreferredSizeWidget appBar;
  final List<Widget> actions;
  final Color backgroundColor;
  final VoidCallback onBackPress;
  // final Widget bottomNavigationBar;

  const StandardPage({
    Key key,
    this.titleText,
    this.child,
    this.title,
    this.leading,
    this.appBar,
    this.actions,
    this.backgroundColor,
    this.onBackPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BgDefault(
      mainColor: backgroundColor,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: backgroundColor ?? Colors.transparent,
        appBar: appBar ??
            GeneralAppbar(
              onBackPress: onBackPress,
              leading: leading,
              title: title ??
                  Text(
                    titleText ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
              actions: actions ?? <Widget>[],
            ),
        body: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: kRadiusCircular),
          child: ColoredBox(
            color: const Color(0xffeeeef3),
            child: SizedBox.expand(
              child: child ?? const SizedBox(),
            ),
          ),
        ),
      ),
    );
  }
}
