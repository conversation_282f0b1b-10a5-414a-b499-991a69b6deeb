import 'package:flutter/material.dart';
import 'package:get/get.dart';

class StreamObx<T> extends StatelessWidget {
  final Stream stream;
  final NotifierBuilder<T> onData;
  final Widget Function(Object error) onError;
  final Widget onLoading;
  // final Widget onEmpty;

  const StreamObx(
    this.stream,
    this.onData, {
    Key key,
    this.onError,
    this.onLoading,
    // this.onEmpty,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: this.stream,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return onError != null
              ? onError(snapshot.error)
              : Center(child: Text('An error occurred: ${snapshot.error}'));
        }
        if (snapshot.hasData) {
          return onData(snapshot.data);
        }
        return onLoading ?? const Center(child: CircularProgressIndicator());
      },
    );
  }
}
