import 'package:flutter/material.dart';

// TODO: remove me
class TopRoundClip extends StatelessWidget {
  TopRoundClip({this.backgroundColor, @required this.child});

  final Color backgroundColor;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      child: Container(
        color: backgroundColor,
        child: SizedBox.expand(
          child: child ?? SizedBox.shrink(),
        ),
      ),
    );
  }
}
