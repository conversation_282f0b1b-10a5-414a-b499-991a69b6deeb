import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';

class UnderlineDivider extends StatelessWidget {
  final Widget child;
  final Color color;
  final Color backgroundColor;
  final EdgeInsetsGeometry insets;

  const UnderlineDivider({
    Key key,
    this.child,
    this.color,
    this.insets,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: backgroundColor ?? Colors.white,
      child: DecoratedBox(
        // [ref]: https://blog.csdn.net/chenlove1/article/details/83627831
        // way0:
        // decoration: BoxDecoration(
        //   border: Border(
        //     bottom: BorderSide(
        //       width: 1.0,
        //       color: color ?? Get.theme.dividerColor,
        //     ),
        //   ),
        // ),
        // decoration: ShapeDecoration(
        //   color: backgroundColor,
        //   // way1:
        //   shape: UnderlineInputBorder(
        //     borderSide: BorderSide(
        //       color: color ?? Get.theme.dividerColor,
        //     ),
        //   ),
        //   // way2:
        //   // shape: Border(
        //   //   bottom: BorderSide(
        //   //     color: color ?? Get.theme.dividerColor,
        //   //   ),
        //   // ),
        // ),
        // way3:
        decoration: UnderlineTabIndicator(
          borderSide: BorderSide(
            color: color ?? Get.theme.dividerColor,
          ),
          // insets: insets ?? EdgeInsets.zero,
          insets: insets ?? kContentPadding,
        ),
        child: child ?? const SizedBox.shrink(),
      ),
    );
  }
}
