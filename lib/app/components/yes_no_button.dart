import 'package:flutter/material.dart';
import 'package:muyipork/constants.dart';

class YesNoButtonArgs {
  final String buttonText;
  final Color backgroundColor;
  final Function onPressed;

  YesNoButtonArgs({
    this.buttonText,
    this.backgroundColor,
    this.onPressed,
  });
}

class YesNoButton extends StatelessWidget {
  final Function onLeftPressed;
  final Function onMidPressed;
  final Function onRightPressed;
  final String leftButtonText;
  final String midButtonText;
  final String rightButtonText;
  final Color leftColor;
  final Color midColor;
  final Color rightColor;

  const YesNoButton({
    Key key,
    this.onLeftPressed,
    this.onMidPressed,
    this.onRightPressed,
    this.leftButtonText,
    this.midButtonText,
    this.rightButtonText,
    this.leftColor,
    this.midColor,
    this.rightColor,
  }) : super(key: key);

  YesNoButton.withArgs({
    YesNoButtonArgs left,
    YesNoButtonArgs mid,
    YesNoButtonArgs right,
  })  : leftButtonText = left?.buttonText,
        leftColor = left?.backgroundColor,
        onLeftPressed = left?.onPressed,
        midButtonText = mid?.buttonText,
        midColor = mid?.backgroundColor,
        onMidPressed = mid?.onPressed,
        rightButtonText = right?.buttonText,
        rightColor = right?.backgroundColor,
        onRightPressed = right?.onPressed;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: kButtonHeight,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: _children(),
      ),
    );
  }

  List<Widget> _children() {
    final children = <Widget>[];
    children.add(
      Expanded(
        child: TextButton(
          onPressed: onLeftPressed,
          style: TextButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.horizontal(
                left: kRadius20,
              ),
            ),
            minimumSize: Size(double.infinity, kButtonHeight),
            backgroundColor: leftColor ?? Color(0xff3e4b5a),
          ),
          child: Text(
            leftButtonText ?? '取消',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    if (midButtonText != null) {
      children.add(
        Expanded(
          child: TextButton(
            onPressed: onMidPressed,
            style: TextButton.styleFrom(
              shape: RoundedRectangleBorder(),
              minimumSize: Size(double.infinity, kButtonHeight),
              backgroundColor: midColor ?? Colors.grey,
            ),
            child: Text(
              midButtonText ?? '保留',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }
    children.add(
      Expanded(
        child: TextButton(
          onPressed: onRightPressed,
          style: TextButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.horizontal(
                right: kRadius20,
              ),
            ),
            minimumSize: Size(double.infinity, kButtonHeight),
            backgroundColor: rightColor ?? kColorPrimary,
          ),
          child: Text(
            rightButtonText ?? '確認',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    return children;
  }
}
