// To parse this JSON data, do
//
//     final brandsBannersPut = brandsBannersPutFromJson(jsonString);

import 'dart:convert';

class BrandsBannersPut {
  BrandsBannersPut({
    this.images,
  });

  List<Image> images;

  BrandsBannersPut copyWith({
    List<Image> images,
  }) =>
      BrandsBannersPut(
        images: images ?? this.images,
      );

  factory BrandsBannersPut.fromRawJson(String str) =>
      BrandsBannersPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsBannersPut.fromJson(Map<String, dynamic> json) =>
      BrandsBannersPut(
        images: json["images"] == null
            ? null
            : List<Image>.from(json["images"].map((x) => Image.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "images": images == null ? null : jsonEncode(images),
      };
}

// TODO: make single file: image.dart
class Image {
  Image({
    this.imageId,
    this.imageUrl,
    this.sort,
  });

  num imageId;
  String imageUrl;
  num sort;

  Image copyWith({
    num imageId,
    String imageUrl,
    num sort,
  }) =>
      Image(
        imageId: imageId ?? this.imageId,
        imageUrl: imageUrl ?? this.imageUrl,
        sort: sort ?? this.sort,
      );

  factory Image.fromRawJson(String str) => Image.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Image.fromJson(Map<String, dynamic> json) => Image(
        imageId: json["image_id"] == null ? null : json["image_id"],
        imageUrl: json["image_url"] == null ? null : json["image_url"],
        sort: json["sort"] == null ? null : json["sort"],
      );

  Map<String, dynamic> toJson() => {
        "image_id": imageId == null ? null : imageId,
        // "image_url": imageUrl == null ? null : imageUrl,
        "sort": sort == null ? null : sort,
      };
}
