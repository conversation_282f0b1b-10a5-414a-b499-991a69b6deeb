// To parse this JSON data, do
//
//     final brandsInvoice = brandsInvoiceFromJson(jsonString);

import 'dart:convert';

class BrandsInvoice {
  BrandsInvoice({
    this.taxId,
    this.taxType,
    this.guiException,
    this.status,
  });

  String taxId;
  num taxType;
  num guiException;
  num status;

  BrandsInvoice copyWith({
    String taxId,
    num taxType,
    num guiException,
    num status,
  }) =>
      BrandsInvoice(
        taxId: taxId ?? this.taxId,
        taxType: taxType ?? this.taxType,
        guiException: guiException ?? this.guiException,
        status: status ?? this.status,
      );

  factory BrandsInvoice.fromRawJson(String str) =>
      BrandsInvoice.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsInvoice.fromJson(Map<String, dynamic> json) => BrandsInvoice(
        taxId: json["tax_id"] == null ? null : json["tax_id"],
        taxType: json["tax_type"] == null ? null : json["tax_type"],
        guiException:
            json["gui_exception"] == null ? null : json["gui_exception"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "tax_id": taxId == null ? null : taxId,
        "tax_type": taxType == null ? null : taxType,
        "gui_exception": guiException == null ? null : guiException,
        "status": status == null ? null : status,
      };
}
