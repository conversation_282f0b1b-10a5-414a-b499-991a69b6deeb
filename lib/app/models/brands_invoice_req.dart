// To parse this JSON data, do
//
//     final brandsInvoiceReq = brandsInvoiceReqFromJson(jsonString);

import 'dart:convert';

class BrandsInvoiceReq {
  BrandsInvoiceReq({
    this.taxType,
    this.guiException,
    this.status,
  });

  num taxType;
  num guiException;
  num status;

  BrandsInvoiceReq copyWith({
    num taxType,
    num guiException,
    num status,
  }) =>
      BrandsInvoiceReq(
        taxType: taxType ?? this.taxType,
        guiException: guiException ?? this.guiException,
        status: status ?? this.status,
      );

  factory BrandsInvoiceReq.fromRawJson(String str) =>
      BrandsInvoiceReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsInvoiceReq.fromJson(Map<String, dynamic> json) =>
      BrandsInvoiceReq(
        taxType: json["tax_type"] == null ? null : json["tax_type"],
        guiException:
            json["gui_exception"] == null ? null : json["gui_exception"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "tax_type": taxType == null ? null : taxType,
        "gui_exception": guiException == null ? null : guiException,
        "status": status == null ? null : status,
      };
}
