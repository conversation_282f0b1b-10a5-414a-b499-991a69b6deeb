// To parse this JSON data, do
//
//     final brandsNewsPut = brandsNewsPutFromJson(jsonString);

import 'dart:convert';

class BrandsNewsPut {
  BrandsNewsPut({
    this.news,
  });

  String news;

  BrandsNewsPut copyWith({
    String news,
  }) =>
      BrandsNewsPut(
        news: news ?? this.news,
      );

  factory BrandsNewsPut.fromRawJson(String str) =>
      BrandsNewsPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsNewsPut.fromJson(Map<String, dynamic> json) => BrandsNewsPut(
        news: json["news"] == null ? null : json["news"],
      );

  Map<String, dynamic> toJson() => {
        "news": news == null ? null : news,
      };
}
