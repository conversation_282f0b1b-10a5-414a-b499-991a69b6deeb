// To parse this JSON data, do
//
//     final brandsPut = brandsPutFromJson(jsonString);

import 'dart:convert';

class BrandsPut {
  BrandsPut({
    this.type,
    this.name,
    this.taxId,
    this.lineName,
    this.lineId,
    this.lineChannelId,
    this.lineSecretCode,
    this.lineAccessToken,
    this.lineLiffId,
  });

  num type;
  String name;
  String taxId;
  String lineName;
  String lineId;
  String lineChannelId;
  String lineSecretCode;
  String lineAccessToken;
  String lineLiffId;

  BrandsPut copyWith({
    num type,
    String name,
    String taxId,
    String lineName,
    String lineId,
    String lineChannelId,
    String lineSecretCode,
    String lineAccessToken,
    String lineLiffId,
  }) =>
      BrandsPut(
        type: type ?? this.type,
        name: name ?? this.name,
        taxId: taxId ?? this.taxId,
        lineName: lineName ?? this.lineName,
        lineId: lineId ?? this.lineId,
        lineChannelId: lineChannelId ?? this.lineChannelId,
        lineSecretCode: lineSecretCode ?? this.lineSecretCode,
        lineAccessToken: lineAccessToken ?? this.lineAccessToken,
        lineLiffId: lineLiffId ?? this.lineLiffId,
      );

  factory BrandsPut.fromRawJson(String str) =>
      BrandsPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsPut.fromJson(Map<String, dynamic> json) => BrandsPut(
        type: json["type"] == null ? null : json["type"],
        name: json["name"] == null ? null : json["name"],
        taxId: json["tax_id"] == null ? null : json["tax_id"],
        lineName: json["line_name"] == null ? null : json["line_name"],
        lineId: json["line_id"] == null ? null : json["line_id"],
        lineChannelId:
            json["line_channel_id"] == null ? null : json["line_channel_id"],
        lineSecretCode:
            json["line_secret_code"] == null ? null : json["line_secret_code"],
        lineAccessToken: json["line_access_token"] == null
            ? null
            : json["line_access_token"],
        lineLiffId: json["line_liff_id"] == null ? null : json["line_liff_id"],
      );

  Map<String, dynamic> toJson() => {
        "type": type == null ? null : type,
        "name": name == null ? null : name,
        "tax_id": taxId == null ? null : taxId,
        "line_name": lineName == null ? null : lineName,
        "line_id": lineId == null ? null : lineId,
        "line_channel_id": lineChannelId == null ? null : lineChannelId,
        "line_secret_code": lineSecretCode == null ? null : lineSecretCode,
        "line_access_token": lineAccessToken == null ? null : lineAccessToken,
        "line_liff_id": lineLiffId == null ? null : lineLiffId,
      };
}
