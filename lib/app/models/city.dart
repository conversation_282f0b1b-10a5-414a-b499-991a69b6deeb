// To parse this JSON data, do
//
//     final city = cityFromJson(jsonString);

import 'dart:convert';

class City {
  City({
    this.createdAt,
    this.id,
    this.name,
    this.updatedAt,
  });

  String createdAt;
  num id;
  String name;
  String updatedAt;

  City copyWith({
    String createdAt,
    num id,
    String name,
    String updatedAt,
  }) =>
      City(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        name: name ?? this.name,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory City.fromRawJson(String str) => City.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory City.fromJson(Map<String, dynamic> json) => City(
        createdAt: json["created_at"] == null ? null : json["created_at"],
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
        updatedAt: json["updated_at"] == null ? null : json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "created_at": createdAt == null ? null : createdAt,
        "id": id == null ? null : id,
        "name": name == null ? null : name,
        "updated_at": updatedAt == null ? null : updatedAt,
      };
}
