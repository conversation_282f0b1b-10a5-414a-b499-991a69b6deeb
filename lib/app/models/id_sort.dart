// To parse this JSON data, do
//
//     final idSort = idSortFromJson(jsonString);

import 'dart:convert';

class IDSort {
  IDSort({
    this.id,
    this.sort,
  });

  num id;
  num sort;

  IDSort copyWith({
    num id,
    num sort,
  }) =>
      IDSort(
        id: id ?? this.id,
        sort: sort ?? this.sort,
      );

  factory IDSort.fromRawJson(String str) => IDSort.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory IDSort.fromJson(Map<String, dynamic> json) => IDSort(
        id: json["id"] == null ? null : json["id"],
        sort: json["sort"] == null ? null : json["sort"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "sort": sort == null ? null : sort,
      };
}
