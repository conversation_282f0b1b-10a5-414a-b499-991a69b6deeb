import 'dart:convert';

class InvoiceData {
    String seller;
    int date;
    String number;

    InvoiceData({
        this.seller,
        this.date,
        this.number,
    });

    InvoiceData copyWith({
        String seller,
        int date,
        String number,
    }) => 
        InvoiceData(
            seller: seller ?? this.seller,
            date: date ?? this.date,
            number: number ?? this.number,
        );

    factory InvoiceData.fromRawJson(String str) => InvoiceData.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory InvoiceData.fromJson(Map<String, dynamic> json) => InvoiceData(
        seller: json["seller"],
        date: json["date"],
        number: json["number"],
    );

    Map<String, dynamic> toJson() => {
        "seller": seller,
        "date": date,
        "number": number,
    };
}
