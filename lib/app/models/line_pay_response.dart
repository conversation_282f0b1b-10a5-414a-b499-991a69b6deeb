// To parse this JSON data, do
//
//     final linePayResponse = linePayResponseFromJson(jsonString);

import 'dart:convert';

class LinePayResponse {
  LinePayResponse({
    this.returnCode,
    this.returnMessage,
  });

  String returnCode;
  String returnMessage;

  LinePayResponse copyWith({
    String returnCode,
    String returnMessage,
  }) =>
      LinePayResponse(
        returnCode: returnCode ?? this.returnCode,
        returnMessage: returnMessage ?? this.returnMessage,
      );

  factory LinePayResponse.fromRawJson(String str) =>
      LinePayResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LinePayResponse.fromJson(Map<String, dynamic> json) =>
      LinePayResponse(
        returnCode: json["returnCode"] == null ? null : json["returnCode"],
        returnMessage:
            json["returnMessage"] == null ? null : json["returnMessage"],
      );

  Map<String, dynamic> toJson() => {
        "returnCode": returnCode == null ? null : returnCode,
        "returnMessage": returnMessage == null ? null : returnMessage,
      };
}
