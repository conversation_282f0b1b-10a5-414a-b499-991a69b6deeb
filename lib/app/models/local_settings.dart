// To parse this JSON data, do
//
//     final localSettings = localSettingsFromJson(jsonString);

import 'dart:convert';

class LocalSettings {
  num printInvoice;
  num printReceipt;
  num printReceiptCount;
  num printItemReceipt;
  num printItemReceiptCount;
  num autoPrintReceiptEnabled;
  num autoPrintReceipt;
  num autoPrintSticker;
  List<AutoPrint> autoPrint;

  LocalSettings({
    this.printInvoice,
    this.printReceipt,
    this.printReceiptCount,
    this.printItemReceipt,
    this.printItemReceiptCount,
    this.autoPrintReceiptEnabled,
    this.autoPrintReceipt,
    this.autoPrintSticker,
    this.autoPrint,
  });

  LocalSettings copyWith({
    num printInvoice,
    num printReceipt,
    num printReceiptCount,
    num printItemReceipt,
    num printItemReceiptCount,
    num autoPrintReceiptEnabled,
    num autoPrintReceipt,
    num autoPrintSticker,
    List<AutoPrint> autoPrint,
  }) =>
      LocalSettings(
        printInvoice: printInvoice ?? this.printInvoice,
        printReceipt: printReceipt ?? this.printReceipt,
        printReceiptCount: printReceiptCount ?? this.printReceiptCount,
        printItemReceipt: printItemReceipt ?? this.printItemReceipt,
        printItemReceiptCount:
            printItemReceiptCount ?? this.printItemReceiptCount,
        autoPrintReceiptEnabled:
            autoPrintReceiptEnabled ?? this.autoPrintReceiptEnabled,
        autoPrintReceipt: autoPrintReceipt ?? this.autoPrintReceipt,
        autoPrintSticker: autoPrintSticker ?? this.autoPrintSticker,
        autoPrint: autoPrint ?? this.autoPrint,
      );

  factory LocalSettings.fromRawJson(String str) =>
      LocalSettings.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LocalSettings.fromJson(Map<String, dynamic> json) => LocalSettings(
        printInvoice: json["print_invoice"],
        printReceipt: json["print_receipt"],
        printReceiptCount: json["print_receipt_count"],
        printItemReceipt: json["print_item_receipt"],
        printItemReceiptCount: json["print_item_receipt_count"],
        autoPrintReceiptEnabled: json["auto_print_receipt_enabled"],
        autoPrintReceipt: json["auto_print_receipt"],
        autoPrintSticker: json["auto_print_sticker"],
        autoPrint: List<AutoPrint>.from(
            (json["auto_print"] ?? []).map((x) => AutoPrint.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "print_invoice": printInvoice,
        "print_receipt": printReceipt,
        "print_receipt_count": printReceiptCount,
        "print_item_receipt": printItemReceipt,
        "print_item_receipt_count": printItemReceiptCount,
        "auto_print_receipt_enabled": autoPrintReceiptEnabled,
        "auto_print_receipt": autoPrintReceipt,
        "auto_print_sticker": autoPrintSticker,
        "auto_print":
            List<dynamic>.from((autoPrint ?? []).map((x) => x.toJson())),
      };
}

class AutoPrint {
  num type;
  num enabled;
  num receipt;
  num sticker;

  AutoPrint({
    this.type,
    this.enabled,
    this.receipt,
    this.sticker,
  });

  AutoPrint copyWith({
    num type,
    num enabled,
    num receipt,
    num sticker,
  }) =>
      AutoPrint(
        type: type ?? this.type,
        enabled: enabled ?? this.enabled,
        receipt: receipt ?? this.receipt,
        sticker: sticker ?? this.sticker,
      );

  factory AutoPrint.fromRawJson(String str) =>
      AutoPrint.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AutoPrint.fromJson(Map<String, dynamic> json) => AutoPrint(
        type: json["type"],
        enabled: json["enabled"],
        receipt: json["receipt"],
        sticker: json["sticker"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "enabled": enabled,
        "receipt": receipt,
        "sticker": sticker,
      };
}

extension AutoPrintX on AutoPrint {
  bool get receiptEnabled => enabled == 1 && receipt == 1;
  bool get stickerEnabled => enabled == 1 && sticker == 1;
}