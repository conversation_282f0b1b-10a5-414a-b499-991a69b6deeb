// To parse this JSON data, do
//
//     final loginReq = loginReqFromJson(jsonString);

import 'dart:convert';

class LoginReq {
  LoginReq({
    this.code,
    this.username,
    this.password,
  });

  String code;
  String username;
  String password;

  LoginReq copyWith({
    String code,
    String username,
    String password,
  }) =>
      LoginReq(
        code: code ?? this.code,
        username: username ?? this.username,
        password: password ?? this.password,
      );

  factory LoginReq.fromRaw<PERSON>son(String str) =>
      LoginReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginReq.fromJson(Map<String, dynamic> json) => LoginReq(
        code: json["code"] == null ? null : json["code"],
        username: json["username"] == null ? null : json["username"],
        password: json["password"] == null ? null : json["password"],
      );

  Map<String, dynamic> toJson() => {
        "code": code == null ? null : code,
        "username": username == null ? null : username,
        "password": password == null ? null : password,
      };
}
