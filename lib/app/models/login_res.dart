// To parse this JSON data, do
//
//     final loginRes = loginResFromJson(jsonString);

import 'dart:convert';

class LoginRes {
  LoginRes({
    this.token,
    this.alreadyLogin,
  });

  String token;
  bool alreadyLogin;

  LoginRes copyWith({
    String token,
    bool alreadyLogin,
  }) =>
      LoginRes(
        token: token ?? this.token,
        alreadyLogin: alreadyLogin ?? this.alreadyLogin,
      );

  factory LoginRes.fromRawJson(String str) =>
      LoginRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginRes.fromJson(Map<String, dynamic> json) => LoginRes(
        token: json["token"] == null ? null : json["token"],
        alreadyLogin:
            json["already_login"] == null ? null : json["already_login"],
      );

  Map<String, dynamic> toJson() => {
        "token": token == null ? null : token,
        "already_login": alreadyLogin == null ? null : alreadyLogin,
      };
}
