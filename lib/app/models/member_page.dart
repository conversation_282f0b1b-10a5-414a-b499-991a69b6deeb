// To parse this JSON data, do
//
//     final memberPage = memberPageFromJson(jsonString);

import 'dart:convert';

import 'package:okshop_model/okshop_model.dart';

class MemberPage {
  MemberPage({
    this.data,
    this.pagination,
    this.vipCount,
  });

  List<Member> data;
  Pagination pagination;
  num vipCount;

  MemberPage copyWith({
    List<Member> data,
    Pagination pagination,
    num vipCount,
  }) =>
      MemberPage(
        data: data ?? this.data,
        pagination: pagination ?? this.pagination,
        vipCount: vipCount ?? this.vipCount,
      );

  factory MemberPage.fromRawJson(String str) =>
      MemberPage.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberPage.fromJson(Map<String, dynamic> json) => MemberPage(
        data: json["data"] == null
            ? null
            : List<Member>.from(json["data"].map((x) => Member.fromJson(x))),
        pagination: json["pagination"] == null
            ? null
            : Pagination.fromJson(json["pagination"]),
        vipCount: json["vip_count"] == null ? null : json["vip_count"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? null
            : List<dynamic>.from(data.map((x) => x.toJson())),
        "pagination": pagination == null ? null : pagination.toJson(),
        "vip_count": vipCount == null ? null : vipCount,
      };
}
