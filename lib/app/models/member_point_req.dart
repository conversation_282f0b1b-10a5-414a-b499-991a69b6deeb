// To parse this JSON data, do
//
//     final memberPointReq = memberPointReqFromJson(jsonString);

import 'dart:convert';

class MemberPointReq {
  MemberPointReq({
    this.points,
    this.type,
    this.expiryDate,
    this.comment,
  });

  num points;
  num type;
  String expiryDate;
  String comment;

  MemberPointReq copyWith({
    num points,
    num type,
    String expiryDate,
    String comment,
  }) =>
      MemberPointReq(
        points: points ?? this.points,
        type: type ?? this.type,
        expiryDate: expiryDate ?? this.expiryDate,
        comment: comment ?? this.comment,
      );

  factory MemberPointReq.fromRawJson(String str) =>
      MemberPointReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberPointReq.fromJson(Map<String, dynamic> json) => MemberPointReq(
        points: json["points"] == null ? null : json["points"],
        type: json["type"] == null ? null : json["type"],
        expiryDate: json["expiry_date"] == null ? null : json["expiry_date"],
        comment: json["comment"] == null ? null : json["comment"],
      );

  Map<String, dynamic> toJson() => {
        "points": points == null ? null : points,
        "type": type == null ? null : type,
        "expiry_date": expiryDate == null ? null : expiryDate,
        "comment": comment == null ? null : comment,
      };
}
