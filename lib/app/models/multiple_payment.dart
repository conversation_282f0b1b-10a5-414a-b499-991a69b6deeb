// To parse this JSON data, do
//
//     final multiplePayment = multiplePaymentFromJson(jsonString);

import 'dart:convert';

class MultiplePayment {
  MultiplePayment({
    this.paymentMethodId,
    this.money,
    this.other,
  });

  num paymentMethodId;
  num money;
  String other;

  MultiplePayment copyWith({
    num paymentMethodId,
    num money,
    String other,
  }) =>
      MultiplePayment(
        paymentMethodId: paymentMethodId ?? this.paymentMethodId,
        money: money ?? this.money,
        other: other ?? this.other,
      );

  factory MultiplePayment.fromRawJson(String str) =>
      MultiplePayment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MultiplePayment.fromJson(Map<String, dynamic> json) =>
      MultiplePayment(
        paymentMethodId: json["payment_method_id"] == null
            ? null
            : json["payment_method_id"],
        money: json["money"] == null ? null : json["money"],
        other: json["other"] == null ? null : json["other"],
      );

  Map<String, dynamic> toJson() => {
        "payment_method_id": paymentMethodId == null ? null : paymentMethodId,
        "money": money == null ? null : money,
        "other": other == null ? null : other,
      };
}
