import 'dart:convert';

import 'package:intl/intl.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/extension.dart';

class OrderDetail {
  OrderDetail({
    this.comment,
    this.createdAt,
    this.id,
    this.invoiceInfo,
    this.invoiceType,
    this.memberId,
    this.memberName,
    this.memberStatus,
    this.orderAddresses,
    this.orderDiner,
    this.orderDiscount,
    this.orderInvoice,
    this.orderItems,
    this.orderNumber,
    this.orderPayment,
    this.orderPayments,
    this.orderShipping,
    this.paymentStatus,
    this.redeemMemberPoints,
    this.refundCreatedAt,
    this.refundStoreAccountName,
    this.status,
    this.subtotal,
    this.total,
    this.type,
    this.updatedAt,
  });

  String comment;
  String createdAt;
  num id;
  InvoiceInfo invoiceInfo;
  num invoiceType;
  num memberId;
  String memberName;
  num memberStatus;
  List<OrderAddress> orderAddresses;
  OrderDiner orderDiner;
  List<OrderDiscount> orderDiscount;
  OrderInvoice orderInvoice;
  List<OrderItem> orderItems;
  String orderNumber;
  OrderPayment orderPayment;
  List<OrderPayment> orderPayments;
  OrderShipping orderShipping;
  num paymentStatus;
  num redeemMemberPoints;
  String refundCreatedAt;
  String refundStoreAccountName;
  num status;
  num subtotal;
  num total;
  // 0：餐飲: 內用
  // 1：餐飲: 店內->外帶 / 餐飲: 線上->自取
  // 2：餐飲: 外送
  // 3：零售: 自取
  // 4：零售: 宅配
  // 5：零售: 超商
  num type;
  String updatedAt;

  OrderDetail.fromJson(Map<String, dynamic> json) {
    comment = json['comment'];
    createdAt = json['created_at'];
    id = json['id'];
    memberId = json['member_id'];
    memberName = json['member_name'];
    memberStatus = json['member_status'];
    orderAddresses = List.from(json['order_addresses'] ?? [])
        .map((x) => OrderAddress.fromJson(x))
        .toList();
    orderDiner = json['order_diner'] != null
        ? OrderDiner.fromJson(json['order_diner'])
        : null;
    orderDiscount = List.from(json['order_discount'] ?? [])
        .map((x) => OrderDiscount.fromJson(x))
        .toList();
    orderInvoice = json['order_invoice'] != null
        ? OrderInvoice.fromJson(json['order_invoice'])
        : null;
    orderItems = List.from(json['order_items'] ?? [])
        .map((x) => OrderItem.fromJson(x))
        .toList();
    orderNumber = json['order_number'];
    orderShipping = json["order_shipping"] != null
        ? OrderShipping.fromJson(json["order_shipping"])
        : null;
    orderPayment = json['order_payment'] != null
        ? OrderPayment.fromJson(json['order_payment'])
        : null;
    orderPayments = List.from(json['order_payments'] ?? [])
        .map((x) => OrderPayment.fromJson(x))
        .toList();
    paymentStatus = json['payment_status'];
    status = json['status'];
    subtotal = json['subtotal'];
    total = json['total'];
    type = json['type'];
    invoiceType = json["invoice_type"];
    invoiceInfo = json["invoice_info"] != null
        ? InvoiceInfo.fromJson(json["invoice_info"])
        : null;
    redeemMemberPoints = json["redeem_member_points"];
    refundCreatedAt = json["refund_created_at"];
    refundStoreAccountName = json["refund_store_account_name"];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{
      "redeem_member_points": redeemMemberPoints,
      "refund_created_at": refundCreatedAt,
      "refund_store_account_name": refundStoreAccountName,
    };
    data['comment'] = comment;
    data['created_at'] = createdAt;
    data['id'] = id;
    data['member_id'] = memberId;
    data['member_name'] = memberName;
    data['member_status'] = memberStatus;
    data["order_addresses"] = orderAddresses != null
        ? List<dynamic>.from(orderAddresses.map((x) => x.toJson()))
        : null;
    data['order_diner'] = orderDiner?.toJson();
    data['order_discount'] = orderDiscount?.map((v) => v.toJson())?.toList();
    data['order_invoice'] = orderInvoice?.toJson();
    data['order_items'] = orderItems?.map((v) => v.toJson())?.toList();
    data['order_number'] = orderNumber;
    data['order_payment'] = orderPayment?.toJson();
    data['order_payment'] =
        orderPayments != null && this.orderPayments.isNotEmpty
            ? jsonEncode(this.orderPayments)
            : null;
    data["order_shipping"] = orderShipping?.toJson();
    data['payment_status'] = paymentStatus;
    data['status'] = status;
    data['subtotal'] = subtotal;
    data['total'] = total;
    data['type'] = type;
    data["invoice_type"] = invoiceType;
    data["invoice_info"] = invoiceInfo?.toJson();
    data['updated_at'] = updatedAt;
    return data;
  }

  // TODO: REMOVE ME
  // use 'type.orderType.getNameWithSource(orderSource)' instead
  // 顯示中文字串
  String orderServiceDisplayStr() {
    this.type ??= -1;
    final orderSource = this.orderDiner.source.orderSource;
    return this.type.orderType.getNameWithSource(orderSource);
    // switch (type) {
    //   case 0:
    //     return '內用';
    //   case 1:
    //     {
    //       //線上單是自取 / 店內單是外帶
    //       if (orderDiner.source == ORDER_SOURCE_ONLINE) {
    //         return '自取';
    //       }
    //       return '外帶';
    //     }
    //   case 2:
    //     return '外送';
    //   default:
    //     return '未知';
    // }
  }

  //內用區域桌號顯示字串
  // String partitionTableDisplayStr() => orderDiner?.partitionTableName ?? '';
  String get partitionTableName => orderDiner?.partitionTableName ?? '';

  //創建時間顯示
  String createdAtDisplayStr() {
    return DateFormat.MMMEd('zh_TW').format(createdLocalTimeAt);
  }

  //創建時間顯示
  String createdAtStickerDisplayStr() {
    return DateFormat.Hm('zh_TW').format(createdLocalTimeAt);
  }

  DateTime get createdLocalTimeAt => utcToLocal(this.createdAt);

  //訂單是否當前還在可以編輯的狀態?
  bool editable() {
    // 已付款單不可編輯
    if ([PaymentStatus.Paid].contains(paymentStatus.paymentStatus)) {
      return false;
    }
    // 可以編輯的狀態
    // 0：處理中
    // 1: 已確認
    if ([OrderStatus.Padding, OrderStatus.Accepted]
        .contains(status.orderStatus)) {
      return true;
    }
    return false;
  }

  //非常神奇的預測 Server 變數習性來得知送出時真正的 kind
  //希望不會有錯, 喔彌陀佛，阿門
  // 類型
  // 0：餐飲店內
  // 1：餐飲線上 (這個在此處應該是不會有)
  // 2：零售
  num get kind {
    if (type == RETAIL_ORDER_TYPE_PICK_UP ||
        type == RETAIL_ORDER_TYPE_HOME_DELIVERY ||
        type == RETAIL_ORDER_TYPE_CONVENIENCE_STORE) {
      return 2;
    } else {
      return orderDiner.source;
    }
  }
}

class OrderDiscount {
  OrderDiscount({
    this.discountDescription,
    this.discountName,
    this.discountPrice,
    this.type,
  });

  DiscountDescription discountDescription;
  String discountName;
  num discountPrice;
  num type;

  OrderDiscount copyWith({
    DiscountDescription discountDescription,
    String discountName,
    num discountPrice,
    num type,
  }) =>
      OrderDiscount(
        discountDescription: discountDescription ?? this.discountDescription,
        discountName: discountName ?? this.discountName,
        discountPrice: discountPrice ?? this.discountPrice,
        type: type ?? this.type,
      );

  factory OrderDiscount.fromRawJson(String str) =>
      OrderDiscount.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderDiscount.fromJson(Map<String, dynamic> json) => OrderDiscount(
        discountDescription: json["discount_description"] == null
            ? null
            : DiscountDescription.fromJson(json["discount_description"]),
        discountName:
            json["discount_name"] == null ? null : json["discount_name"],
        discountPrice:
            json["discount_price"] == null ? null : json["discount_price"],
        type: json["type"] == null ? null : json["type"],
      );

  Map<String, dynamic> toJson() => {
        "discount_description":
            discountDescription == null ? null : discountDescription.toJson(),
        "discount_name": discountName == null ? null : discountName,
        "discount_price": discountPrice == null ? null : discountPrice,
        "type": type == null ? null : type,
      };
}

class DiscountDescription {
  DiscountDescription({
    this.couponId,
    this.description,
    this.discount,
    this.expiryDate,
    this.id,
    this.imageUrl,
    this.isOnline,
    this.kind,
    this.lastCount,
    this.promotionType,
    this.status,
    this.title,
  });

  num couponId;
  String description;
  num discount;
  String expiryDate;
  num id;
  String imageUrl;
  num isOnline;
  num kind;
  num lastCount;
  num promotionType;
  num status;
  String title;

  DiscountDescription copyWith({
    num couponId,
    String description,
    num discount,
    String expiryDate,
    num id,
    String imageUrl,
    num isOnline,
    num kind,
    num lastCount,
    num promotionType,
    num status,
    String title,
  }) =>
      DiscountDescription(
        couponId: couponId ?? this.couponId,
        description: description ?? this.description,
        discount: discount ?? this.discount,
        expiryDate: expiryDate ?? this.expiryDate,
        id: id ?? this.id,
        imageUrl: imageUrl ?? this.imageUrl,
        isOnline: isOnline ?? this.isOnline,
        kind: kind ?? this.kind,
        lastCount: lastCount ?? this.lastCount,
        promotionType: promotionType ?? this.promotionType,
        status: status ?? this.status,
        title: title ?? this.title,
      );

  factory DiscountDescription.fromRawJson(String str) =>
      DiscountDescription.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DiscountDescription.fromJson(Map<String, dynamic> json) =>
      DiscountDescription(
        couponId: json["coupon_id"] == null ? null : json["coupon_id"],
        description: json["description"] == null ? null : json["description"],
        discount: json["discount"] == null ? null : json["discount"],
        expiryDate: json["expiry_date"] == null ? null : json["expiry_date"],
        id: json["id"] == null ? null : json["id"],
        imageUrl: json["image_url"] == null ? null : json["image_url"],
        isOnline: json["is_online"] == null ? null : json["is_online"],
        kind: json["kind"] == null ? null : json["kind"],
        lastCount: json["last_count"] == null ? null : json["last_count"],
        promotionType:
            json["promotion_type"] == null ? null : json["promotion_type"],
        status: json["status"] == null ? null : json["status"],
        title: json["title"] == null ? null : json["title"],
      );

  Map<String, dynamic> toJson() => {
        "coupon_id": couponId == null ? null : couponId,
        "description": description == null ? null : description,
        "discount": discount == null ? null : discount,
        "expiry_date": expiryDate == null ? null : expiryDate,
        "id": id == null ? null : id,
        "image_url": imageUrl == null ? null : imageUrl,
        "is_online": isOnline == null ? null : isOnline,
        "kind": kind == null ? null : kind,
        "last_count": lastCount == null ? null : lastCount,
        "promotion_type": promotionType == null ? null : promotionType,
        "status": status == null ? null : status,
        "title": title == null ? null : title,
      };
}

class OrderInvoice {
  String carrierId;
  num carrierType;
  bool invoicePaper;
  String npoBan;
  String number;
  String randomNumber;
  num status;
  String vatNumber;

  OrderInvoice(
      {this.carrierId,
      this.carrierType,
      this.invoicePaper,
      this.npoBan,
      this.number,
      this.randomNumber,
      this.status,
      this.vatNumber});

  OrderInvoice.fromJson(Map<String, dynamic> json) {
    carrierId = json['carrier_id'];
    carrierType = json['carrier_type'];
    invoicePaper = json['invoice_paper'];
    npoBan = json['npo_ban'];
    number = json['number'];
    randomNumber = json['random_number'];
    status = json['status'];
    vatNumber = json['vat_number'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['carrier_id'] = this.carrierId;
    data['carrier_type'] = this.carrierType;
    data['invoice_paper'] = this.invoicePaper;
    data['npo_ban'] = this.npoBan;
    data['number'] = this.number;
    data['random_number'] = this.randomNumber;
    data['status'] = this.status;
    data['vat_number'] = this.vatNumber;
    return data;
  }
}

class OrderItem {
  //據設計者: 這個是單件價格，不是算上 quantity 的總價
  num finalPrice;
  num id;
  num isVip;
  List<num> productCategoryIds;
  num productId;
  String productName;
  String productSpec1;
  List<num> productSpec1Ids;
  num productTaxType;
  num quantity;
  num type;
  num vipPrice;

  //-- 以下為 Modal 編輯時期擴充用變數，作為暫存不會被算成 json 的一部分
  //就決定這樣處理編輯時期的資料了，應該這樣就足以應付 (不做 Addition Category 分類)
  List<AdditionProduct> additionProducts = [];

  OrderItem({
    this.finalPrice,
    this.id,
    this.isVip,
    this.productCategoryIds,
    this.productId,
    this.productName,
    this.productSpec1,
    this.productSpec1Ids,
    this.productTaxType,
    this.quantity = 1,
    this.type,
    this.vipPrice,
  });

  OrderItem copyWith({
    num finalPrice,
    num id,
    num isVip,
    List<num> productCategoryIds,
    num productId,
    String productName,
    String productSpec1,
    List<num> productSpec1Ids,
    num productTaxType,
    num quantity,
    num type,
    num vipPrice,
  }) =>
      OrderItem(
        finalPrice: finalPrice ?? this.finalPrice,
        id: id ?? this.id,
        isVip: isVip ?? this.isVip,
        productCategoryIds: productCategoryIds ?? this.productCategoryIds,
        productId: productId ?? this.productId,
        productName: productName ?? this.productName,
        productSpec1: productSpec1 ?? this.productSpec1,
        productSpec1Ids: productSpec1Ids ?? this.productSpec1Ids,
        productTaxType: productTaxType ?? this.productTaxType,
        quantity: quantity ?? this.quantity,
        type: type ?? this.type,
        vipPrice: vipPrice ?? this.vipPrice,
      );

  factory OrderItem.fromRawJson(String str) =>
      OrderItem.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderItem.fromJson(Map<String, dynamic> json) => OrderItem(
        finalPrice: json["final_price"] == null ? null : json["final_price"],
        id: json["id"] == null ? null : json["id"],
        isVip: json["is_vip"] == null ? null : json["is_vip"],
        productCategoryIds: json["product_category_ids"] == null
            ? null
            : List<int>.from(json["product_category_ids"].map((x) => x)),
        productId: json["product_id"] == null ? null : json["product_id"],
        productName: json["product_name"] == null ? null : json["product_name"],
        productSpec1:
            json["product_spec_1"] == null ? null : json["product_spec_1"],
        productSpec1Ids: json["product_spec_1_ids"] == null
            ? null
            : List<int>.from(json["product_spec_1_ids"].map((x) => x)),
        productTaxType:
            json["product_tax_type"] == null ? null : json["product_tax_type"],
        quantity: json["quantity"] == null ? null : json["quantity"],
        type: json["type"] == null ? null : json["type"],
        vipPrice: json["vip_price"] == null ? null : json["vip_price"],
      );

  Map<String, dynamic> toJson() => {
        "final_price": finalPrice == null ? null : finalPrice,
        "id": id == null ? null : id,
        "is_vip": isVip == null ? null : isVip,
        "product_category_ids": productCategoryIds == null
            ? null
            : List<dynamic>.from(productCategoryIds.map((x) => x)),
        "product_id": productId == null ? null : productId,
        "product_name": productName == null ? null : productName,
        "product_spec_1": productSpec1 == null ? null : productSpec1,
        "product_spec_1_ids": productSpec1Ids == null
            ? null
            : List<dynamic>.from(productSpec1Ids.map((x) => x)),
        "product_tax_type": productTaxType == null ? null : productTaxType,
        "quantity": quantity == null ? null : quantity,
        "type": type == null ? null : type,
        "vip_price": vipPrice == null ? null : vipPrice,
      };

  //檢查否個附加商品是否存在 (additionProducts)
  //編輯時期限定
  bool isAdditionProductSelected(num additionProductId) {
    return additionProducts.any((element) => element.id == additionProductId);
  }

  //嘗試加入一個附加商品
  bool addAdditionProduct(AdditionProduct additionProduct) {
    if (additionProduct != null &&
        !isAdditionProductSelected(additionProduct.id)) {
      final jsonString = additionProduct.toRawJson();
      final ap = AdditionProduct.fromRawJson(jsonString);
      additionProducts.add(ap);
      return true;
    }
    return false;
  }

  //嘗試移除一個附加商品
  bool removeAdditionProduct(num additionProductId) {
    for (num i = 0; i < additionProducts.length; ++i) {
      if (additionProducts[i].id == additionProductId) {
        additionProducts.removeAt(i);
        return true;
      }
    }
    return false;
  }

  //嘗試加入/移除一個附加商品
  //這邊邏輯比較特殊，要做單選判斷，如果是單選擇要把已有的分類內產品都給先取消掉
  bool setAdditionProduct(
    AdditionProduct additionProduct,
    bool b,
    bool isMultiSelection,
    Iterable<AdditionProduct> categoryAdditionProducts,
  ) {
    if (additionProduct != null) {
      if (b) {
        if (!isMultiSelection) {
          //單選，嘗試先移除所有同分類商品
          for (var item in categoryAdditionProducts) {
            removeAdditionProduct(item.id);
          }
        }
        return addAdditionProduct(additionProduct);
      } else {
        return removeAdditionProduct(additionProduct.id);
      }
    }
    return false;
  }

  //是否有任何一個商品?
  bool hasOneAdditionProduct(Iterable<num> additionProducts) {
    return additionProducts
        .any((element) => isAdditionProductSelected(element));
  }

  //檢查當前選擇了傳入的附加商品中的幾項
  num hasAdditionProductQuantity(Iterable<num> additionProducts) {
    additionProducts ??= <num>[];
    return additionProducts.fold(0, (previousValue, element) {
      return previousValue + (isAdditionProductSelected(element) ? 1 : 0);
    });
  }

  // TODO: implement me.
  // String get nnproductSpec1 {
  //   return '';
  // }

  //填寫 spec 字串以及 SpecIDs
  // TODO: refactor me, use nnproductSpec1 instead.
  fillSpec() {
    // print('fillSpec');
    productSpec1 = '';
    productSpec1Ids = [];
    for (num i = 0; i < additionProducts.length; ++i) {
      if (productSpec1.isNotEmpty) {
        //Yeah?
        productSpec1 += '/';
        // productSpec1 += '、';
      }
      productSpec1 += additionProducts[i].name;

      //Add price?
      if (additionProducts[i].price > 0) {
        productSpec1 += '+${additionProducts[i].price.decimalStyle}';
      }

      //Also add the spec ID
      productSpec1Ids.add(additionProducts[i].id);
    }
  }
}

class OrderDiner {
  OrderDiner({
    this.adult,
    this.cancelReason,
    this.checkoutAt,
    this.child,
    this.id,
    this.isPrint,
    this.mealAt,
    this.memo,
    this.source,
    this.table1Id,
    this.table1Name,
    this.table2Id,
    this.table2Name,
  });

  num adult;
  String cancelReason;
  String checkoutAt;
  num child;
  num id;
  num isPrint;
  String mealAt;
  String memo;
  num source;
  num table1Id;
  String table1Name;
  num table2Id;
  String table2Name;

  OrderDiner copyWith({
    num adult,
    String cancelReason,
    String checkoutAt,
    num child,
    num id,
    num isPrint,
    String mealAt,
    String memo,
    num source,
    num table1Id,
    String table1Name,
    num table2Id,
    String table2Name,
  }) =>
      OrderDiner(
        adult: adult ?? this.adult,
        cancelReason: cancelReason ?? this.cancelReason,
        checkoutAt: checkoutAt ?? this.checkoutAt,
        child: child ?? this.child,
        id: id ?? this.id,
        isPrint: isPrint ?? this.isPrint,
        mealAt: mealAt ?? this.mealAt,
        memo: memo ?? this.memo,
        source: source ?? this.source,
        table1Id: table1Id ?? this.table1Id,
        table1Name: table1Name ?? this.table1Name,
        table2Id: table2Id ?? this.table2Id,
        table2Name: table2Name ?? this.table2Name,
      );

  factory OrderDiner.fromRawJson(String str) =>
      OrderDiner.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderDiner.fromJson(Map<String, dynamic> json) => OrderDiner(
        adult: json["adult"] == null ? null : json["adult"],
        cancelReason:
            json["cancel_reason"] == null ? null : json["cancel_reason"],
        checkoutAt: json["checkout_at"] == null ? null : json["checkout_at"],
        child: json["child"] == null ? null : json["child"],
        id: json["id"] == null ? null : json["id"],
        isPrint: json["is_print"] == null ? null : json["is_print"],
        mealAt: json["meal_at"] == null ? null : json["meal_at"],
        memo: json["memo"] == null ? null : json["memo"],
        source: json["source"] == null ? null : json["source"],
        table1Id: json["table1_id"] == null ? null : json["table1_id"],
        table1Name: json["table1_name"] == null ? null : json["table1_name"],
        table2Id: json["table2_id"] == null ? null : json["table2_id"],
        table2Name: json["table2_name"] == null ? null : json["table2_name"],
      );

  Map<String, dynamic> toJson() => {
        "adult": adult == null ? null : adult,
        "cancel_reason": cancelReason == null ? null : cancelReason,
        "checkout_at": checkoutAt == null ? null : checkoutAt,
        "child": child == null ? null : child,
        "id": id == null ? null : id,
        "is_print": isPrint == null ? null : isPrint,
        "meal_at": mealAt == null ? null : mealAt,
        "memo": memo == null ? null : memo,
        "source": source == null ? null : source,
        "table1_id": table1Id == null ? null : table1Id,
        "table1_name": table1Name == null ? null : table1Name,
        "table2_id": table2Id == null ? null : table2Id,
        "table2_name": table2Name == null ? null : table2Name,
      };
}

OrderAddress orderAddressesFromJson(String str) =>
    OrderAddress.fromJson(json.decode(str));

String orderAddressesToJson(OrderAddress data) => json.encode(data.toJson());

class OrderAddress {
  OrderAddress({
    this.address,
    this.city,
    this.cityId,
    this.cityarea,
    this.cityAreaId,
    this.name,
    this.phone,
    this.postcode,
    this.type,
  });

  String address;
  String city;
  num cityId;
  String cityarea;
  num cityAreaId;
  String name;
  String phone;
  String postcode;
  //type = 0 時上面的 name phone 是 buyer 的
  //type = 1 時上面的 name phone 是 receiver 的
  num type;

  factory OrderAddress.fromJson(Map<String, dynamic> json) => OrderAddress(
        address: json["address"] == null ? null : json["address"],
        city: json["city"] == null ? null : json["city"],
        cityId: json["city_id"] == null ? null : json["city_id"],
        cityarea: json["cityarea"] == null ? null : json["cityarea"],
        cityAreaId: json["cityarea_id"] == null ? null : json["cityarea_id"],
        name: json["name"] == null ? null : json["name"],
        phone: json["phone"] == null ? null : json["phone"],
        postcode: json["postcode"] == null ? null : json["postcode"],
        type: json["type"] == null ? null : json["type"],
      );

  Map<String, dynamic> toJson() => {
        "address": address == null ? null : address,
        "city": city == null ? null : city,
        "city_id": cityId == null ? null : cityId,
        "cityarea": cityarea == null ? null : cityarea,
        "cityarea_id": cityAreaId == null ? null : cityAreaId,
        "name": name == null ? null : name,
        "phone": phone == null ? null : phone,
        "postcode": postcode == null ? null : postcode,
        "type": type == null ? null : type,
      };
}

class OrderShipping {
  OrderShipping({
    this.shippingFee,
    this.shippingId,
    this.shippingMethodId,
    this.shippingName,
  });

  num shippingFee;
  num shippingId;
  num shippingMethodId;
  String shippingName;

  OrderShipping copyWith({
    num shippingFee,
    num shippingId,
    num shippingMethodId,
    String shippingName,
  }) =>
      OrderShipping(
        shippingFee: shippingFee ?? this.shippingFee,
        shippingId: shippingId ?? this.shippingId,
        shippingMethodId: shippingMethodId ?? this.shippingMethodId,
        shippingName: shippingName ?? this.shippingName,
      );

  factory OrderShipping.fromRawJson(String str) =>
      OrderShipping.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderShipping.fromJson(Map<String, dynamic> json) => OrderShipping(
        shippingFee: json["shipping_fee"] == null ? null : json["shipping_fee"],
        shippingId: json["shipping_id"] == null ? null : json["shipping_id"],
        shippingMethodId: json["shipping_method_id"] == null
            ? null
            : json["shipping_method_id"],
        shippingName:
            json["shipping_name"] == null ? null : json["shipping_name"],
      );

  Map<String, dynamic> toJson() => {
        "shipping_fee": shippingFee == null ? null : shippingFee,
        "shipping_id": shippingId == null ? null : shippingId,
        "shipping_method_id":
            shippingMethodId == null ? null : shippingMethodId,
        "shipping_name": shippingName == null ? null : shippingName,
      };
}

class InvoiceInfo {
  InvoiceInfo({
    this.vatNumber,
    this.carrierId,
    this.cityId,
    this.cityareaId,
    this.address,
    this.name,
    this.company,
  });

  String vatNumber;
  String carrierId;
  String cityId;
  String cityareaId;
  String address;
  String name;
  String company;

  InvoiceInfo copyWith({
    String vatNumber,
    String carrierId,
    String cityId,
    String cityareaId,
    String address,
    String name,
    String company,
  }) =>
      InvoiceInfo(
        vatNumber: vatNumber ?? this.vatNumber,
        carrierId: carrierId ?? this.carrierId,
        cityId: cityId ?? this.cityId,
        cityareaId: cityareaId ?? this.cityareaId,
        address: address ?? this.address,
        name: name ?? this.name,
        company: company ?? this.company,
      );

  factory InvoiceInfo.fromRawJson(String str) =>
      InvoiceInfo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InvoiceInfo.fromJson(Map<String, dynamic> json) => InvoiceInfo(
        vatNumber: json["vat_number"] == null ? null : json["vat_number"],
        carrierId: json["carrier_id"] == null ? null : json["carrier_id"],
        cityId: json["city_id"] == null ? null : json["city_id"],
        cityareaId: json["cityarea_id"] == null ? null : json["cityarea_id"],
        address: json["address"] == null ? null : json["address"],
        name: json["name"] == null ? null : json["name"],
        company: json["company"] == null ? null : json["company"],
      );

  Map<String, dynamic> toJson() => {
        "vat_number": vatNumber == null ? null : vatNumber,
        "carrier_id": carrierId == null ? null : carrierId,
        "city_id": cityId == null ? null : cityId,
        "cityarea_id": cityareaId == null ? null : cityareaId,
        "address": address == null ? null : address,
        "name": name == null ? null : name,
        "company": company == null ? null : company,
      };
}

class OrderPayment {
  OrderPayment({
    this.info,
    this.name,
    this.payMethodId,
    this.paymentMethodId,
    this.total,
  });

  Info info;
  String name;
  num payMethodId;
  num paymentMethodId;
  num total;

  OrderPayment copyWith({
    Info info,
    String name,
    num payMethodId,
    num paymentMethodId,
    num total,
  }) =>
      OrderPayment(
        info: info ?? this.info,
        name: name ?? this.name,
        payMethodId: payMethodId ?? this.payMethodId,
        paymentMethodId: paymentMethodId ?? this.paymentMethodId,
        total: total ?? this.total,
      );

  factory OrderPayment.fromRawJson(String str) =>
      OrderPayment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderPayment.fromJson(Map<String, dynamic> json) => OrderPayment(
        info: json["info"] == null ? null : Info.fromJson(json["info"]),
        name: json["name"] == null ? null : json["name"],
        payMethodId:
            json["pay_method_id"] == null ? null : json["pay_method_id"],
        paymentMethodId: json["payment_method_id"] == null
            ? null
            : json["payment_method_id"],
        total: json["total"] == null ? null : json["total"],
      );

  Map<String, dynamic> toJson() => {
        "info": info == null ? null : info.toJson(),
        "name": name == null ? null : name,
        "pay_method_id": payMethodId == null ? null : payMethodId,
        "payment_method_id": paymentMethodId == null ? null : paymentMethodId,
        "total": total == null ? null : total,
      };
}

class Info {
  Info({
    this.paid,
    this.change,
    this.paymentFee,
    this.other,
  });

  num paid;
  num change;
  num paymentFee;
  String other;

  Info copyWith({
    num paid,
    num change,
    num paymentFee,
    String other,
  }) =>
      Info(
        paid: paid ?? this.paid,
        change: change ?? this.change,
        paymentFee: paymentFee ?? this.paymentFee,
        other: other ?? this.other,
      );

  factory Info.fromRawJson(String str) => Info.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Info.fromJson(Map<String, dynamic> json) => Info(
        paid: json["paid"] == null ? null : json["paid"],
        change: json["change"] == null ? null : json["change"],
        paymentFee: json["payment_fee"] == null ? null : json["payment_fee"],
        other: json["other"] == null ? null : json["other"],
      );

  Map<String, dynamic> toJson() => {
        "paid": paid == null ? null : paid,
        "change": change == null ? null : change,
        "payment_fee": paymentFee == null ? null : paymentFee,
        "other": other == null ? null : other,
      };
}
