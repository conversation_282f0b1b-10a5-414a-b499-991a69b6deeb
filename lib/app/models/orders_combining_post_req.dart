// To parse this JSON data, do
//
//     final ordersCombiningPostReq = ordersCombiningPostReqFromJson(jsonString);

import 'dart:convert';

import 'multiple_payment.dart';

class OrdersCombiningPostReq {
  OrdersCombiningPostReq({
    this.memberId,
    this.subtotal,
    this.fee,
    this.discount,
    this.additionalCharges,
    this.redeemMemberPoints,
    this.pointDiscountLimit,
    this.pointGet,
    this.memberCouponId,
    this.memberCouponDiscount,
    this.memberCouponExtraPrice,
    this.total,
    this.paid,
    this.change,
    this.comment,
    this.invoice,
    this.invoiceNumber,
    this.randomNumber,
    this.invoicePaper,
    this.vatNumber,
    this.carrierType,
    this.carrierId,
    this.npoBan,
    this.multiplePayment,
    this.paymentMethodId,
    this.orderIds,
  });

  num memberId;
  num subtotal;
  num fee;
  num discount;
  num additionalCharges;
  num redeemMemberPoints;
  num pointDiscountLimit;
  num pointGet;
  num memberCouponId;
  num memberCouponDiscount;
  num memberCouponExtraPrice;
  num total;
  num paid;
  num change;
  String comment;
  bool invoice;
  String invoiceNumber;
  num randomNumber;
  bool invoicePaper;
  String vatNumber;
  num carrierType;
  String carrierId;
  String npoBan;
  List<MultiplePayment> multiplePayment;
  num paymentMethodId;
  List<num> orderIds;

  OrdersCombiningPostReq copyWith({
    num memberId,
    num subtotal,
    num fee,
    num discount,
    num additionalCharges,
    num redeemMemberPoints,
    num pointDiscountLimit,
    num pointGet,
    num memberCouponId,
    num memberCouponDiscount,
    num memberCouponExtraPrice,
    num total,
    num paid,
    num change,
    String comment,
    bool invoice,
    String invoiceNumber,
    num randomNumber,
    bool invoicePaper,
    String vatNumber,
    num carrierType,
    String carrierId,
    String npoBan,
    List<MultiplePayment> multiplePayment,
    num paymentMethodId,
    List<num> orderIds,
  }) =>
      OrdersCombiningPostReq(
        memberId: memberId ?? this.memberId,
        subtotal: subtotal ?? this.subtotal,
        fee: fee ?? this.fee,
        discount: discount ?? this.discount,
        additionalCharges: additionalCharges ?? this.additionalCharges,
        redeemMemberPoints: redeemMemberPoints ?? this.redeemMemberPoints,
        pointDiscountLimit: pointDiscountLimit ?? this.pointDiscountLimit,
        pointGet: pointGet ?? this.pointGet,
        memberCouponId: memberCouponId ?? this.memberCouponId,
        memberCouponDiscount: memberCouponDiscount ?? this.memberCouponDiscount,
        memberCouponExtraPrice:
            memberCouponExtraPrice ?? this.memberCouponExtraPrice,
        total: total ?? this.total,
        paid: paid ?? this.paid,
        change: change ?? this.change,
        comment: comment ?? this.comment,
        invoice: invoice ?? this.invoice,
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        randomNumber: randomNumber ?? this.randomNumber,
        invoicePaper: invoicePaper ?? this.invoicePaper,
        vatNumber: vatNumber ?? this.vatNumber,
        carrierType: carrierType ?? this.carrierType,
        carrierId: carrierId ?? this.carrierId,
        npoBan: npoBan ?? this.npoBan,
        multiplePayment: multiplePayment ?? this.multiplePayment,
        paymentMethodId: paymentMethodId ?? this.paymentMethodId,
        orderIds: orderIds ?? this.orderIds,
      );

  factory OrdersCombiningPostReq.fromRawJson(String str) =>
      OrdersCombiningPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrdersCombiningPostReq.fromJson(Map<String, dynamic> json) =>
      OrdersCombiningPostReq(
        memberId: json["member_id"] == null ? null : json["member_id"],
        subtotal: json["subtotal"] == null ? null : json["subtotal"],
        fee: json["fee"] == null ? null : json["fee"],
        discount: json["discount"] == null ? null : json["discount"],
        additionalCharges: json["additional_charges"] == null
            ? null
            : json["additional_charges"],
        redeemMemberPoints: json["redeem_member_points"] == null
            ? null
            : json["redeem_member_points"],
        pointDiscountLimit: json["point_discount_limit"] == null
            ? null
            : json["point_discount_limit"],
        pointGet: json["point_get"] == null ? null : json["point_get"],
        memberCouponId:
            json["member_coupon_id"] == null ? null : json["member_coupon_id"],
        memberCouponDiscount: json["member_coupon_discount"] == null
            ? null
            : json["member_coupon_discount"],
        memberCouponExtraPrice: json["member_coupon_extra_price"] == null
            ? null
            : json["member_coupon_extra_price"],
        total: json["total"] == null ? null : json["total"],
        paid: json["paid"] == null ? null : json["paid"],
        change: json["change"] == null ? null : json["change"],
        comment: json["comment"] == null ? null : json["comment"],
        invoice: json["invoice"] == null ? null : json["invoice"],
        invoiceNumber:
            json["invoice_number"] == null ? null : json["invoice_number"],
        randomNumber:
            json["random_number"] == null ? null : json["random_number"],
        invoicePaper:
            json["invoice_paper"] == null ? null : json["invoice_paper"],
        vatNumber: json["vat_number"] == null ? null : json["vat_number"],
        carrierType: json["carrier_type"] == null ? null : json["carrier_type"],
        carrierId: json["carrier_id"] == null ? null : json["carrier_id"],
        npoBan: json["npo_ban"] == null ? null : json["npo_ban"],
        multiplePayment: json["multiple_payment"] == null
            ? null
            : List<MultiplePayment>.from(jsonDecode(json["multiple_payment"])
                .map((x) => MultiplePayment.fromJson(x))),
        paymentMethodId: json["payment_method_id"] == null
            ? null
            : json["payment_method_id"],
        orderIds: json["order_ids"] == null
            ? null
            : List<num>.from(json["order_ids"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "member_id": memberId == null ? null : memberId,
        "subtotal": subtotal == null ? null : subtotal,
        "fee": fee == null ? null : fee,
        "discount": discount == null ? null : discount,
        "additional_charges":
            additionalCharges == null ? null : additionalCharges,
        "redeem_member_points":
            redeemMemberPoints == null ? null : redeemMemberPoints,
        "point_discount_limit":
            pointDiscountLimit == null ? null : pointDiscountLimit,
        "point_get": pointGet == null ? null : pointGet,
        "member_coupon_id": memberCouponId == null ? null : memberCouponId,
        "member_coupon_discount":
            memberCouponDiscount == null ? null : memberCouponDiscount,
        "member_coupon_extra_price":
            memberCouponExtraPrice == null ? null : memberCouponExtraPrice,
        "total": total == null ? null : total,
        "paid": paid == null ? null : paid,
        "change": change == null ? null : change,
        "comment": comment == null ? null : comment,
        "invoice": invoice == null ? null : invoice,
        "invoice_number": invoiceNumber == null ? null : invoiceNumber,
        "random_number": randomNumber == null ? null : randomNumber,
        "invoice_paper": invoicePaper == null ? null : invoicePaper,
        "vat_number": vatNumber == null ? null : vatNumber,
        "carrier_type": carrierType == null ? null : carrierType,
        "carrier_id": carrierId == null ? null : carrierId,
        "npo_ban": npoBan == null ? null : npoBan,
        "multiple_payment": multiplePayment == null
            ? null
            : jsonEncode(multiplePayment.map((e) => e.toJson()).toList()),
        "payment_method_id": paymentMethodId == null ? null : paymentMethodId,
        "order_ids": orderIds == null ? null : jsonEncode(orderIds ?? <num>[]),
      };
}
