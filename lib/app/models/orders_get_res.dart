export 'package:okshop_model/okshop_model.dart';

import 'package:muyipork/constants.dart';

// TODO: remove me, use OrderStatus instead.
const ORDER_STATUS_PROCESSING = 0; // 處理中
const ORDER_STATUS_CONFIRMED = 1; // 已確認
const ORDER_STATUS_COMPLETED = 2; // 訂單完成
const ORDER_STATUS_CANCELLED_BY_SHOP = 3; // 訂單取消 （店家）
const ORDER_STATUS_ERROR = 4; // 訂單異常
const ORDER_STATUS_REFUNDED = 5; // 訂單退貨、退款
const ORDER_STATUS_CANCELLED_BY_CUSTOMER = 6; // 訂單取消（消費者）

// TODO: remove me, use OrderStatus extension instead.
const ORDER_STATUS = const [
  {
    kKeyKey: ORDER_STATUS_PROCESSING,
    kKeyValue: '處理中',
  },
  {
    kKeyKey: ORDER_STATUS_CONFIRMED,
    kKeyValue: '已確認',
  },
  {
    kKeyKey: ORDER_STATUS_COMPLETED,
    kKeyValue: '訂單完成',
  },
  {
    kKeyKey: ORDER_STATUS_CANCELLED_BY_SHOP,
    kKeyValue: '店家取消',
  },
  {
    kKeyKey: ORDER_STATUS_ERROR,
    kKeyValue: '訂單異常',
  },
  {
    kKeyKey: ORDER_STATUS_REFUNDED,
    kKeyValue: '訂單退貨、退款',
  },
  {
    kKeyKey: ORDER_STATUS_CANCELLED_BY_CUSTOMER,
    kKeyValue: '顧客取消',
  },
];

// TODO: remove me, use OrderType instead.
// 0：餐飲: 內用
// 1：餐飲: 店內->外帶 / 餐飲: 線上->自取
// 2：餐飲: 外送
// 3：零售: 自取
// 4：零售: 宅配
// 5：零售: 超商
const MEAL_ORDER_TYPE_EAT_IN = 0;
const MEAL_ORDER_TYPE_TAKE_AWAY_OR_FETCH_AWAY = 1;
const MEAL_ORDER_TYPE_DELIVERY = 2;
const RETAIL_ORDER_TYPE_PICK_UP = 3;
const RETAIL_ORDER_TYPE_HOME_DELIVERY = 4;
const RETAIL_ORDER_TYPE_CONVENIENCE_STORE = 5;
