import 'package:muyipork/app/models/res_base.dart';
import 'package:dio/dio.dart';

class OrdersOrderIdInvoicePutRes extends ResBase {
  bool isUpdated;
  int orderId;
  String invoiceNumber;

  OrdersOrderIdInvoicePutRes({this.isUpdated, this.orderId, this.invoiceNumber});

  OrdersOrderIdInvoicePutRes.resError(Response response) : super.resError(response);
  OrdersOrderIdInvoicePutRes.unKnownError() : super.unKnownError();

  OrdersOrderIdInvoicePutRes.fromJson(Map<String, dynamic> json) {
    isUpdated = json['is_updated'];
    orderId = json['order_id'];
    invoiceNumber = json['invoice_number'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['order_id'] = this.orderId;
    data['invoice_number'] = this.invoiceNumber;
    return data;
  }
}