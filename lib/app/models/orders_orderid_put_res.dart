import 'package:muyipork/app/models/res_base.dart';
import 'package:dio/dio.dart';

class OrdersOrderIdPutRes extends ResBase {
  bool isUpdated;
  int orderId;

  OrdersOrderIdPutRes({this.isUpdated, this.orderId});

  OrdersOrderIdPutRes.resError(Response response) : super.resError(response);
  OrdersOrderIdPutRes.unKnownError() : super.unKnownError();

  OrdersOrderIdPutRes.fromJson(Map<String, dynamic> json) {
    isUpdated = json['is_updated'];
    orderId = json['order_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['order_id'] = this.orderId;
    return data;
  }
}