// To parse this JSON data, do
//
//     final ordersOrderIdStatusPutQry = ordersOrderIdStatusPutQryFromJson(jsonString);

import 'dart:convert';

class OrdersOrderIdStatusPutQry {
  OrdersOrderIdStatusPutQry({
    this.isPushMsg,
  });

  num isPushMsg;

  OrdersOrderIdStatusPutQry copyWith({
    num isPushMsg,
  }) =>
      OrdersOrderIdStatusPutQry(
        isPushMsg: isPushMsg ?? this.isPushMsg,
      );

  factory OrdersOrderIdStatusPutQry.fromRawJson(String str) =>
      OrdersOrderIdStatusPutQry.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrdersOrderIdStatusPutQry.fromJson(Map<String, dynamic> json) =>
      OrdersOrderIdStatusPutQry(
        isPushMsg: json["is_push_msg"] == null ? null : json["is_push_msg"],
      );

  Map<String, dynamic> toJson() => {
        "is_push_msg": isPushMsg == null ? null : isPushMsg,
      };
}
