// To parse this JSON data, do
//
//     final ordersOrderIdStatusPutReq = ordersOrderIdStatusPutReqFromJson(jsonString);

import 'dart:convert';

class OrdersOrderIdStatusPutReq {
  OrdersOrderIdStatusPutReq({
    this.status,
    this.cancelReason,
  });

  // 狀態
  // 0: 處理中
  // 1: 已確認
  // 2: 訂單完成
  // 3: 訂單取消 （店家）
  // 4: 訂單異常
  // 5: 訂單退貨、退款
  // 6: 訂單取消（消費者）
  num status;
  // 取消理由
  String cancelReason;

  OrdersOrderIdStatusPutReq copyWith({
    num status,
    String cancelReason,
  }) =>
      OrdersOrderIdStatusPutReq(
        status: status ?? this.status,
        cancelReason: cancelReason ?? this.cancelReason,
      );

  factory OrdersOrderIdStatusPutReq.fromRawJson(String str) =>
      OrdersOrderIdStatusPutReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrdersOrderIdStatusPutReq.fromJson(Map<String, dynamic> json) =>
      OrdersOrderIdStatusPutReq(
        status: json["status"] == null ? null : json["status"],
        cancelReason:
            json["cancel_reason"] == null ? null : json["cancel_reason"],
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "cancel_reason": cancelReason == null ? null : cancelReason,
      };
}
