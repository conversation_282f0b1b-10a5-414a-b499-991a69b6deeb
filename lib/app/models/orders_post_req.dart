import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:muyipork/app/models/multiple_payment.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/extension.dart';

class OrdersPostReq {
  OrdersPostReq({
    this.memberId,
    this.table1Id,
    this.table2Id,
    this.adult,
    this.child,
    this.type,
    this.source,
    this.subtotal,
    this.fee = 0,
    this.discount = 0,
    this.additionalCharges = 0,
    this.redeemMemberPoints,
    this.pointDiscountLimit,
    this.pointGet,
    this.memberCouponId,
    this.memberCouponDiscount,
    this.memberCouponExtraPrice,
    this.total,
    this.paid = 0,
    this.change,
    this.paymentStatus,
    this.status,
    this.multiplePayment,
    this.paymentMethodId,
    this.paymentFee,
    this.shippingMethodId,
    this.shippingFee,
    this.mealAt,
    this.comment = '',
    this.memo = '',
    this.invoice = true,
    this.invoiceType,
    this.invoiceInfo,
    this.invoiceNumber,
    this.randomNumber,
    this.invoicePaper,
    this.vatNumber = '',
    this.carrierType,
    this.carrierId = '',
    this.npoBan,
    this.buyerName,
    this.buyerPhone,
    this.buyerCityId,
    this.buyerCityareaId,
    this.buyerAddress,
    this.receiverName,
    this.receiverPhone,
    this.receiverCityId,
    this.receiverCityareaId,
    this.receiverAddress,
    this.items,
  });

  // 會員
  num memberId;
  // 桌位-區域編號 (也就是 PartitionId)
  num table1Id;
  // 桌位-桌號編號 (也就是 TableId)
  num table2Id;
  // 人數-大人
  num adult;
  // 人數-小孩
  num child;
  // 訂單類型
  // 0: 餐飲: 內用
  // 1: 餐飲: 店內->外帶 / 餐飲: 線上->自取
  // 2: 餐飲: 外送
  // 3: 零售: 自取
  // 4: 零售: 宅配
  // 5: 零售: 超商
  num type;
  // 來源
  // 0: 店員點餐
  // 1: LINE 點餐
  num source;
  // 小計
  num subtotal;
  // 服務費(%)
  num fee;
  // 現場折扣
  num discount;
  // 額外費用
  num additionalCharges;
  // 會員點數(使用)
  num redeemMemberPoints;
  // 會員點數折抵上限(點)
  num pointDiscountLimit;
  // 會員點數獲得(點)
  num pointGet;
  num memberCouponId;
  num memberCouponDiscount;
  num memberCouponExtraPrice;
  // 總金額
  num total;
  // 實收
  num paid;
  // 找零
  num change;
  // 付款狀態
  // 0: 未付款
  // 1: 未結清
  // 2: 已付款
  // 3: 付款失敗
  // 4: 超過付款時間
  num paymentStatus;
  // 狀態
  // 0: 處理中
  // 1: 已確認
  // 2: 訂單完成
  // 3: 訂單取消 （店家）
  // 4: 訂單異常
  // 5: 訂單退貨、退款
  // 6: 訂單取消（消費者）
  num status;
  //多重支付方式
  List<MultiplePayment> multiplePayment;
  // 單一支付方式 (deprecated)
  // TODO: remove me
  num paymentMethodId;
  num paymentFee;
  num shippingMethodId;
  num shippingFee;
  // 取/用餐時間
  String mealAt;
  // 店家備註
  String comment;
  // 消費者備註
  String memo;
  // 是否開立發票
  bool invoice;
  num invoiceType;
  InvoiceInfo invoiceInfo;
  // 發票號碼
  String invoiceNumber;
  // 發票隨機碼
  String randomNumber;
  // 是否印出紙本發票
  bool invoicePaper;
  // 統編
  String vatNumber;
  // 載具類別
  // 0: 悠遊卡
  // 1: 一卡通
  // 2: icash
  // 3: 手機
  // 4: 自然人憑證
  // 5: 金融卡
  // 6: 公用事業
  // 7: 信用卡
  // 8: 會員
  num carrierType;
  // 信用卡: 當次刷 卡日期+刷卡金額。 手機: 必須以 / 為起始作為判斷,
  // 目前總長度共為 8 碼， 條碼內容物除第 1 碼外只會有
  // 0123456789 ABCDEFGHIJKLMNOPQRSTUVWXYZ + - . 這39個字元。
  // 自然人憑證: 2位 大寫字母+14 位數字。
  String carrierId;
  // 愛心碼
  String npoBan;
  // 訂購者姓名（內用、外帶、自取用）
  String buyerName;
  // 訂購者電話（內用、外帶、自取用）
  String buyerPhone;
  num buyerCityId;
  num buyerCityareaId;
  String buyerAddress;
  // 收件者姓名
  String receiverName;
  // 收件者電話
  String receiverPhone;
  // 收件者城市編號
  num receiverCityId;
  // 收件者區域編號
  num receiverCityareaId;
  // 收件者地址
  String receiverAddress;
  // 訂單產品
  List<OrderItem> items;

  OrdersPostReq copyWith({
    int memberId,
    int table1Id,
    int table2Id,
    int adult,
    int child,
    int type,
    int source,
    int subtotal,
    int fee,
    int discount,
    int additionalCharges,
    int redeemMemberPoints,
    int pointDiscountLimit,
    int pointGet,
    int memberCouponId,
    int memberCouponDiscount,
    int memberCouponExtraPrice,
    int total,
    int paid,
    int change,
    int paymentStatus,
    int status,
    List<MultiplePayment> multiplePayment,
    int paymentMethodId,
    int paymentFee,
    int shippingMethodId,
    int shippingFee,
    String mealAt,
    String comment,
    String memo,
    bool invoice,
    int invoiceType,
    InvoiceInfo invoiceInfo,
    String invoiceNumber,
    String randomNumber,
    bool invoicePaper,
    String vatNumber,
    String carrierType,
    String carrierId,
    String npoBan,
    String buyerName,
    String buyerPhone,
    int buyerCityId,
    int buyerCityareaId,
    String buyerAddress,
    String receiverName,
    String receiverPhone,
    int receiverCityId,
    int receiverCityareaId,
    String receiverAddress,
    List<OrderItem> items,
  }) =>
      OrdersPostReq(
        memberId: memberId ?? this.memberId,
        table1Id: table1Id ?? this.table1Id,
        table2Id: table2Id ?? this.table2Id,
        adult: adult ?? this.adult,
        child: child ?? this.child,
        type: type ?? this.type,
        source: source ?? this.source,
        subtotal: subtotal ?? this.subtotal,
        fee: fee ?? this.fee,
        discount: discount ?? this.discount,
        additionalCharges: additionalCharges ?? this.additionalCharges,
        redeemMemberPoints: redeemMemberPoints ?? this.redeemMemberPoints,
        pointDiscountLimit: pointDiscountLimit ?? this.pointDiscountLimit,
        pointGet: pointGet ?? this.pointGet,
        memberCouponId: memberCouponId ?? this.memberCouponId,
        memberCouponDiscount: memberCouponDiscount ?? this.memberCouponDiscount,
        memberCouponExtraPrice:
            memberCouponExtraPrice ?? this.memberCouponExtraPrice,
        total: total ?? this.total,
        paid: paid ?? this.paid,
        change: change ?? this.change,
        paymentStatus: paymentStatus ?? this.paymentStatus,
        status: status ?? this.status,
        multiplePayment: multiplePayment ?? this.multiplePayment,
        paymentMethodId: paymentMethodId ?? this.paymentMethodId,
        paymentFee: paymentFee ?? this.paymentFee,
        shippingMethodId: shippingMethodId ?? this.shippingMethodId,
        shippingFee: shippingFee ?? this.shippingFee,
        mealAt: mealAt ?? this.mealAt,
        comment: comment ?? this.comment,
        memo: memo ?? this.memo,
        invoice: invoice ?? this.invoice,
        invoiceType: invoiceType ?? this.invoiceType,
        invoiceInfo: invoiceInfo ?? this.invoiceInfo,
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        randomNumber: randomNumber ?? this.randomNumber,
        invoicePaper: invoicePaper ?? this.invoicePaper,
        vatNumber: vatNumber ?? this.vatNumber,
        carrierType: carrierType ?? this.carrierType,
        carrierId: carrierId ?? this.carrierId,
        npoBan: npoBan ?? this.npoBan,
        buyerName: buyerName ?? this.buyerName,
        buyerPhone: buyerPhone ?? this.buyerPhone,
        buyerCityId: buyerCityId ?? this.buyerCityId,
        buyerCityareaId: buyerCityareaId ?? this.buyerCityareaId,
        buyerAddress: buyerAddress ?? this.buyerAddress,
        receiverName: receiverName ?? this.receiverName,
        receiverPhone: receiverPhone ?? this.receiverPhone,
        receiverCityId: receiverCityId ?? this.receiverCityId,
        receiverCityareaId: receiverCityareaId ?? this.receiverCityareaId,
        receiverAddress: receiverAddress ?? this.receiverAddress,
        items: items ?? this.items,
      );

  factory OrdersPostReq.fromRawJson(String str) =>
      OrdersPostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrdersPostReq.fromJson(Map<String, dynamic> json) => OrdersPostReq(
        memberId: json["member_id"] == null ? null : json["member_id"],
        table1Id: json["table1_id"] == null ? null : json["table1_id"],
        table2Id: json["table2_id"] == null ? null : json["table2_id"],
        adult: json["adult"] == null ? null : json["adult"],
        child: json["child"] == null ? null : json["child"],
        type: json["type"] == null ? null : json["type"],
        source: json["source"] == null ? null : json["source"],
        subtotal: json["subtotal"] == null ? null : json["subtotal"],
        fee: json["fee"] == null ? null : json["fee"],
        discount: json["discount"] == null ? null : json["discount"],
        additionalCharges: json["additional_charges"] == null
            ? null
            : json["additional_charges"],
        redeemMemberPoints: json["redeem_member_points"] == null
            ? null
            : json["redeem_member_points"],
        pointDiscountLimit: json["point_discount_limit"] == null
            ? null
            : json["point_discount_limit"],
        pointGet: json["point_get"] == null ? null : json["point_get"],
        memberCouponId:
            json["member_coupon_id"] == null ? null : json["member_coupon_id"],
        memberCouponDiscount: json["member_coupon_discount"] == null
            ? null
            : json["member_coupon_discount"],
        memberCouponExtraPrice: json["member_coupon_extra_price"] == null
            ? null
            : json["member_coupon_extra_price"],
        total: json["total"] == null ? null : json["total"],
        paid: json["paid"] == null ? null : json["paid"],
        change: json["change"] == null ? null : json["change"],
        paymentStatus:
            json["payment_status"] == null ? null : json["payment_status"],
        status: json["status"] == null ? null : json["status"],
        multiplePayment: json["multiple_payment"] == null
            ? null
            : List<MultiplePayment>.from(json["multiple_payment"]
                .map((x) => MultiplePayment.fromJson(x))),
        paymentMethodId: json["payment_method_id"] == null
            ? null
            : json["payment_method_id"],
        paymentFee: json["payment_fee"] == null ? null : json["payment_fee"],
        shippingMethodId: json["shipping_method_id"] == null
            ? null
            : json["shipping_method_id"],
        shippingFee: json["shipping_fee"] == null ? null : json["shipping_fee"],
        mealAt: json["meal_at"] == null ? null : json["meal_at"],
        comment: json["comment"] == null ? null : json["comment"],
        memo: json["memo"] == null ? null : json["memo"],
        invoice: json["invoice"] == null ? null : json["invoice"],
        invoiceType: json["invoice_type"] == null ? null : json["invoice_type"],
        invoiceInfo: json["invoice_info"] == null
            ? null
            : InvoiceInfo.fromJson(json["invoice_info"]),
        invoiceNumber:
            json["invoice_number"] == null ? null : json["invoice_number"],
        randomNumber:
            json["random_number"] == null ? null : json["random_number"],
        invoicePaper:
            json["invoice_paper"] == null ? null : json["invoice_paper"],
        vatNumber: json["vat_number"] == null ? null : json["vat_number"],
        carrierType: json["carrier_type"] == null ? null : json["carrier_type"],
        carrierId: json["carrier_id"] == null ? null : json["carrier_id"],
        npoBan: json["npo_ban"] == null ? null : json["npo_ban"],
        buyerName: json["buyer_name"] == null ? null : json["buyer_name"],
        buyerPhone: json["buyer_phone"] == null ? null : json["buyer_phone"],
        buyerCityId:
            json["buyer_city_id"] == null ? null : json["buyer_city_id"],
        buyerCityareaId: json["buyer_cityarea_id"] == null
            ? null
            : json["buyer_cityarea_id"],
        buyerAddress:
            json["buyer_address"] == null ? null : json["buyer_address"],
        receiverName:
            json["receiver_name"] == null ? null : json["receiver_name"],
        receiverPhone:
            json["receiver_phone"] == null ? null : json["receiver_phone"],
        receiverCityId:
            json["receiver_city_id"] == null ? null : json["receiver_city_id"],
        receiverCityareaId: json["receiver_cityarea_id"] == null
            ? null
            : json["receiver_cityarea_id"],
        receiverAddress:
            json["receiver_address"] == null ? null : json["receiver_address"],
        items: json["items"] == null
            ? null
            : List<OrderItem>.from(
                json["items"].map((x) => OrderItem.fromJson(x))),
      );

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{
      "member_id": memberId == null ? null : memberId,
      "table1_id": table1Id == null ? null : table1Id,
      "table2_id": table2Id == null ? null : table2Id,
      "adult": adult == null ? null : adult,
      "child": child == null ? null : child,
      "type": type == null ? null : type,
      "source": source == null ? null : source,
      "subtotal": subtotal == null ? null : subtotal.toInt(),
      "fee": fee == null ? null : fee,
      "discount": discount == null ? null : discount.toInt(),
      "additional_charges":
          additionalCharges == null ? null : additionalCharges.toInt(),
      "redeem_member_points":
          redeemMemberPoints == null ? null : redeemMemberPoints.toInt(),
      "point_discount_limit":
          pointDiscountLimit == null ? null : pointDiscountLimit.toInt(),
      "point_get": pointGet == null ? null : pointGet.toInt(),
      "member_coupon_id": memberCouponId == null ? null : memberCouponId,
      "member_coupon_discount":
          memberCouponDiscount == null ? null : memberCouponDiscount.toInt(),
      "member_coupon_extra_price": memberCouponExtraPrice == null
          ? null
          : memberCouponExtraPrice.toInt(),
      "total": total == null ? null : total.toInt(),
      "paid": paid == null ? null : paid.toInt(),
      "change": change == null ? null : change.toInt(),
      "payment_status": paymentStatus == null ? null : paymentStatus,
      "status": status == null ? null : status,
      // "multiple_payment": multiplePayment == null
      //     ? null
      //     : List<dynamic>.from(multiplePayment.map((x) => x.toJson())),
      "payment_method_id": paymentMethodId == null ? null : paymentMethodId,
      "payment_fee": paymentFee == null ? null : paymentFee,
      "shipping_method_id": shippingMethodId == null ? null : shippingMethodId,
      "shipping_fee": shippingFee == null ? null : shippingFee,
      "meal_at": mealAt == null ? null : mealAt,
      "comment": comment == null ? null : comment,
      "memo": memo == null ? null : memo,
      "invoice": invoice == null ? null : invoice,
      "invoice_type": invoiceType == null ? null : invoiceType,
      "invoice_info": invoiceInfo == null ? null : invoiceInfo.toJson(),
      "invoice_number": invoiceNumber == null ? null : invoiceNumber,
      "random_number": randomNumber == null ? null : randomNumber,
      "invoice_paper": invoicePaper == null ? null : invoicePaper,
      "vat_number": vatNumber == null ? null : vatNumber,
      "carrier_type": carrierType == null ? null : carrierType,
      "carrier_id": carrierId == null ? null : carrierId,
      "npo_ban": npoBan == null ? null : npoBan,
      "buyer_name": buyerName == null ? null : buyerName,
      "buyer_phone": buyerPhone == null ? null : buyerPhone,
      "buyer_city_id": buyerCityId == null ? null : buyerCityId,
      "buyer_cityarea_id": buyerCityareaId == null ? null : buyerCityareaId,
      "buyer_address": buyerAddress == null ? null : buyerAddress,
      "receiver_name": receiverName == null ? null : receiverName,
      "receiver_phone": receiverPhone == null ? null : receiverPhone,
      "receiver_city_id": receiverCityId == null ? null : receiverCityId,
      "receiver_cityarea_id":
          receiverCityareaId == null ? null : receiverCityareaId,
      "receiver_address": receiverAddress == null ? null : receiverAddress,
      // "items": items == null
      //     ? null
      //     : List<dynamic>.from(items.map((x) => x.toJson())),
    };
    if (items != null) {
      // data['items'] = items.map((e) => e.toJson(ignoreIdAndCatIDs: true)).toList();
      data['items'] = jsonEncode(items.map((e) => e.toJson()).toList());
    }
    if (multiplePayment != null && multiplePayment.isNotEmpty) {
      final ls = multiplePayment.map((e) => e.toJson()).toList();
      data['multiple_payment'] = jsonEncode(ls);
    }
    return data;
  }

  // TODO: remove this, use containsTable instead
  // 是否已經選擇了合法的桌號
  bool hasSelectLegalTable() => containsTable;

  bool get containsTable {
    return (table1Id is num) || (table2Id is num);
  }

  // 當前的訂單 type 是否需要選擇桌號?
  bool shouldSelectTable() {
    if (type == MEAL_ORDER_TYPE_EAT_IN ||
        type == MEAL_ORDER_TYPE_TAKE_AWAY_OR_FETCH_AWAY) {
      return true;
    }
    return false;
  }

  // This will paste some reference data from an existing Order data.
  pasteRef(OrderSummary order) {
    adult = order.adult;
    child = order.child;
    source = order.source;
    table1Id = order.table1Id;
    table2Id = order.table2Id;
    type = order.type;
  }

  // 嘗試轉換出可以編輯用的日期 DateTime (只給日期)
  // 有可能回傳 null
  DateTime getMealAtLocalDate() {
    if (mealAt != null) {
      DateTime mealAtDateTime =
          DateFormat('yyyy-MM-dd HH:mm:ss').parseUTC(mealAt);
      if (mealAtDateTime != null) {
        return DateTime(
                mealAtDateTime.year, mealAtDateTime.month, mealAtDateTime.day)
            .toLocal();
      }
    }
    return null;
  }

  // 嘗試轉換出可以編輯用的用餐時間 TimeOfDay (只給時分)
  // 有可能回傳 null
  TimeOfDay getMealLocalTimeOfDay() {
    if (mealAt != null) {
      DateTime mealAtDateTime =
          DateFormat('yyyy-MM-dd HH:mm:ss').parseUTC(mealAt);
      if (mealAtDateTime != null) {
        return TimeOfDay.fromDateTime(mealAtDateTime.toLocal());
      }
    }
    return null;
  }

  // 嘗試轉換出可以編輯用的日期時間
  DateTime getMealAtLocalDateTime() {
    if (mealAt != null) {
      return DateFormat('yyyy-MM-dd HH:mm:ss').parseUTC(mealAt).toLocal();
    }
    return null;
  }

  // 這會移除所有 quantity 為 0 的 OrderItem
  ensureNoZeroQuantityItem() {
    items ??= <OrderItem>[];
    for (int i = items.length - 1; i >= 0; i--) {
      if (items[i].quantity != null && items[i].quantity <= 0) {
        items.removeAt(i);
      }
    }
  }
}
