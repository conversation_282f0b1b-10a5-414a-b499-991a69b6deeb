// To parse this JSON data, do
//
//     final payMethod = payMethodFromJson(jsonString);

import 'dart:convert';

class PayMethod {
  PayMethod({
    this.id,
    this.name,
    this.status,
    this.type,
  });

  num id;
  String name;
  num status;
  num type;

  PayMethod copyWith({
    num id,
    String name,
    num status,
    num type,
  }) =>
      PayMethod(
        id: id ?? this.id,
        name: name ?? this.name,
        status: status ?? this.status,
        type: type ?? this.type,
      );

  factory PayMethod.fromRawJson(String str) =>
      PayMethod.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PayMethod.fromJson(Map<String, dynamic> json) => PayMethod(
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
        status: json["status"] == null ? null : json["status"],
        type: json["type"] == null ? null : json["type"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "name": name == null ? null : name,
        "status": status == null ? null : status,
        "type": type == null ? null : type,
      };
}
