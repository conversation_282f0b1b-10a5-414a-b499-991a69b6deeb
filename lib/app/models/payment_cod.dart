// To parse this JSON data, do
//
//     final paymentCod = paymentCodFromJson(jsonString);

import 'dart:convert';

class PaymentCod {
  PaymentCod({
    this.description,
    this.fee,
    this.free,
    this.status,
  });

  String description;
  num fee;
  num free;
  num status;

  PaymentCod copyWith({
    String description,
    num fee,
    num free,
    num status,
  }) =>
      PaymentCod(
        description: description ?? this.description,
        fee: fee ?? this.fee,
        free: free ?? this.free,
        status: status ?? this.status,
      );

  factory PaymentCod.fromRawJson(String str) =>
      PaymentCod.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentCod.fromJson(Map<String, dynamic> json) => PaymentCod(
        description: json["description"] == null ? null : json["description"],
        fee: json["fee"] == null ? null : json["fee"],
        free: json["free"] == null ? null : json["free"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "fee": fee == null ? null : fee,
        "free": free == null ? null : free,
        "status": status == null ? null : status,
      };
}
