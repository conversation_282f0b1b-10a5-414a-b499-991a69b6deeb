// To parse this JSON data, do
//
//     final paymentEcpayPut = paymentEcpayPutFromJson(jsonString);

import 'dart:convert';

class PaymentEcpayPut {
  PaymentEcpayPut({
    this.merchantId,
    this.hashKey,
    this.hashIv,
    this.description,
    this.creditStatus,
  });

  String merchantId;
  String hashKey;
  String hashIv;
  String description;
  num creditStatus;

  PaymentEcpayPut copyWith({
    String merchantId,
    String hashKey,
    String hashIv,
    String description,
    num creditStatus,
  }) =>
      PaymentEcpayPut(
        merchantId: merchantId ?? this.merchantId,
        hashKey: hashKey ?? this.hashKey,
        hashIv: hashIv ?? this.hashIv,
        description: description ?? this.description,
        creditStatus: creditStatus ?? this.creditStatus,
      );

  factory PaymentEcpayPut.fromRawJson(String str) =>
      PaymentEcpayPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentEcpayPut.fromJson(Map<String, dynamic> json) =>
      PaymentEcpayPut(
        merchantId: json["merchant_id"] == null ? null : json["merchant_id"],
        hashKey: json["hash_key"] == null ? null : json["hash_key"],
        hashIv: json["hash_iv"] == null ? null : json["hash_iv"],
        description: json["description"] == null ? null : json["description"],
        creditStatus:
            json["credit_status"] == null ? null : json["credit_status"],
      );

  Map<String, dynamic> toJson() => {
        "merchant_id": merchantId == null ? null : merchantId,
        "hash_key": hashKey == null ? null : hashKey,
        "hash_iv": hashIv == null ? null : hashIv,
        "description": description == null ? null : description,
        "credit_status": creditStatus == null ? null : creditStatus,
      };
}
