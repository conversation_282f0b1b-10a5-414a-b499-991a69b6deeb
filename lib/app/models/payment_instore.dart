// To parse this JSON data, do
//
//     final paymentInstore = paymentInstoreFromJson(jsonString);

import 'dart:convert';

class PaymentInstore {
  PaymentInstore({
    this.brandPayMethodId,
    this.description,
    this.descriptionDiner,
    this.fee,
    this.free,
    this.id,
    this.isCustom,
    this.name,
    this.payMethodId,
    this.setting,
    this.sort,
    this.status,
    this.statusDiner,
  });

  num brandPayMethodId;
  String description;
  String descriptionDiner;
  num fee;
  num free;
  num id;
  bool isCustom;
  String name;
  num payMethodId;
  String setting;
  num sort;
  num status;
  num statusDiner;

  PaymentInstore copyWith({
    num brandPayMethodId,
    String description,
    String descriptionDiner,
    num fee,
    num free,
    num id,
    bool isCustom,
    String name,
    num payMethodId,
    String setting,
    num sort,
    num status,
    num statusDiner,
  }) =>
      PaymentInstore(
        brandPayMethodId: brandPayMethodId ?? this.brandPayMethodId,
        description: description ?? this.description,
        descriptionDiner: descriptionDiner ?? this.descriptionDiner,
        fee: fee ?? this.fee,
        free: free ?? this.free,
        id: id ?? this.id,
        isCustom: isCustom ?? this.isCustom,
        name: name ?? this.name,
        payMethodId: payMethodId ?? this.payMethodId,
        setting: setting ?? this.setting,
        sort: sort ?? this.sort,
        status: status ?? this.status,
        statusDiner: statusDiner ?? this.statusDiner,
      );

  factory PaymentInstore.fromRawJson(String str) =>
      PaymentInstore.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentInstore.fromJson(Map<String, dynamic> json) => PaymentInstore(
        brandPayMethodId: json["brand_pay_method_id"] == null
            ? null
            : json["brand_pay_method_id"],
        description: json["description"] == null ? null : json["description"],
        descriptionDiner: json["description_diner"] == null
            ? null
            : json["description_diner"],
        fee: json["fee"] == null ? null : json["fee"],
        free: json["free"] == null ? null : json["free"],
        id: json["id"] == null ? null : json["id"],
        isCustom: json["is_custom"] == null ? null : json["is_custom"],
        name: json["name"] == null ? null : json["name"],
        payMethodId:
            json["pay_method_id"] == null ? null : json["pay_method_id"],
        setting: json["setting"] == null ? null : json["setting"],
        sort: json["sort"] == null ? null : json["sort"],
        status: json["status"] == null ? null : json["status"],
        statusDiner: json["status_diner"] == null ? null : json["status_diner"],
      );

  Map<String, dynamic> toJson() => {
        "brand_pay_method_id":
            brandPayMethodId == null ? null : brandPayMethodId,
        "description": description == null ? null : description,
        "description_diner": descriptionDiner == null ? null : descriptionDiner,
        "fee": fee == null ? null : fee,
        "free": free == null ? null : free,
        "id": id == null ? null : id,
        "is_custom": isCustom == null ? null : isCustom,
        "name": name == null ? null : name,
        "pay_method_id": payMethodId == null ? null : payMethodId,
        "setting": setting == null ? null : setting,
        "sort": sort == null ? null : sort,
        "status": status == null ? null : status,
        "status_diner": statusDiner == null ? null : statusDiner,
      };
}
