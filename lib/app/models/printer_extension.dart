// To parse this JSON data, do
//
//     final printerExtension = printerExtensionFromJson(jsonString);

import 'dart:convert';

import 'printer_task.dart';

class PrinterExtension {
  num availableAt;
  num times;
  num printing;
  List<PrinterTask> tasks;

  PrinterExtension({
    this.availableAt,
    this.times,
    this.printing,
    this.tasks,
  });

  PrinterExtension copyWith({
    num availableAt,
    num times,
    num printing,
    List<PrinterTask> tasks,
  }) =>
      PrinterExtension(
        availableAt: availableAt ?? this.availableAt,
        times: times ?? this.times,
        printing: printing ?? this.printing,
        tasks: tasks ?? this.tasks,
      );

  factory PrinterExtension.fromRawJson(String str) =>
      PrinterExtension.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PrinterExtension.fromJson(Map<String, dynamic> json) =>
      PrinterExtension(
        availableAt: json["available_at"],
        times: json["times"],
        printing: json["printing"],
        tasks: json["tasks"] == null
            ? []
            : List<PrinterTask>.from(
                json["tasks"].map((x) => PrinterTask.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "available_at": availableAt,
        "times": times,
        "printing": printing,
        "tasks": tasks == null
            ? []
            : List<dynamic>.from(tasks.map((x) => x.toJson())),
      };
}
