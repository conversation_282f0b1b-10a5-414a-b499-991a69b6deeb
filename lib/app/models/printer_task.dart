import 'dart:convert';

class PrinterTask {
  num printerId;
  num createdAt;
  num status;
  List<int> bytes;

  PrinterTask({
    this.printerId,
    this.createdAt,
    this.status,
    this.bytes,
  });

  PrinterTask copyWith({
    num printerId,
    num createdAt,
    num status,
    List<int> bytes,
  }) =>
      PrinterTask(
        printerId: printerId ?? this.printerId,
        createdAt: createdAt ?? this.createdAt,
        status: status ?? this.status,
        bytes: bytes ?? this.bytes,
      );

  factory PrinterTask.fromRawJson(String str) =>
      PrinterTask.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PrinterTask.fromJson(Map<String, dynamic> json) => PrinterTask(
        printerId: json["printer_id"],
        createdAt: json["created_at"],
        status: json["status"],
        bytes: json["bytes"] == null
            ? []
            : List<int>.from(json["bytes"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "printer_id": printerId,
        "created_at": createdAt,
        "status": status,
        "bytes": bytes == null ? [] : List<dynamic>.from(bytes.map((x) => x)),
      };
}
