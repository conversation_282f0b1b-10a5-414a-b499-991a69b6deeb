// To parse this JSON data, do
//
//     final productsGetQry = productsGetQryFromJson(jsonString);

import 'dart:convert';

class ProductsGetQry {
  ProductsGetQry({
    this.page,
    this.limit,
    this.categoryId,
    this.kind,
  });

  num page;
  num limit;
  num categoryId;
  num kind;

  ProductsGetQry copyWith({
    num page,
    num limit,
    num categoryId,
    num kind,
  }) =>
      ProductsGetQry(
        page: page ?? this.page,
        limit: limit ?? this.limit,
        categoryId: categoryId ?? this.categoryId,
        kind: kind ?? this.kind,
      );

  factory ProductsGetQry.fromRawJson(String str) =>
      ProductsGetQry.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsGetQry.fromJson(Map<String, dynamic> json) => ProductsGetQry(
        page: json["page"] == null ? null : json["page"],
        limit: json["limit"] == null ? null : json["limit"],
        categoryId: json["category_id"] == null ? null : json["category_id"],
        kind: json["kind"] == null ? null : json["kind"],
      );

  Map<String, dynamic> toJson() => {
        "page": page == null ? null : page,
        "limit": limit == null ? null : limit,
        "category_id": categoryId == null ? null : categoryId,
        "kind": kind == null ? null : kind,
      };
}
