import 'res_base.dart';
import 'package:dio/dio.dart';

class ProductsProductIdMenuGetRes extends ResBase {
  VProductSingle data;

  ProductsProductIdMenuGetRes({this.data});

  ProductsProductIdMenuGetRes.resError(Response response)
      : super.resError(response);
  ProductsProductIdMenuGetRes.unKnownError() : super.unKnownError();

  ProductsProductIdMenuGetRes.fromJson(Map<String, dynamic> json) {
    data =
        json['data'] != null ? new VProductSingle.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data.toJson();
    }
    return data;
  }
}

class VProductSingle {
  int id;
  int price;
  int stock;
  String summary;
  String title;
  List<VProductAdditionCategory> vProductAdditionCategories;

  VProductSingle(
      {this.id,
      this.price,
      this.stock,
      this.summary,
      this.title,
      this.vProductAdditionCategories});

  VProductSingle.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    price = json['price'];
    stock = json['stock'];
    summary = json['summary'];
    title = json['title'];
    if (json['vproduct_addition_categories'] != null) {
      vProductAdditionCategories = [];
      json['vproduct_addition_categories'].forEach((v) {
        vProductAdditionCategories
            .add(new VProductAdditionCategory.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['price'] = this.price;
    data['stock'] = this.stock;
    data['summary'] = this.summary;
    data['title'] = this.title;
    if (this.vProductAdditionCategories != null) {
      data['vproduct_addition_categories'] =
          this.vProductAdditionCategories.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class VProductAdditionCategory {
  List<AdditionProductSetting> items;
  int option;
  int optionMax;
  int optionMin;
  int productId;
  int required;
  num sort;
  String title;

  VProductAdditionCategory(
      {this.items,
      this.option,
      this.optionMax,
      this.optionMin,
      this.productId,
      this.required,
      this.sort,
      this.title});

  VProductAdditionCategory.fromJson(Map<String, dynamic> json) {
    items = [];
    if (json['items'] != null) {
      json['items'].forEach((v) {
        items.add(new AdditionProductSetting.fromJson(v));
      });
    }
    option = json['option'];
    optionMax = json['option_max'];
    optionMin = json['option_min'];
    productId = json['product_id'];
    required = json['required'];
    sort = json['sort'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.items != null) {
      data['items'] = this.items.map((v) => v.toJson()).toList();
    }
    data['option'] = this.option;
    data['option_max'] = this.optionMax;
    data['option_min'] = this.optionMin;
    data['product_id'] = this.productId;
    data['required'] = this.required;
    data['sort'] = this.sort;
    data['title'] = this.title;
    return data;
  }
}

class AdditionProductSetting {
  int additionCategoryId;
  int id;
  int kind;
  String name;
  int price;
  num sort;

  AdditionProductSetting(
      {this.additionCategoryId,
      this.id,
      this.kind,
      this.name,
      this.price,
      this.sort});

  AdditionProductSetting.fromJson(Map<String, dynamic> json) {
    additionCategoryId = json['addition_category_id'];
    id = json['id'];
    kind = json['kind'];
    name = json['name'];
    price = json['price'];
    sort = json['sort'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['addition_category_id'] = this.additionCategoryId;
    data['id'] = this.id;
    data['kind'] = this.kind;
    data['name'] = this.name;
    data['price'] = this.price;
    data['sort'] = this.sort;
    return data;
  }
}
