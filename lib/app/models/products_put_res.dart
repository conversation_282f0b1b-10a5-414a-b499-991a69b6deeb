import 'res_base.dart';
import 'package:dio/dio.dart';

class ProductsPutRes extends ResBase {
  bool isUpdated;
  int productId;

  ProductsPutRes({this.isUpdated, this.productId});

  ProductsPutRes.resError(Response response) : super.resError(response);
  ProductsPutRes.unKnownError() : super.unKnownError();

  ProductsPutRes.fromJson(Map<String, dynamic> json) {
    isUpdated = json['is_updated'];
    productId = json['product_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['product_id'] = this.productId;
    return data;
  }
}