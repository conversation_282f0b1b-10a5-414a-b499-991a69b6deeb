// To parse this JSON data, do
//
//     final qrFormat = qrFormatFromJson(jsonString);

import 'dart:convert';

class QrFormat {
  QrFormat({
    this.type,
    this.memberId,
    this.orderId,
    this.memberCouponId,
    this.memberQuestionnaireId,
  });

  String type;
  num memberId;
  num orderId;
  num memberCouponId;
  num memberQuestionnaireId;

  QrFormat copyWith({
    String type,
    num memberId,
    num orderId,
    num memberCouponId,
    num memberQuestionnaireId,
  }) =>
      QrFormat(
        type: type ?? this.type,
        memberId: memberId ?? this.memberId,
        orderId: orderId ?? this.orderId,
        memberCouponId: memberCouponId ?? this.memberCouponId,
        memberQuestionnaireId:
            memberQuestionnaireId ?? this.memberQuestionnaireId,
      );

  factory QrFormat.fromRawJson(String str) =>
      QrFormat.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory QrFormat.fromJson(Map<String, dynamic> json) => QrFormat(
        type: json["type"] == null ? null : json["type"],
        memberId: json["member_id"] == null ? null : json["member_id"],
        orderId: json["order_id"] == null ? null : json["order_id"],
        memberCouponId:
            json["member_coupon_id"] == null ? null : json["member_coupon_id"],
        memberQuestionnaireId: json["member_questionnaire_id"] == null
            ? null
            : json["member_questionnaire_id"],
      );

  Map<String, dynamic> toJson() => {
        "type": type == null ? null : type,
        "member_id": memberId == null ? null : memberId,
        "order_id": orderId == null ? null : orderId,
        "member_coupon_id": memberCouponId == null ? null : memberCouponId,
        "member_questionnaire_id":
            memberQuestionnaireId == null ? null : memberQuestionnaireId,
      };
}
