// To parse this JSON data, do
//
//     final reportsSales = reportsSalesFromJson(jsonString);

import 'dart:convert';

import 'package:okshop_model/okshop_model.dart';

class ReportsSales {
  ReportsSales({
    this.app,
    this.online,
    this.print,
  });

  List<App> app;
  List<App> online;
  Print print;

  ReportsSales copyWith({
    List<App> app,
    List<App> online,
    Print print,
  }) =>
      ReportsSales(
        app: app ?? this.app,
        online: online ?? this.online,
        print: print ?? this.print,
      );

  factory ReportsSales.fromRawJson(String str) =>
      ReportsSales.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ReportsSales.fromJson(Map<String, dynamic> json) => ReportsSales(
        app: json["app"] == null
            ? null
            : List<App>.from(json["app"].map((x) => App.fromJson(x))),
        online: json["online"] == null
            ? null
            : List<App>.from(json["online"].map((x) => App.fromJson(x))),
        print: json["print"] == null ? null : Print.fromJson(json["print"]),
      );

  Map<String, dynamic> toJson() => {
        "app":
            app == null ? null : List<dynamic>.from(app.map((x) => x.toJson())),
        "online": online == null
            ? null
            : List<dynamic>.from(online.map((x) => x.toJson())),
        "print": print == null ? null : print.toJson(),
      };
}

// class App {
//   App({
//     this.price,
//     this.productTitle,
//     this.quantity,
//     this.total,
//     this.updatedAt,
//   });

//   num price;
//   String productTitle;
//   num quantity;
//   num total;
//   String updatedAt;

//   App copyWith({
//     num price,
//     String productTitle,
//     num quantity,
//     num total,
//     String updatedAt,
//   }) =>
//       App(
//         price: price ?? this.price,
//         productTitle: productTitle ?? this.productTitle,
//         quantity: quantity ?? this.quantity,
//         total: total ?? this.total,
//         updatedAt: updatedAt ?? this.updatedAt,
//       );

//   factory App.fromRawJson(String str) => App.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory App.fromJson(Map<String, dynamic> json) => App(
//         price: json["price"] == null ? null : json["price"],
//         productTitle:
//             json["product_title"] == null ? null : json["product_title"],
//         quantity: json["quantity"] == null ? null : json["quantity"],
//         total: json["total"] == null ? null : json["total"],
//         updatedAt: json["updated_at"] == null ? null : json["updated_at"],
//       );

//   Map<String, dynamic> toJson() => {
//         "price": price == null ? null : price,
//         "product_title": productTitle == null ? null : productTitle,
//         "quantity": quantity == null ? null : quantity,
//         "total": total == null ? null : total,
//         "updated_at": updatedAt == null ? null : updatedAt,
//       };
// }

class Print {
  Print({
    this.lastDate,
    this.storeAccountName,
  });

  String lastDate;
  String storeAccountName;

  Print copyWith({
    String lastDate,
    String storeAccountName,
  }) =>
      Print(
        lastDate: lastDate ?? this.lastDate,
        storeAccountName: storeAccountName ?? this.storeAccountName,
      );

  factory Print.fromRawJson(String str) => Print.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Print.fromJson(Map<String, dynamic> json) => Print(
        lastDate: json["last_date"] == null ? null : json["last_date"],
        storeAccountName: json["store_account_name"] == null
            ? null
            : json["store_account_name"],
      );

  Map<String, dynamic> toJson() => {
        "last_date": lastDate == null ? null : lastDate,
        "store_account_name":
            storeAccountName == null ? null : storeAccountName,
      };
}
