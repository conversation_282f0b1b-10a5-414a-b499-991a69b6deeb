import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:muyipork/extension.dart';

//這些Server過來的錯誤會被 hasError 忽略 (無資料代號不算是錯誤!)
List<String> ignoredErrorCode = ['1104'];

class ResBase {
  //Http 狀態錯誤資訊
  StatusError statusError;
  //Server 自訂錯誤資訊
  ResError error;

  ResBase();

  //這會嘗試同時處理 Http Response 以及 Server 來的錯誤狀況。
  ResBase.resError(Response response) {
    if (response != null) {
      statusError = StatusError(
          statusCode: response.statusCode.toString(),
          statusMessage: response.statusMessage);
      if (response.data is Map<String, dynamic>) {
        //Try handle the server error code.
        Map<String, dynamic> json = response.data;
        if (json['error'] != null) {
          error = ResError.fromJson(json['error']);
          error.message = error.localMessage;
        }
      }
    }
  }

  ResBase.unKnownError() {
    error = ResError(code: '????', message: 'Unknown Error!');
  }

  //Construct use a server error json.
  ResBase.fromErrorJson(Map<String, dynamic> json) {
    if (json['error'] != null) {
      error = ResError.fromJson(json['error']);
    }
  }

  Map<String, dynamic> errorJson() {
    //Server error go first.
    if (error != null) {
      return error.toJson();
    }

    //Try StatusError next.
    if (statusError != null) {
      return statusError.toJson();
    }

    //The ultimate solution. If you use the design correctly you should not see this!
    return ResError(code: '????', message: 'Unknown Error!').toJson();
  }

  bool hasError() {
    return (error != null && !ignoredErrorCode.contains(error.code)) ||
        (statusError != null);
  }

  String formattedErrorStr() {
    String returnStr = '';
    if (error != null) {
      returnStr += '[' + error.code + '] ' + error.message + '\n';
    }
    if (statusError != null) {
      returnStr += '[' +
          statusError.statusCode +
          '] ' +
          statusError.statusMessage +
          '\n';
    }
    return returnStr;
  }
}

class StatusError {
  String statusCode;
  String statusMessage;

  StatusError({this.statusCode, this.statusMessage});

  StatusError.fromJson(Map<String, dynamic> json) {
    statusCode = json['status_code'];
    statusMessage = json['status_message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status_code'] = this.statusCode;
    data['status_message'] = this.statusMessage;
    return data;
  }
}

// To parse this JSON data, do
//
//     final resError = resErrorFromJson(jsonString);

class ResError {
  ResError({
    this.code,
    this.httpCode,
    this.message,
    this.messageDetail,
  });

  String code;
  num httpCode;
  String message;
  String messageDetail;

  ResError copyWith({
    String code,
    num httpCode,
    String message,
    String messageDetail,
  }) =>
      ResError(
        code: code ?? this.code,
        httpCode: httpCode ?? this.httpCode,
        message: message ?? this.message,
        messageDetail: messageDetail ?? this.messageDetail,
      );

  factory ResError.fromRawJson(String str) =>
      ResError.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ResError.fromJson(Map<String, dynamic> json) => ResError(
        code: json["code"] == null ? null : json["code"].toString(),
        httpCode: json["http_code"] == null ? null : json["http_code"],
        message: json["message"] == null ? null : json["message"],
        messageDetail:
            json["message_detail"] == null ? null : json["message_detail"],
      );

  Map<String, dynamic> toJson() => {
        "code": code == null ? null : code,
        "http_code": httpCode == null ? null : httpCode,
        "message": message == null ? null : message,
        "message_detail": messageDetail == null ? null : messageDetail,
      };
}
