import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:muyipork/app/models/setting_line_order.dart';
import 'package:muyipork/app/models/setting_order_auto.dart';
import 'package:muyipork/app/models/setting_order_fee.dart';
import 'package:muyipork/app/models/res_base.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/static_methods.dart';

class SettingGetRes extends ResBase {
  SettingGetData data;

  SettingGetRes({this.data}) {
    this.data ??= SettingGetData();
  }

  SettingGetRes.resError(Response response) : super.resError(response);
  SettingGetRes.unKnownError() : super.unKnownError();

  SettingGetRes.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? new SettingGetData.fromJson(json['data'])
        : SettingGetData(
            hours: SettingBusinessHours(),
            other: SettingOther(),
          );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data.toJson();
    }
    return data;
  }

  SettingGetData get nnData {
    data ??= SettingGetData();
    return data;
  }

  //確保編輯的資料正好是一整周的份 (7天 = 7格)
  //0 = Sunday
  ensureWeekDays() {
    if (data.hours == null) {
      data.hours = SettingBusinessHours(
        week: [],
        comment: '',
      );
    }

    if (data.hours.week == null) {
      data.hours.week = [];
    }
    for (int i = 0; i < 7; ++i) {
      if (i >= data.hours.week.length) {
        data.hours.week.add(SettingWeekDay(
          day: i + 1,
          status: 1,
          hours: [],
        ));
      } else {
        data.hours.week[i].day = i + 1;
      }
    }
  }
}

class SettingGetData {
  SettingBusinessHours hours;
  SettingOther other;

  SettingGetData({this.hours, this.other}) {
    this.hours ??= SettingBusinessHours();
    this.other ??= SettingOther();
  }

  SettingGetData.fromJson(Map<String, dynamic> json) {
    final v1 = json['business_hours'] != null
        ? new SettingBusinessHours.fromJson(jsonDecode(json['business_hours']))
        : null;
    final v2 = json['hours'] != null
        ? new SettingBusinessHours.fromJson(jsonDecode(json['hours']))
        : null;
    hours = v1 ?? v2 ?? SettingBusinessHours();
    // businessHours = json['business_hours'] != null
    //     ? new SettingBusinessHours.fromJson(jsonDecode(json['business_hours']))
    //     : SettingBusinessHours();
    // businessHours = json['hours'] != null
    //     ? new SettingBusinessHours.fromJson(jsonDecode(json['hours']))
    //     : SettingBusinessHours();
    other = json['other'] != null
        ? SettingOther.fromJson(jsonDecode(json['other']))
        : SettingOther();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.hours != null) {
      data['hours'] = jsonEncode(this.hours.toJson());
    }
    if (this.other != null) {
      data['other'] = jsonEncode(this.other.toJson());
    }
    return data;
  }

  SettingOther get nnOther {
    other ??= SettingOther();
    return other;
  }
}

class SettingBusinessHours {
  //Order: Monday to Sunday
  List<SettingWeekDay> week;
  String comment;

  SettingBusinessHours({
    this.week,
    this.comment = '',
  }) {
    this.week ??= <SettingWeekDay>[];
  }

  SettingBusinessHours.fromJson(Map<String, dynamic> json) {
    if (json['week'] != null) {
      week = [];
      json['week'].forEach((v) {
        week.add(new SettingWeekDay.fromJson(v));
      });
    }
    comment = json['comment'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.week != null) {
      data['week'] = this.week.map((v) => v.toJson()).toList();
    }
    data['comment'] = this.comment;
    return data;
  }

  //Maybe we just need one? But whatever.
  Iterable<OpenHoursDateTime> _getOpenHoursInOneWeek() sync* {
    final now = DateTime.now();
    const DAYS_IN_WEEK = 7;
    //weekday: Monday = 1 / Sunday = 7
    for (int i = 0; i < DAYS_IN_WEEK; i++) {
      final weekIndex = (now.weekday - 1 + i) % DAYS_IN_WEEK;
      final futureWeekDay = now.add(Duration(days: i));
      if (weekIndex < week.length) {
        final element = week.elementAt(weekIndex);
        yield* element.formOpenHoursAtDate(futureWeekDay);
      }
    }
  }

  //Search and decide a nearest future.
  //Possible return null. (If there isn't any business hours data)
  OpenHoursDateTime getNearestFutureHoursInOneWeek() {
    final now = DateTime.now();
    for (var timeRange in _getOpenHoursInOneWeek()) {
      if (now.isAfter(timeRange.end)) {
        // now 超過時段，略過
        continue;
      }
      // 返回時段內區間
      return timeRange;
    }
    //Cannot found a nearest time
    // print('Not nearest future hours found!');
    return null;
  }
}

//The actual business hours time for compare to Orders create time. (for filter in order_view)
class OpenHoursDateTime {
  const OpenHoursDateTime({
    @required this.start,
    @required this.end,
  })  : assert(start != null),
        assert(end != null);

  final DateTime start;
  final DateTime end;

  Duration get duration => end.difference(start);

  @override
  bool operator ==(Object other) {
    if (other.runtimeType != runtimeType) return false;
    return other is DateTimeRange && other.start == start && other.end == end;
  }

  @override
  int get hashCode => hashValues(start, end);

  @override
  String toString() => '$start - $end';

  bool contains(DateTime dateTime) {
    final _begin = start ?? end;
    final _end = end ?? start;
    return _begin.isBefore(dateTime) && _end.isAfter(dateTime);
  }

  bool outOfRange(DateTime dateTime) => !contains(dateTime);
}

class SettingWeekDay {
  num day;
  num status;
  List<SettingOpenHour> hours;

  SettingWeekDay({
    this.day = 0,
    this.status = 0,
    this.hours,
  }) {
    this.hours ??= <SettingOpenHour>[];
  }

  SettingWeekDay.fromJson(Map<String, dynamic> json) {
    day = json['day'];
    status = json['status'];
    hours = [];
    if (json['hours'] != null) {
      json['hours'].forEach((v) {
        hours.add(new SettingOpenHour.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['day'] = this.day;
    data['status'] = this.status;
    if (this.hours != null) {
      data['hours'] = this.hours.map((v) => v.toJson()).toList();
    }
    return data;
  }

  String dayDisplayName() {
    switch (day) {
      case 0:
        return '星期日';
      case 1:
        return '星期一';
      case 2:
        return '星期二';
      case 3:
        return '星期三';
      case 4:
        return '星期四';
      case 5:
        return '星期五';
      case 6:
        return '星期六';
      case 7:
        return '星期日';
      default:
        return '-';
    }
  }

  //Form open hours of the day
  Iterable<OpenHoursDateTime> formOpenHoursAtDate(DateTime atDate) {
    return hours.map((h) => h.formOpenHoursAtDate(atDate));
  }
}

class SettingOpenHour {
  SettingOpenHour({
    this.open,
    this.close,
  });

  String open;
  String close;

  SettingOpenHour copyWith({
    String open,
    String close,
  }) =>
      SettingOpenHour(
        open: open ?? this.open,
        close: close ?? this.close,
      );

  factory SettingOpenHour.fromRawJson(String str) =>
      SettingOpenHour.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SettingOpenHour.fromJson(Map<String, dynamic> json) =>
      SettingOpenHour(
        open: json["open"] == null ? null : json["open"],
        close: json["close"] == null ? null : json["close"],
      );

  Map<String, dynamic> toJson() => {
        "open": open == null ? null : open,
        "close": close == null ? null : close,
      };

  // 產生完整日期
  // Form a DateTime of an actual day.
  OpenHoursDateTime formOpenHoursAtDate(DateTime atDate) {
    TimeOfDay openTOD = tryParseToTimeOfDay(open);
    TimeOfDay closeTOD = tryParseToTimeOfDay(close);
    DateTime openDateTime = DateTime(
        atDate.year, atDate.month, atDate.day, openTOD.hour, openTOD.minute);
    DateTime closeDateTime = DateTime(
        atDate.year, atDate.month, atDate.day, closeTOD.hour, closeTOD.minute);

    OpenHoursDateTime formedData = OpenHoursDateTime(
      start: openDateTime,
      end: closeDateTime,
    );

    return formedData;
  }
}

class SettingOther {
  SettingLineOrder lineOrder;
  SettingOrderAuto auto;
  SettingOrderFee fee;
  num printOrder;
  num printDetail;

  // 0: 餐飲-前結帳
  // 1: 餐飲-後結帳
  // 2: 零售
  // 3: 餐飲-前結帳+零售
  // 4: 餐飲-後結帳+零售
  num checkoutType;

  SettingOther({
    this.lineOrder,
    this.auto,
    this.fee,
    this.printOrder = 0,
    this.printDetail = 0,
    this.checkoutType = 0,
  }) {
    this.lineOrder ??= SettingLineOrder();
    this.auto ??= SettingOrderAuto();
    this.fee ??= SettingOrderFee();
  }

  factory SettingOther.fromJson(Map<String, dynamic> json) => SettingOther(
        lineOrder: json['line_order'] == null
            ? SettingLineOrder()
            : SettingLineOrder.fromJson(json['line_order']),
        auto: json['auto'] == null
            ? SettingOrderAuto()
            : SettingOrderAuto.fromJson(json['auto']),
        fee: json['fee'] == null
            ? SettingOrderFee()
            : SettingOrderFee.fromJson(json['fee']),
        printOrder: json['print_order'] == null ? 0 : json['print_order'],
        printDetail: json['print_detail'] == null ? 0 : json['print_detail'],
        checkoutType: json['checkout_type'] == null ? 0 : json['checkout_type'],
      );

  Map<String, dynamic> toJson() => {
        'line_order': lineOrder == null ? null : lineOrder.toJson(),
        'auto': auto == null ? null : auto.toJson(),
        'fee': fee == null ? null : fee.toJson(),
        'print_order': printOrder == null ? null : printOrder,
        'print_detail': printDetail == null ? null : printDetail,
        'checkout_type': checkoutType == null ? null : checkoutType,
      };

  SettingLineOrder get nnLineOrder {
    lineOrder ??= SettingLineOrder();
    return lineOrder;
  }

  // 0: 餐飲-前結帳
  // 1: 餐飲-後結帳
  // 2: 零售
  // 3: 餐飲-前結帳+零售
  // 4: 餐飲-後結帳+零售
  BrandsType getBrandsType() {
    switch (checkoutType) {
      case 0:
        return BrandsType.BeforeDinner;
      case 1:
        return BrandsType.AfterDinner;
      case 2:
        return BrandsType.Retail;
      case 3:
        return BrandsType.BeforeDinnerWithRetail;
      case 4:
        return BrandsType.AfterDinnerWithRetail;
    }
    return BrandsType.Max;
  }

  // 0: 餐飲-前結帳
  // 1: 餐飲-後結帳
  // 2: 零售
  // 3: 餐飲-前結帳+零售
  // 4: 餐飲-後結帳+零售
  static String getBrandsTypeDisplayStr(int type) {
    switch (type) {
      case 0:
        return '餐飲-前結帳';
      case 1:
        return '餐飲-後結帳';
      case 2:
        return '零售';
      case 3:
        return '餐飲-前結帳+零售';
      case 4:
        return '餐飲-後結帳+零售';
    }
    return '???';
  }
}
