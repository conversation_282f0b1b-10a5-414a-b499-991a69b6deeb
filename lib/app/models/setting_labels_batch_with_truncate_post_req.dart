import 'dart:convert';

import 'package:okshop_model/okshop_model.dart';

class SettingLabelsBatchWithTruncatePostReq {
  List<SettingLabel> labels;

  SettingLabelsBatchWithTruncatePostReq({
    this.labels,
  });

  SettingLabelsBatchWithTruncatePostReq.fromJson(Map<String, dynamic> json) {
    labels = json['labels'] ?? [];
  }

  Map<String, dynamic> toBody() {
    final Map<String, dynamic> data = new Map<String, dynamic>();

    if (this.labels != null) {
      data['labels'] = jsonEncode(this.labels);
    }

    return data;
  }
}
