import 'package:dio/dio.dart';
import 'package:muyipork/app/models/res_base.dart';

class SettingLabelsBatchWithTruncatePostRes extends ResBase {
  bool isCreated;
  List<int> channelLabelIds;

  SettingLabelsBatchWithTruncatePostRes.resError(Response response) : super.resError(response);
  SettingLabelsBatchWithTruncatePostRes.unKnownError() : super.unKnownError();

  SettingLabelsBatchWithTruncatePostRes({this.isCreated, this.channelLabelIds});

  SettingLabelsBatchWithTruncatePostRes.fromJson(Map<String, dynamic> json) {
    isCreated = json['is_created'];
    channelLabelIds = json['channel_label_ids'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_created'] = this.isCreated;
    data['channel_label_ids'] = this.channelLabelIds;
    return data;
  }
}