import 'package:dio/dio.dart';
import 'package:muyipork/app/models/res_base.dart';

class SettingLabelsPostRes extends ResBase {
  bool isCreated;
  int labelId;

  SettingLabelsPostRes({this.isCreated, this.labelId});

  SettingLabelsPostRes.resError(Response response) : super.resError(response);
  SettingLabelsPostRes.unKnownError() : super.unKnownError();

  SettingLabelsPostRes.fromJson(Map<String, dynamic> json) {
    isCreated = json['is_created'];
    labelId = json['label_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_created'] = this.isCreated;
    data['label_id'] = this.labelId;
    return data;
  }
}