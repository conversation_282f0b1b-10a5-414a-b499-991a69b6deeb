import 'package:dio/dio.dart';
import 'package:muyipork/app/models/res_base.dart';

class SettingLabelsPutRes extends ResBase {
  bool isUpdated;
  int labelId;

  SettingLabelsPutRes({this.isUpdated, this.labelId});

  SettingLabelsPutRes.resError(Response response) : super.resError(response);
  SettingLabelsPutRes.unKnownError() : super.unKnownError();

  SettingLabelsPutRes.fromJson(Map<String, dynamic> json) {
    isUpdated = json['is_updated'];
    labelId = json['label_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['label_id'] = this.labelId;
    return data;
  }
}