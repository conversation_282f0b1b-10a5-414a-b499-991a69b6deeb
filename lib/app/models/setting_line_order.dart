//line 訂單設定
import 'dart:convert';

class SettingLineOrder {
  DinerOnline dinerOnlineOrder;
  DinerOnline dinerOnlineHere;
  DinerOnline dinerOnlineTogo;
  DinerOnline dinerOnlineDelivery;

  //內用
  //0: 關閉
  //1: 開啟
  int dineIn = 0;

  //自取
  //0: 關閉
  //1: 開啟
  int toGo = 0;

  //外送
  //0: 關閉
  //1: 開啟
  int delivery = 0;

  //點餐上限
  //0: 無限
  //1~99: 上限數（計算 line 訂單已被接單的數量）
  num limit = 0;

  //備餐時間（分鐘）
  num preparationMin = 0;

  SettingLineOrder({
    this.dineIn = 0,
    this.toGo = 0,
    this.delivery = 0,
    this.limit = 0,
    this.preparationMin = 0,
    this.dinerOnlineOrder,
    this.dinerOnlineHere,
    this.dinerOnlineTogo,
    this.dinerOnlineDelivery,
  }) {
    this.dinerOnlineOrder ??= DinerOnline();
    this.dinerOnlineHere ??= DinerOnline();
    this.dinerOnlineTogo ??= DinerOnline();
    this.dinerOnlineDelivery ??= DinerOnline();
  }

  factory SettingLineOrder.fromJson(Map<String, dynamic> json) =>
      SettingLineOrder(
        dineIn: json['dine_in'] == null ? 0 : json['dine_in'],
        toGo: json['to_go'] == null ? 0 : json['to_go'],
        delivery: json['delivery'] == null ? 0 : json['delivery'],
        limit: json['limit'] == null ? 0 : json['limit'],
        preparationMin:
            json['preparation_min'] == null ? 0 : json['preparation_min'],
        dinerOnlineOrder: json["diner_online_order"] == null
            ? null
            : DinerOnline.fromJson(json["diner_online_order"]),
        dinerOnlineHere: json["diner_online_here"] == null
            ? null
            : DinerOnline.fromJson(json["diner_online_here"]),
        dinerOnlineTogo: json["diner_online_togo"] == null
            ? null
            : DinerOnline.fromJson(json["diner_online_togo"]),
        dinerOnlineDelivery: json["diner_online_delivery"] == null
            ? null
            : DinerOnline.fromJson(json["diner_online_delivery"]),
      );

  Map<String, dynamic> toJson() => {
        "dine_in": dineIn == null ? 0 : dineIn,
        "to_go": toGo == null ? 0 : toGo,
        "delivery": delivery == null ? 0 : delivery,
        "limit": limit == null ? 0 : limit,
        "preparation_min": preparationMin == null ? 0 : preparationMin,
        "diner_online_order":
            dinerOnlineOrder == null ? null : dinerOnlineOrder.toJson(),
        "diner_online_here":
            dinerOnlineHere == null ? null : dinerOnlineHere.toJson(),
        "diner_online_togo":
            dinerOnlineTogo == null ? null : dinerOnlineTogo.toJson(),
        "diner_online_delivery":
            dinerOnlineDelivery == null ? null : dinerOnlineDelivery.toJson(),
      };

  DinerOnline get nnDinerOnlineOrder {
    dinerOnlineOrder ??= DinerOnline();
    return dinerOnlineOrder;
  }

  DinerOnline get nnDinerOnlineHere {
    dinerOnlineHere ??= DinerOnline();
    return dinerOnlineHere;
  }

  DinerOnline get nnDinerOnlineTogo {
    dinerOnlineTogo ??= DinerOnline();
    return dinerOnlineTogo;
  }

  DinerOnline get nnDinerOnlineDelivery {
    dinerOnlineDelivery ??= DinerOnline();
    return dinerOnlineDelivery;
  }
}

class DinerOnline {
  DinerOnline({
    this.status,
    this.tableStatus,
    this.tableType,
    this.preOrderWithinDays,
    this.preOrderAfterMinute,
    this.orderLimitation,
    this.description,
  });

  num status;
  num tableStatus;
  num tableType;
  num preOrderWithinDays;
  num preOrderAfterMinute;
  num orderLimitation;
  String description;

  DinerOnline copyWith({
    num status,
    num tableStatus,
    num tableType,
    num preOrderWithinDays,
    num preOrderAfterMinute,
    num orderLimitation,
    String description,
  }) =>
      DinerOnline(
        status: status ?? this.status,
        tableStatus: tableStatus ?? this.tableStatus,
        tableType: tableType ?? this.tableType,
        preOrderWithinDays: preOrderWithinDays ?? this.preOrderWithinDays,
        preOrderAfterMinute: preOrderAfterMinute ?? this.preOrderAfterMinute,
        orderLimitation: orderLimitation ?? this.orderLimitation,
        description: description ?? this.description,
      );

  factory DinerOnline.fromRawJson(String str) =>
      DinerOnline.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DinerOnline.fromJson(Map<String, dynamic> json) => DinerOnline(
        status: json["status"] == null ? null : json["status"],
        tableStatus: json["table_status"] == null ? null : json["table_status"],
        tableType: json["table_type"] == null ? null : json["table_type"],
        preOrderWithinDays: json["pre_order_within_days"] == null
            ? null
            : json["pre_order_within_days"],
        preOrderAfterMinute: json["pre_order_after_minute"] == null
            ? null
            : json["pre_order_after_minute"],
        orderLimitation:
            json["order_limitation"] == null ? null : json["order_limitation"],
        description: json["description"] == null ? null : json["description"],
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "table_status": tableStatus == null ? null : tableStatus,
        "table_type": tableType == null ? null : tableType,
        "pre_order_within_days":
            preOrderWithinDays == null ? null : preOrderWithinDays,
        "pre_order_after_minute":
            preOrderAfterMinute == null ? null : preOrderAfterMinute,
        "order_limitation": orderLimitation == null ? null : orderLimitation,
        "description": description == null ? null : description,
      };
}
