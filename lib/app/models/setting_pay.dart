// To parse this JSON data, do
//
//     final settingPay = settingPayFromJson(jsonString);

import 'dart:convert';

class SettingPay {
  SettingPay({
    this.id,
    this.isCustom,
    this.name,
    this.payMethodId,
    this.sort,
    this.status,
  });

  num id;
  bool isCustom;
  String name;
  num payMethodId;
  num sort;
  num status;

  SettingPay copyWith({
    num id,
    bool isCustom,
    String name,
    num payMethodId,
    num sort,
    num status,
  }) =>
      SettingPay(
        id: id ?? this.id,
        isCustom: isCustom ?? this.isCustom,
        name: name ?? this.name,
        payMethodId: payMethodId ?? this.payMethodId,
        sort: sort ?? this.sort,
        status: status ?? this.status,
      );

  factory SettingPay.fromRawJson(String str) =>
      SettingPay.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SettingPay.fromJson(Map<String, dynamic> json) => SettingPay(
        id: json["id"] == null ? null : json["id"],
        isCustom: json["is_custom"] == null ? null : json["is_custom"],
        name: json["name"] == null ? null : json["name"],
        payMethodId:
            json["pay_method_id"] == null ? null : json["pay_method_id"],
        sort: json["sort"] == null ? null : json["sort"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "is_custom": isCustom == null ? null : isCustom,
        "name": name == null ? null : name,
        "pay_method_id": payMethodId == null ? null : payMethodId,
        "sort": sort == null ? null : sort,
        "status": status == null ? null : status,
      };
}
