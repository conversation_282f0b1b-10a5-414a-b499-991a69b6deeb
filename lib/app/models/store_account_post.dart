// To parse this JSON data, do
//
//     final storeAccountPost = storeAccountPostFromJson(jsonString);

import 'dart:convert';

class StoreAccountPost {
  StoreAccountPost({
    this.roleId,
    this.username,
    this.name,
    this.password,
    this.status,
    this.comment,
  });

  num roleId;
  String username;
  String name;
  String password;
  num status;
  String comment;

  StoreAccountPost copyWith({
    num roleId,
    String username,
    String name,
    String password,
    num status,
    String comment,
  }) =>
      StoreAccountPost(
        roleId: roleId ?? this.roleId,
        username: username ?? this.username,
        name: name ?? this.name,
        password: password ?? this.password,
        status: status ?? this.status,
        comment: comment ?? this.comment,
      );

  factory StoreAccountPost.fromRawJson(String str) =>
      StoreAccountPost.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StoreAccountPost.fromJson(Map<String, dynamic> json) =>
      StoreAccountPost(
        roleId: json["role_id"] == null ? null : json["role_id"],
        username: json["username"] == null ? null : json["username"],
        name: json["name"] == null ? null : json["name"],
        password: json["password"] == null ? null : json["password"],
        status: json["status"] == null ? null : json["status"],
        comment: json["comment"] == null ? null : json["comment"],
      );

  Map<String, dynamic> toJson() => {
        "role_id": roleId == null ? null : roleId,
        "username": username == null ? null : username,
        "name": name == null ? null : name,
        "password": password == null ? null : password,
        "status": status == null ? null : status,
        "comment": comment == null ? null : comment,
      };
}
