// To parse this JSON data, do
//
//     final storeAccountPut = storeAccountPutFromJson(jsonString);

import 'dart:convert';

class StoreAccountPut {
  StoreAccountPut({
    this.roleId,
    this.name,
    this.password,
    this.status,
    this.comment,
  });

  num roleId;
  String name;
  String password;
  num status;
  String comment;

  StoreAccountPut copyWith({
    num roleId,
    String name,
    String password,
    num status,
    String comment,
  }) =>
      StoreAccountPut(
        roleId: roleId ?? this.roleId,
        name: name ?? this.name,
        password: password ?? this.password,
        status: status ?? this.status,
        comment: comment ?? this.comment,
      );

  factory StoreAccountPut.fromRawJson(String str) =>
      StoreAccountPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StoreAccountPut.fromJson(Map<String, dynamic> json) =>
      StoreAccountPut(
        roleId: json["role_id"] == null ? null : json["role_id"],
        name: json["name"] == null ? null : json["name"],
        password: json["password"] == null ? null : json["password"],
        status: json["status"] == null ? null : json["status"],
        comment: json["comment"] == null ? null : json["comment"],
      );

  Map<String, dynamic> toJson() => {
        "role_id": roleId == null ? null : roleId,
        "name": name == null ? null : name,
        "password": password == null ? null : password,
        "status": status == null ? null : status,
        "comment": comment == null ? null : comment,
      };
}
