import 'dart:convert';

class UpdateRes {
  int orderId;
  bool isUpdated;

  UpdateRes({
    this.orderId,
    this.isUpdated,
  });

  UpdateRes copyWith({
    int orderId,
    bool isUpdated,
  }) =>
      UpdateRes(
        orderId: orderId ?? this.orderId,
        isUpdated: isUpdated ?? this.isUpdated,
      );

  factory UpdateRes.fromRawJson(String str) =>
      UpdateRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UpdateRes.fromJson(Map<String, dynamic> json) => UpdateRes(
        orderId: json["order_id"],
        isUpdated: json["is_updated"],
      );

  Map<String, dynamic> toJson() => {
        "order_id": orderId,
        "is_updated": isUpdated,
      };
}
