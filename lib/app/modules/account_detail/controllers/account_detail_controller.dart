import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:muyipork/enums.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/models/store_account.dart';
import 'package:muyipork/app/providers/account_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

class AccountDetailController extends GetxController with StateMixin<String> {
  final AccountProvider accountProvider;
  final _id = RxNum(0);
  final password = ''.obs;
  final confirmPassword = ''.obs;
  final _draft = StoreAccount().obs;
  final _disposable = Completer();

  set id(num value) => _id.value = value;
  num get id => _id.value;
  String get displayTitle => isUpdating ? '修改操作員' : '新增操作員';
  String get displayButton => isUpdating ? '修改' : '新增';
  bool get isCreating => !isUpdating;
  bool get isUpdating => Get.parameters.containsKey(Keys.Id);
  StoreAccount get draft => _draft.value;

  AccountDetailController({
    @required this.accountProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    id = Get.parameters.containsKey(Keys.Id)
        ? num.tryParse(Get.parameters[Keys.Id])
        : 0;
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      if (isUpdating) {
        // 編輯帳號
        _draft.value = await accountProvider.getStoreAccount(_id.value);
      } else {
        // 新增帳號
        _draft.value = StoreAccount(
          // 新增帳號預設啟用
          status: Switcher.On.index,
          role: Role(
            id: StoreRole.Employee.index,
          ),
        );
      }
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit() async {
    try {
      final needValidate = draft.status.switcher.isOn;
      needValidate && _validate();
      final upsertAccount =
          isUpdating ? _putStoreAccount() : _postStoreAccount();
      final ret = await upsertAccount;
      if (ret != null && ret > 0) {
        // 取得最新資料
        await accountProvider.getStoreAccount(ret);
        return true;
      }
      return false;
    } catch (e) {
      rethrow;
    }
  }

  Future<num> _postStoreAccount() {
    final data = draft.asStoreAccountPost(password.value);
    return accountProvider.postStoreAccount(data);
  }

  Future<num> _putStoreAccount() {
    final data = draft.asStoreAccountPut();
    // 特殊: 啟用才更新密碼
    if (draft.status.switcher.isOn) {
      // 如果密碼為空，定義為維持現狀
      if (password.value == null || password.value.isEmpty) {
        data.password = null;
      } else {
        data.password = password.value;
      }
    }
    return accountProvider.putStoreAccount(_id.value, data);
  }

  void refreshDraft() {
    _draft.refresh();
  }

  bool _validate() {
    if (draft.name == null || draft.name.isEmpty) {
      throw '名稱是必填項目'; // TODO: i18n
    }
    if (true == isCreating) {
      if (draft.username == null || draft.username.isEmpty) {
        throw '帳號是必填項目';
      }
    }
    if (true == isCreating) {
      if (password.value == null || password.value.isEmpty) {
        throw '密碼是必填項目';
      }
    }
    if (password.value != confirmPassword.value) {
      throw '確認密碼不同'; // TODO: i18n
    }
    return true;
  }
}
