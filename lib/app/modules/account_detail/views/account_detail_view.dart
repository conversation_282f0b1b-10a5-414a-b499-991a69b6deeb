import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/dialog_actions.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/account_detail_controller.dart';

class AccountDetailView extends GetView<AccountDetailController> {
  // @override
  // final String tag;

  AccountDetailView({
    Key key,
  }) : // tag = Get.parameters[Keys.Tag],
        super(key: key) {
    // Get.lazyPut<AccountDetailController>(
    //   () => AccountDetailController(
    //     accountProvider: Get.find(),
    //   ),
    //   tag: tag,
    // );
  }

  Future<void> _submit() async {
    try {
      final ret = await FutureProgress(
        future: controller.submit(),
      ).dialog<bool>();
      // 新增/修改 成功
      if (true == ret) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Future<void> _showRolePicker() async {
    try {
      final actions = [
        '店長', // TODO: i18n
        '店員', // TODO: i18n
      ];
      final value = await DialogActions(
        titleText: '請選擇身份', // TODO: i18n
        actions: actions,
      ).dialog<num>();
      switch (value) {
        case 0: // 店長
          controller.draft.role.id = StoreRole.Boss.index;
          controller.refreshDraft();
          break;
        case 1: // 店員
          controller.draft.role.id = StoreRole.Employee.index;
          controller.refreshDraft();
          break;
        default:
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: controller.displayTitle,
      child: controller.obx(
        (state) {
          return BottomWidgetPage.save(
            onPressed: _submit,
            buttonText: controller.displayButton,
            child: _main(),
          );
        },
        onError: (message) {
          return ListWidget.message(
            message,
            buttonText: '重試',
            onPressed: controller.onRefresh,
          );
        },
        onEmpty: ListWidget.blank(),
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, _header());
    children.addIf(
      true,
      Expanded(
        child: Obx(() => _list()),
      ),
    );
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _header() {
    return Text(
      '請填寫操作員資料', // TODO: i18n
      style: TextStyle(
        fontSize: 16,
        color: OKColor.Primary,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    ).paddingSymmetric(vertical: 12);
  }

  Widget _list() {
    final children = <Widget>[];
    children.addIf(
      controller.isUpdating,
      SwitchListTile(
        tileColor: Colors.white,
        contentPadding: kContentPadding,
        title: const Text(
          '啟用帳號', // TODO: i18n
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
        value: controller.draft.status.switcher.isOn,
        onChanged: (value) {
          controller.draft.status = value.switcher.index;
          controller.refreshDraft();
        },
      ),
    );
    final isOn = controller.draft.status.switcher.isOn;
    children.addIf(isOn, _title());
    children.addIf(isOn, _nickname());
    children.addIf(isOn, _account());
    children.addIf(isOn, _password());
    children.addIf(isOn, _confirmPassword());
    children.addIf(isOn, _roleIdHeader());
    children.addIf(isOn, _roleId());
    children.addIf(isOn, _commentHeader());
    children.addIf(isOn, _comment());
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: children,
    );
  }

  Widget _title() {
    return Text(
      '帳號資訊', // TODO: i18n
      style: const TextStyle(
        fontSize: 14,
        color: OKColor.Gray66,
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      vertical: 8.0,
      horizontal: kPadding,
    );
  }

  Widget _nickname() {
    return ListTile(
      contentPadding: kContentPadding,
      tileColor: Colors.white,
      title: CustomEditor(
        initialValue: controller.draft.name ?? '',
        labelText: '名稱', // TODO: i18n
        hintText: '請輸入操作員名稱', // TODO: i18n
        onChanged: (value) {
          controller.draft.name = value;
        },
        validator: (value) {
          if (controller.draft.name == null || controller.draft.name.isEmpty) {
            return '必填項目'; // TODO: i18n
          }
          return null;
        },
      ),
    );
  }

  Widget _account() {
    return ListTile(
      contentPadding: kContentPadding,
      tileColor: Colors.white,
      title: CustomEditor(
        enabled: controller.isCreating,
        initialValue: controller.draft.username ?? '',
        labelText: '帳號', // TODO: i18n
        hintText: '請輸入帳號', // TODO: i18n
        onChanged: (value) {
          controller.draft.username = value;
        },
        validator: (value) {
          if (controller.isCreating) {
            if (controller.draft.username == null ||
                controller.draft.username.isEmpty) {
              return '必填項目'; // TODO: i18n
            }
          }
          return null;
        },
      ),
    );
  }

  Widget _password() {
    return ListTile(
      contentPadding: kContentPadding,
      tileColor: Colors.white,
      title: CustomEditor(
        obscureText: true,
        labelText: '密碼', // TODO: i18n
        hintText: '請輸入密碼', // TODO: i18n
        onChanged: controller.password,
        validator: (value) {
          if (controller.isCreating) {
            // 建立帳號必填密碼
            if (controller.password.value == null ||
                controller.password.value.isEmpty) {
              return '必填項目'; // TODO: i18n
            }
          }
          return null;
        },
      ),
    );
  }

  Widget _confirmPassword() {
    return ListTile(
      contentPadding: kContentPadding,
      tileColor: Colors.white,
      title: CustomEditor(
        obscureText: true,
        labelText: '再次輸入密碼', // TODO: i18n
        hintText: '請輸入密碼', // TODO: i18n
        onChanged: controller.confirmPassword,
        validator: (value) {
          if (controller.password.value != controller.confirmPassword.value) {
            return '確認密碼不同'; // TODO: i18n
          }
          return null;
        },
      ),
    );
  }

  Widget _roleIdHeader() {
    return Text(
      '身份',
      style: const TextStyle(
        fontSize: 14,
        color: OKColor.Gray66,
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
  }

  Widget _roleId() {
    return ListTile(
      contentPadding: kContentPadding,
      onTap: _showRolePicker,
      tileColor: Colors.white,
      title: Obx(() {
        return Text(
          controller.draft.role?.id?.storeRole?.name ??
              controller.draft.role?.name ??
              '',
          style: TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          textAlign: TextAlign.left,
        );
      }),
      trailing: Icon(
        Icons.expand_more,
      ),
    );
  }

  Widget _commentHeader() {
    return Text(
      '備註', // TODO: i18n
      style: const TextStyle(
        fontSize: 14,
        color: OKColor.Gray66,
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
  }

  Widget _comment() {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: kPadding,
        vertical: 12.0,
      ),
      tileColor: Colors.white,
      title: TextFormField(
        initialValue: controller.draft.comment ?? '',
        onChanged: (value) {
          controller.draft.comment = value;
        },
        minLines: 3,
        keyboardType: TextInputType.multiline,
        maxLines: null,
        decoration: const InputDecoration(
          hintText: '請輸入備註…', // TODO: i18n
          border: const OutlineInputBorder(
            borderRadius: BorderRadius.zero,
          ),
          hintStyle: const TextStyle(
            fontSize: 16,
            color: OKColor.Gray66,
          ),
        ),
      ),
    );
  }
}
