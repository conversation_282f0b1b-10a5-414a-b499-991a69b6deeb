import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/models/store_account.dart';
import 'package:muyipork/app/providers/account_provider.dart';

class AccountsController extends GetxController with StateMixin<String> {
  final AccountProvider accountProvider;
  final filter = Filter().obs;
  final _disposable = Completer();
  final data = <StoreAccount>[].obs;

  AccountsController({
    @required this.accountProvider,
  });

  @override
  void onInit() {
    super.onInit();

    final cacheStream = accountProvider.cached.stream
        .debounce(50.milliseconds)
        .asBroadcastStream();

    cacheStream
        .map((event) => accountProvider.where(filter.value))
        .takeUntil(_disposable.future)
        .listen(
      (value) {
        data.assignAll(value);
        if (data.isNotEmpty) {
          change('', status: RxStatus.success());
        } else {
          change('', status: RxStatus.empty());
        }
      },
      onError: (error) {
        change('', status: RxStatus.error('$error'));
      },
    );

    final filterStream = filter.stream
        .tap((value) => change('', status: RxStatus.loading()))
        .debounce(500.milliseconds)
        .asBroadcastStream();

    filterStream
        .map((event) => accountProvider.where(event))
        // .combineLatest(filterStream, (x, y) {
        //   logger.d(
        //       '[AccountsController] getStoreAccouts with filter(${y.keyward})');
        //   return accountProvider.where(y);
        // })
        .takeUntil(_disposable.future)
        .listen(
      (value) {
        data.assignAll(value);
        if (data.isNotEmpty) {
          change('', status: RxStatus.success());
        } else {
          change('', status: RxStatus.empty());
        }
      },
      onError: (error) {
        change('', status: RxStatus.error('$error'));
      },
    );
  }

  @override
  void onReady() {
    super.onReady();
    // HACK: test
    // change('', status: RxStatus.error('message'));
    // change('', status: RxStatus.empty());
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    logger.d('[AccountsController] onRefresh');
    try {
      final ret = await accountProvider.getStoreAccounts();
      data.assignAll(ret);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }
}
