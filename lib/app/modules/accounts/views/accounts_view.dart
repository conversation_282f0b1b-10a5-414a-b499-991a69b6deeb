import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/avatar.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/label_value.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/search_bar.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/models/store_account.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

import '../controllers/accounts_controller.dart';

class AccountsView extends GetView<AccountsController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '操作員帳號設定',
      child: _main(),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      SearchBar(
        hintText: '請輸入操作員名稱或帳號',
        onValueChanged: (value) {
          controller.filter.value.keyword = value;
          controller.filter.refresh();
        },
      ),
    );
    children.addIf(
      true,
      Expanded(child: _slave()),
    );
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _slave() {
    return controller.obx(
      (value) {
        return BottomWidgetPage.save(
          buttonText: '新增操作員',
          onPressed: () {
            Get.toNamed(Routes.ACCOUNT_DETAIL);
          },
          child: _list(),
        );
      },
      onError: (message) {
        return ListWidget.message(
          message,
          buttonText: '重試',
          onPressed: controller.onRefresh,
        );
      },
      onEmpty: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: ListWidget.blank(
          buttonText: '新增操作員',
          onPressed: () {
            Get.toNamed(Routes.ACCOUNT_DETAIL);
          },
        ),
      ),
    );
  }

  Widget _list() {
    return RefreshIndicator(
      onRefresh: controller.onRefresh,
      child: Obx(() {
        final list = controller.data;
        list.sort((x, y) => (y.id ?? 0).compareTo(x.id ?? 0));
        return ListView.separated(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.only(
            bottom: kBottomButtonPadding,
          ),
          itemCount: list.length,
          separatorBuilder: (context, index) => SizedBox(height: 8.0),
          itemBuilder: (context, index) {
            final element = list.elementAt(index);
            return _Item(
              data: element,
              onPressed: () {
                Get.toNamed(
                  Routes.ACCOUNT_DETAIL,
                  parameters: <String, String>{
                    Keys.Id: '${element.id}',
                    Keys.Tag: '${element.id}',
                  },
                );
              },
            );
          },
        );
      }),
    );
  }
}

class _Item extends GetView<AccountsController> {
  final StoreAccount data;
  final Function onPressed;

  const _Item({
    Key key,
    @required this.data,
    @required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: _main(),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, SizedBox(height: 16));
    children.addIf(
      true,
      Padding(
        padding: EdgeInsets.symmetric(horizontal: kPadding),
        child: Avatar(data: data),
      ),
    );
    children.addIf(true, SizedBox(height: 16));
    children.addIf(
      true,
      const Divider(
        height: 1.0,
        indent: kPadding,
        endIndent: kPadding,
      ),
    );
    children.addIf(true, SizedBox(height: 12.0));
    children.addIf(
      true,
      Padding(
        padding: kContentPadding,
        child: LabelValue(
          labelText: '上次登入時間 ',
          // labelStyle: TextStyle(
          //   fontSize: 16,
          //   color: Colors.black,
          //   height: 2,
          // ),
          valueText: data.displayLastLogin ?? '',
          // valueStyle: TextStyle(
          //   fontFamily: 'Helvetica Neue',
          //   fontSize: 16,
          //   color: const Color(0xff3e4b5a),
          //   height: 2,
          // ),
        ),
      ),
    );
    children.addIf(true, SizedBox(height: 8.0));
    children.addIf(
      true,
      Padding(
        padding: kContentPadding,
        child: TextButton.icon(
          onPressed: this.onPressed,
          icon: const Icon(
            Icons.edit,
            color: const Color(0xFF3E4B5A),
          ),
          label: Text(
            '編輯', // TODO: i18n
            style: const TextStyle(
              fontSize: 14,
              color: const Color(0xff3e4b5a),
            ),
            textAlign: TextAlign.left,
          ),
          style: ButtonStyle(
            side: MaterialStateProperty.all(const BorderSide(
              width: 1.0,
              color: const Color(0xffdbdbea),
            )),
            padding: MaterialStateProperty.all(EdgeInsets.zero),
            backgroundColor: MaterialStateProperty.all(kColorBackground),
            shape: MaterialStateProperty.all(const StadiumBorder()),
            minimumSize:
                MaterialStateProperty.all(const Size(double.infinity, 30.0)),
          ),
        ),
      ),
    );
    children.addIf(true, SizedBox(height: 12.0));
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
