import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/composite_edit_item.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/addition_category_setup_controller.dart';

class AdditionCategorySetupView
    extends GetView<AdditionCategorySetupController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.focusScope.unfocus(),
      child: StandardPage(
        titleText: '編輯規格',
        child: controller.obx(
          (state) {
            return BottomWidgetPage.save(
              child: _main(),
              onPressed: _submit,
            );
          },
        ),
      ),
    );
  }

  Future<void> _submit() async {
    try {
      final ret = await FutureProgress(future: controller.submit()).dialog();
      if (ret == true) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      SizedBox(
        height: 30,
      ),
    );
    children.addIf(
      true,
      SettingsWidget.input(
        initialValue: controller.draft.name,
        labelText: '規格名稱',
        hintText: '請輸入規格名稱',
        onChanged: (value) {
          controller.draft.name = value;
          // 加入要更新的 id
          // controller.updating(controller.draft.id);
        },
      ),
    );
    children.addIf(
      true,
      Expanded(child: Obx(() => _additionProductList())),
    );
    return Column(children: children);
  }

  Widget _additionProductList() {
    final list = controller.data;
    final children = List<Widget>.generate(list.length + 1, (index) {
      if (index < list.length) {
        final element = list.elementAt(index);
        return CompositeEditItem(
          CompositeEditItemArgs(
            CompositeContentMode.TextFieldAndNumField,
            CompositeRightButtonMode.Remove,
            mainTextFieldInit: element.name,
            mainTextFieldHint: '選項名稱',
            secondaryTextFieldInit: '${element.price.round()}',
            secondaryTextFieldHint: '金額',
            mainTextFieldChanged: (value) {
              element.name = value;
              controller.updating(element.id);
            },
            secondaryTextFieldChanged: (value) {
              element.price = num.tryParse(value) ?? 0;
              controller.updating(element.id);
            },
            onRightButtonPressed: () async {
              // 刪除
              try {
                return await controller.deleting(element.id);
              } catch (e) {
                return DialogGeneral.alert('$e').dialog();
              }
            },
          ),
          key: Key('$index'),
        );
      }
      return CompositeEditItem(
        CompositeEditItemArgs(
          CompositeContentMode.TextFieldAndNumField,
          CompositeRightButtonMode.Add,
          mainTextFieldHint: '選項名稱',
          secondaryTextFieldHint: '金額',
          mainTextFieldChanged: (value) {
            controller.creating.name = value;
          },
          secondaryTextFieldChanged: (value) {
            controller.creating.price = num.tryParse(value) ?? 0;
          },
          onRightButtonPressed: () {
            final name = controller.creating.name;
            if (name == null || name.isEmpty) {
              return DialogGeneral.alert('選項名稱是必填項目').dialog();
            }
            // return controller.applyCreating();
            return FutureProgress(
              future: controller.submit(),
            ).dialog();
          },
          showDragHandle: false,
        ),
        key: Key('$index'),
      );
    });

    return ReorderableListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: children,
      onReorder: (int srcIndex, int destIndex) {
        // asc (0, 2)
        // desc (1, 0)
        controller.sorting(srcIndex, destIndex);
      },
    );
  }
}
