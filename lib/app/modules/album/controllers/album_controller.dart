import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/image_model.dart';
import 'package:muyipork/keys.dart';

class AlbumController extends GetxController with StateMixin {
  final data = <ImageModel>[].obs;
  final regexp = RegExp(r'^https?://');
  final _page = PageController().obs;

  PageController get page => _page.value;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh().then(
      (value) {
        if (data.isEmpty) {
          change(null, status: RxStatus.empty());
        } else {
          change(null, status: RxStatus.success());
        }
      },
      onError: (error) {
        change(null, status: RxStatus.error('$error'));
      },
    );
  }

  @override
  void onClose() {}

  Future<void> onRefresh() async {
    if (!Get.parameters.containsKey(Keys.Data)) {
      return Future<Null>.error('沒有資料');
    }
    final jsonArray = jsonDecode(Get.parameters[Keys.Data]);
    final it = List.from(jsonArray).map((e) => ImageModel.fromJson(e));
    data.clear();
    data.addAll(it);
    if (Get.parameters.containsKey('initialPage')) {
      final initialPage = num.tryParse(Get.parameters['initialPage']) ?? 1;
      _page.value = PageController(initialPage: initialPage);
    }
  }
}
