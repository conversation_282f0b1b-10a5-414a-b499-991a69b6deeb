import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:photo_view/photo_view_gallery.dart';

import '../controllers/album_controller.dart';

class AlbumView extends GetView<AlbumController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '相簿',
      child: controller.obx(
        (state) {
          return PhotoViewGallery.builder(
              backgroundDecoration: BoxDecoration(
                color: Colors.transparent,
              ),
              scrollPhysics: const BouncingScrollPhysics(),
              builder: (BuildContext context, int index) {
                final element = controller.data.elementAt(index);
                final fromNetwork = controller.regexp.hasMatch(element.url);
                return PhotoViewGalleryPageOptions(
                  imageProvider: fromNetwork
                      ? NetworkImage(element.url)
                      : AssetImage(element.url),
                  // initialScale: PhotoViewComputedScale.contained * 0.8,
                  // minScale: PhotoViewComputedScale.contained * 0.8,
                  // maxScale: PhotoViewComputedScale.covered * 1.1,
                  // heroAttributes: HeroAttributes(tag: galleryItems[index].id),
                );
              },
              itemCount: controller.data.length,
              pageController: controller.page,
              loadingBuilder: (context, progress) {
                return Center(
                  child: Container(
                    width: 20.0,
                    height: 20.0,
                    child: CircularProgressIndicator(
                      value: progress == null
                          ? null
                          : progress.cumulativeBytesLoaded /
                              progress.expectedTotalBytes,
                    ),
                  ),
                );
              });
        },
        onEmpty: ListWidget.blank(),
        onError: ListWidget.message,
      ),
    );
  }
}
