import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:image/image.dart' as Img;
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:muyipork/app/components/dialog_actions.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_obx.dart';
import 'package:muyipork/app/components/image_item.dart';
import 'package:muyipork/app/components/message_page.dart';
import 'package:muyipork/app/components/save_page.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/models/image_model.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:reorderables/reorderables.dart';

import '../controllers/brands_banners_controller.dart';

class BrandsBannersView extends GetView<BrandsBannersController> {
  static const _THUMBNAIL_SIZE = 60.0;
  static const _IMAGE_SIZE = 600.0;

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '輪播圖片',
      child: controller.obx(
        (state) {
          return SavePage(
            onPressed: this._submit,
            child: _main(),
          );
        },
        onError: (message) {
          return MessagePage(
            icon: DialogContentIcon.Alert,
            message: message,
          );
        },
      ),
    );
  }

  void _submit() {
    FutureObx<bool>(
      controller.submit(),
      (state) {
        Future(() => Get.back(result: state));
        return SizedBox.shrink();
      },
    ).dialog<bool>().then(
      (value) {
        if (value != null) {
          Get.back();
        }
      },
    );
  }

  Widget _main() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 30.0,
        ),
        Expanded(
          child: SingleChildScrollView(
            child: _page(),
          ),
        ),
      ],
    );
  }

  Widget _page() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ColoredBox(
          color: Colors.white,
          child: _title().paddingSymmetric(
            horizontal: kPadding,
          ),
        ),
        Container(
          width: double.infinity,
          color: Colors.white,
          child: Obx(() {
            return _sector().paddingSymmetric(
              horizontal: kPadding,
            );
          }),
        ),
        ColoredBox(
          color: Colors.white,
          child: const SizedBox(
            width: double.infinity,
            height: 30.0,
          ),
        ),
        const SizedBox(
          height: kBottomPadding,
        ),
      ],
    );
  }

  Widget _getLocalImage(String path) {
    return SizedBox.fromSize(
      size: Size.square(_THUMBNAIL_SIZE),
      child: Image.file(
        File(path),
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _getNetworkImage(String imageUrl) {
    return SizedBox.fromSize(
      size: Size.square(_THUMBNAIL_SIZE),
      child: CachedNetworkImage(
        fit: BoxFit.contain,
        imageUrl: imageUrl ?? '',
        placeholder: (context, url) {
          return CircularProgressIndicator();
        },
        errorWidget: (context, url, error) => Icon(
          Icons.image,
          size: _THUMBNAIL_SIZE,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _sector() {
    final editing = controller.imageEditing.value;
    final ls = controller.tiles
        .map((e) {
          final child = controller.regexp.hasMatch(e.url)
              ? _getNetworkImage(e.url)
              : _getLocalImage(e.url);
          return ImageItem(
            child: child,
            onDeletePressed: editing
                ? () {
                    if (controller.tiles.remove(e)) {
                      controller.imagesToRemove.add(e);
                    }
                  }
                : null,
          );
        })
        .cast<Widget>()
        .toList();
    if (editing) {
      return ReorderableWrap(
        needsLongPressDraggable: false,
        spacing: 40.0,
        runSpacing: 28.0,
        children: ls,
        onReorder: _onReorder,
        onNoReorder: (int index) {
          //this callback is optional
          logger.d(
              '${DateTime.now().toString().substring(5, 22)} reorder cancelled. index:$index');
        },
        onReorderStarted: (int index) {
          //this callback is optional
          logger.d(
              '${DateTime.now().toString().substring(5, 22)} reorder started: index:$index');
        },
      );
    } else {
      // 加入圖片張數限制
      if (ls.length < BrandsBannersController.MAX_TILE) {
        ls.add(this._plusIcon);
      }
      return Wrap(
        spacing: 40.0,
        runSpacing: 28.0,
        children: ls,
      );
    }
  }

  Future<File> _showImagePicker() {
    return DialogActions(
      titleText: '取得照片',
      actions: [
        '相機',
        '相簿',
      ],
    ).dialog<num>().then(
      (value) {
        final ImagePicker _picker = ImagePicker();
        switch (value.button) {
          case Button.Negative:
            return _picker.pickImage(
              source: ImageSource.camera,
              maxWidth: _IMAGE_SIZE,
              maxHeight: _IMAGE_SIZE,
            );
          case Button.Positive:
            return _picker.pickImage(
              source: ImageSource.gallery,
              maxWidth: _IMAGE_SIZE,
              maxHeight: _IMAGE_SIZE,
            );
          default:
        }
      },
    ).then(
      (value) {
        return _cropImage(value.path);
      },
    );
  }

  Future<File> _cropImage(String path) {
    // create square image with white background
    final data = File(path).readAsBytesSync();
    Img.Image image = Img.decodeImage(data);
    final size = max(image.width, image.height);
    final blank = Img.Image.rgb(size, size);
    blank.fill(Colors.white.value);
    // bland
    final dstX = (size - image.width) ~/ 2;
    final dstY = (size - image.height) ~/ 2;
    final thumbnail = Img.copyInto(
      blank,
      image,
      dstX: dstX,
      dstY: dstY,
      blend: false,
    );
    // Resize the image to a 120x? thumbnail (maintaining the aspect ratio).
    // Img.Image thumbnail = Img.copyResizeCropSquare(image, 300);
    // Img.Image thumbnail2 = Img.copyResize(
    //   image,
    //   width: 300,
    //   height: 300,
    // );
    // Save the thumbnail as a PNG.
    // final newfile = '${path.path}/${path.filename}';
    File(path).writeAsBytesSync(Img.encodePng(thumbnail));

    return ImageCropper.cropImage(
      sourcePath: path,
      maxWidth: _IMAGE_SIZE.round(),
      maxHeight: _IMAGE_SIZE.round(),
      aspectRatio: CropAspectRatio(
        ratioX: 1.0,
        ratioY: 1.0,
      ),
      aspectRatioPresets: [
        CropAspectRatioPreset.square,
        // CropAspectRatioPreset.ratio3x2,
        // CropAspectRatioPreset.original,
        // CropAspectRatioPreset.ratio4x3,
        // CropAspectRatioPreset.ratio16x9
      ],
      androidUiSettings: AndroidUiSettings(
        toolbarTitle: 'Cropper',
        toolbarColor: Colors.deepOrange,
        toolbarWidgetColor: Colors.white,
        initAspectRatio: CropAspectRatioPreset.original,
        lockAspectRatio: true,
        // backgroundColor: Colors.white,
      ),
      iosUiSettings: IOSUiSettings(
          // minimumAspectRatio: 1.0,
          doneButtonTitle: '完成',
          cancelButtonTitle: '取消',
          aspectRatioLockDimensionSwapEnabled: true,
          aspectRatioLockEnabled: true),
    );
  }

  Widget get _plusIcon {
    return ImageItem(
      child: Icon(
        Icons.add,
        size: 60.0,
        color: Colors.white,
      ),
      onPressed: () {
        this._showImagePicker().then(
          (value) {
            controller.tiles.add(ImageModel(
              url: value.uri.toFilePath(),
            ));
            // test
            // final path = value.uri.toFilePath();
            // controller.apiProvider.postImages([path]).then((value) {
            //   logger.d('message');
            // });
            // controller.apiProvider.getImage('31').then((value) {
            //   logger.d('message');
            // });
            // controller.apiProvider.deleteImage('31').then((value) {
            //   logger.d('message');
            // });
            // controller.apiProvider.getImages().then((value) {
            //   logger.d('message');
            // });
          },
        );
      },
    );
  }

  void _onReorder(int oldIndex, int newIndex) {
    logger.d('_onReorder oldIndex($oldIndex), newIndex($newIndex)');
    final tempImage = controller.tiles[oldIndex];
    controller.tiles[oldIndex] = controller.tiles[newIndex];
    controller.tiles[newIndex] = tempImage;
  }

  Widget _title() {
    return Row(
      children: [
        Expanded(
          child: _hint(),
        ),
        Obx(() {
          final editing = controller.imageEditing.value;
          return TextButton.icon(
            onPressed: controller.imageEditing.toggle,
            icon: Icon(
              editing ? Icons.check : Icons.edit,
              color: OKColor.Primary,
            ),
            label: Text(
              editing ? '完成' : '編輯',
              style: TextStyle(
                fontSize: 14,
                color: OKColor.Primary,
                height: 2,
              ),
              textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.center,
            ),
          );
        }),
      ],
    );
  }

  Widget _hint() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 14,
          color: const Color(0xff222222),
        ),
        children: [
          TextSpan(
            text: '圖片：',
          ),
          TextSpan(
            text: '(請上傳正方形尺寸圖片)',
            style: TextStyle(
              color: const Color(0xff929ca8),
            ),
          ),
        ],
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.left,
    );
  }
}
