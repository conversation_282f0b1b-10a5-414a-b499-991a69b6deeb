import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/api_provider.dart';

class BrandsBasicController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final cityEditing = TextEditingController();
  final districtEditing = TextEditingController();
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  final _draft = BrandsBasic().obs;
  BrandsBasic get draft => _draft.value;

  BrandsBasicController({
    @required this.apiProvider,
  });

  void refreshDraft() {
    _draft.refresh();
  }

  @override
  void onInit() {
    super.onInit();
    final brandBasicStream = _draft.stream.asBroadcastStream();
    // 監聽縣市
    brandBasicStream
        .map((event) {
          return prefProvider.cities.firstWhere(
            (element) => element.id == event.cityId,
            orElse: () => null,
          );
        })
        .where((event) => event != null)
        .distinct((x, y) => x.id == y.id)
        .takeUntil(this._disposable.future)
        .listen(
          (event) {
            this.cityEditing.text = event.name;
            // update district
            final districts = prefProvider.getDistricts(event.id);
            final target = districts.firstWhere(
              (element) => element.id == this.draft.cityareaId,
              orElse: () => districts.elementAt(0),
            );
            if (target != null) {
              draft.cityareaId = target.id;
              refreshDraft();
            }
          },
        );
    // 監聽鄉鎮市區
    brandBasicStream
        .map((event) {
          return prefProvider.districts.firstWhere(
            (element) => element.id == event.cityareaId,
            orElse: () => null,
          );
        })
        .where((event) => event != null)
        .takeUntil(_disposable.future)
        .listen((event) {
          districtEditing.text = event.name;
        });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      final res = await apiProvider.getBrandsBasic();
      _draft.value = res.asPut();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<num> submit() {
    // special case
    if (draft.email == null) {
      draft.email = '';
    }
    return apiProvider.putBrandsBasic(draft);
  }
}
