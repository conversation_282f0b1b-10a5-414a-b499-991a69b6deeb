import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_obx.dart';
import 'package:muyipork/app/components/message_page.dart';
import 'package:muyipork/app/components/save_page.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/extension.dart';

import '../controllers/brands_news_controller.dart';

class BrandsNewsView extends GetView<BrandsNewsController> {
  static const _LENGTH_LIMIT = 200;

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '最新消息',
      child: controller.obx(
        (state) {
          return SavePage(
            onPressed: this._submit,
            child: Column(
              children: [
                SizedBox(
                  height: 30.0,
                ),
                SettingsWidget.comment(
                  // 可編輯，使用要出送的結構
                  initialValue: controller.data.news,
                  hintText: '請輸入200字內文字',
                  onChanged: (value) {
                    controller.data.news = value;
                    // 輸入框，不需更新顯示
                    // controller.refreshData();
                  },
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(_LENGTH_LIMIT),
                  ],
                ),
              ],
            ),
          );
        },
        onError: (message) {
          return MessagePage(
            icon: DialogContentIcon.Alert,
            message: message,
          );
        },
      ),
    );
  }

  void _submit() {
    FutureObx(
      controller.submit(),
      (state) {
        Future(() => Get.back(result: state));
        return SizedBox.shrink();
      },
    )
        .dialog(
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          Get.back();
        }
      },
    );
  }
}
