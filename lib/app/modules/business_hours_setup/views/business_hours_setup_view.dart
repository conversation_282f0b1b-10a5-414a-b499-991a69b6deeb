import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/week_day_editing_item.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/business_hours_setup_controller.dart';

class BusinessHoursSetupView extends GetView<BusinessHoursSetupController> {
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.dataChanged) {
          await Get.showConfirm(
            '',
            '資料已變更，是否儲存變更?',
            textConfirm: '儲存',
            textCancel: '放棄',
            onConfirm: _submit,
          );
          return false;
        }
        return true;
      },
      child: GestureDetector(
        onTap: () => Get.focusScope.unfocus(),
        child: StandardPage(
          titleText: '營業時間',
          child: controller.obx((state) {
            return BottomWidgetPage.save(
              child: ListView(
                padding: EdgeInsets.only(top: 30, bottom: kBottomButtonPadding),
                children: weekDaysList().toList(growable: false),
              ),
              onPressed: _submit,
            );
          }),
        ),
      ),
    );
  }

  Iterable<Widget> weekDaysList() sync* {
    for (int i = 0; i < controller.draft.data.hours.week.length; ++i) {
      yield WeekDayEditingItem(
        controller.draft.data.hours.week[i],
        onChanged: () => controller.makeDataChanged(),
      );
    }
  }

  Future<void> _submit() async {
    final res = await FutureProgress(
      future: controller.submit(),
    ).dialog();
    if (res == true) {
      Get.back();
    }
    // Get.showLoading();
    // try {
    //   await controller.trySaveSetting();
    //   // hide loading
    //   Get.back();
    //   // exit page
    //   Get.back();
    // } catch (e) {
    //   // hide loading
    //   Get.back();
    //   Get.showAlert(e.toString());
    // }
  }
}
