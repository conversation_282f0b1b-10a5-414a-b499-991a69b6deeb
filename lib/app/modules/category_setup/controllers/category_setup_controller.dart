import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:muyipork/app/models/id_sort.dart';
import 'package:muyipork/app/models/products_get_qry.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';

enum CategorySetupTab {
  Categories,
  AdditionCategories,
}

extension ExtensionCategorySetupTab on CategorySetupTab {
  bool get containsCategories {
    return this == CategorySetupTab.Categories;
  }

  bool get containsAdditionCategories {
    return this == CategorySetupTab.AdditionCategories;
  }
}

class CategorySetupArgs {
  CategorySetupArgs({
    this.initialTab = CategorySetupTab.Categories,
    @required this.kind,
    @required this.title,
  });
  //Set the initial tab.
  final CategorySetupTab initialTab;
  // 設定 API 使用類型
  // 0: 店內
  // 1: 線上
  // 2: 零售
  final num kind;
  final String title;
}

class CategorySetupController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final _deleting = <num>[].obs;
  final _updating = <num>[].obs;
  final ProductProvider productProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  BoxProvider get boxProvider => productProvider.boxProvider;

  final categories = <Category>[].obs;
  final additionCategories = <Category>[].obs;

  final _currentTab = CategorySetupTab.Categories.obs;
  CategorySetupTab get currentTab => _currentTab.value;

  CategorySetupArgs args;

  String get title => args.title;

  final _categoryReq = Category().obs;
  Category get categoryReq => _categoryReq.value;

  CategorySetupController({
    @required this.productProvider,
  });

  @override
  void onInit() {
    super.onInit();
    args = Get.arguments;
    _currentTab.value = args?.initialTab ?? CategorySetupTab.Categories;
    // 監聽快取，變動時更新顯示
    final box = boxProvider.getGsBox(kBoxCategory);
    box
        .watch()
        .debounce(1.seconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      final it = List.from(box.getValues(), growable: false)
          .map((e) => Category.fromJson(e))
          .where((element) => element.kind == args.kind);
      categories.assignAll(it);
    });
    // 監聽快取，變動時更新顯示
    final additionBox = boxProvider.getGsBox(kBoxAdditionCategory);
    additionBox
        .watch()
        .debounce(1.seconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      final it = List.from(additionBox.getValues(), growable: false)
          .map((e) => Category.fromJson(e))
          .where((element) => element.kind == args.kind);
      additionCategories.assignAll(it);
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> _fetch() async {
    if (currentTab == CategorySetupTab.Categories) {
      await _fetchCategories();
    } else if (currentTab == CategorySetupTab.AdditionCategories) {
      await _fetchAdditionCategories();
    }
  }

  Future<void> onRefresh() async {
    try {
      _currentTab.value = args?.initialTab ?? CategorySetupTab.Categories;
      await _fetch();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<void> _fetchCategories() async {
    final ret = await productProvider.getCategories(args.kind.productKind);
    categories.assignAll(ret);
  }

  Future<void> _fetchAdditionCategories() async {
    final ret =
        await productProvider.getAdditionCategories(args.kind.productKind);
    additionCategories.assignAll(ret);
  }

  void onTabTap(num tabIndex) {
    // 這會讓顯示頁面切換
    switch (tabIndex) {
      case 0:
        _currentTab.value = CategorySetupTab.Categories;
        break;
      case 1:
        _currentTab.value = CategorySetupTab.AdditionCategories;
        break;
    }
  }

  // 嘗試新增一格。
  Future<bool> addNewCategory() async {
    if (categoryReq.name == null || categoryReq.name.isEmpty) {
      return false;
    }
    // 取得數值最大 sort
    final sort = categories.fold<num>(0, (previousValue, element) {
      element.sort ??= 0;
      return max(previousValue, element.sort);
    });
    // 一定新增最後一個
    categoryReq.sort = sort + 1;
    categoryReq.kind = args.kind;
    final ret = await productProvider.postCategories(categoryReq);
    if (ret != null && ret != 0) {
      // 產生新的草稿
      _categoryReq.value = Category();
      return true;
    }
    return false;
  }

  // 移動某格至某格
  void sortingCategory(int srcIndex, int destIndex) {
    final list = categories;
    if (srcIndex < destIndex) {
      // 排序不超過新增
      if (destIndex <= list.length) {
        final element = list.elementAt(srcIndex);
        list.insert(destIndex, element);
        list.removeAt(srcIndex);
      }
    } else {
      final element = list.removeAt(srcIndex);
      list.insert(destIndex, element);
    }
    for (var i = 0; i < list.length; i++) {
      final element = list.elementAt(i);
      element.sort = i;
    }
  }

  // 嘗試新增一格。
  Future<bool> addNewAdditionCategory() async {
    if (categoryReq.name == null || categoryReq.name.isEmpty) {
      return false;
    }
    // 取得數值最大 sort
    final sort = additionCategories.fold<num>(0, (previousValue, element) {
      element.sort ??= 0;
      return max(previousValue, element.sort);
    });
    // 一定新增最後一個
    categoryReq.sort = sort + 1;
    categoryReq.kind = args.kind;
    final ret = await productProvider.postAdditionCategories(categoryReq);
    if (ret is num && ret > 0) {
      // 產生新的草稿
      _categoryReq.value = Category();
      return true;
    }
    return false;
  }

  void deletingAdditionCategories(num id) {
    _deleting.add(id);
    additionCategories.removeWhere((element) => element.id == id);
  }

  // 移動某格至某格
  void sortAdditionCategory(int srcIndex, int destIndex) {
    final list = additionCategories;
    if (srcIndex < destIndex) {
      // 排序不超過新增
      if (destIndex <= list.length) {
        final element = list.elementAt(srcIndex);
        list.insert(destIndex, element);
        list.removeAt(srcIndex);
      }
    } else {
      final element = list.removeAt(srcIndex);
      list.insert(destIndex, element);
    }
    for (var i = 0; i < list.length; i++) {
      final element = list.elementAt(i);
      element.sort = i;
    }
  }

  Future<bool> deletingCategories(num id) async {
    final filter = ProductsGetQry(
      page: 1,
      limit: 500,
      categoryId: id,
      kind: args.kind,
    );
    final products = await productProvider.getProducts(filter);
    if (products.isNotEmpty) {
      final description = products.map((e) => e.title).join(', ');
      throw '須先刪除此分類以下商品:\n$description';
    }
    _deleting.add(id);
    categories.removeWhere((element) => element.id == id);
    return true;
  }

  Future<bool> _deleteCategories() async {
    if (_deleting.isNotEmpty) {
      final ids = _deleting.toSet();
      _deleting.clear();
      for (var id in ids) {
        await productProvider.deleteCategory(id);
      }
    }
    return true;
  }

  // FIXME: 刪除規格時，應一併刪除底下子物件
  Future<bool> _deleteAdditionCategories() async {
    if (_deleting.isNotEmpty) {
      final ids = _deleting.toSet();
      _deleting.clear();
      for (var id in ids) {
        await productProvider.deleteAdditionCategory(id);
      }
    }
    return true;
  }

  void updating(num id) {
    _updating.addIf(!_updating.contains(id), id);
  }

  Future<bool> _updateCategories() async {
    if (_updating.isNotEmpty) {
      final ids = _updating.toSet();
      _updating.clear();
      for (var id in ids) {
        final index = categories.indexWhere((element) => element.id == id);
        if (index >= 0) {
          final element = categories.elementAt(index);
          await productProvider.putCategories(element);
        }
      }
    }
    return true;
  }

  Future<bool> _submitCategories() async {
    // 更新
    await _updateCategories();
    // 刪除
    await _deleteCategories();
    // 新增
    await addNewCategory();
    // 排序
    await _applySortingCategories();
    // 更新舊快取
    return true;
  }

  Future<bool> _submitAdditionCategories() async {
    // 刪除
    await _deleteAdditionCategories();
    // 新增
    await addNewAdditionCategory();
    // 排序
    await _sortAdditionCategories();
    return true;
  }

  Future<bool> submit() async {
    if (currentTab == CategorySetupTab.Categories) {
      return _submitCategories();
    } else if (currentTab == CategorySetupTab.AdditionCategories) {
      return _submitAdditionCategories();
    }
    return true;
  }

  Future<bool> _applySortingCategories() {
    final it =
        categories.map((element) => IDSort(id: element.id, sort: element.sort));
    return productProvider.sortCategories(it);
  }

  Future<bool> _sortAdditionCategories() {
    final it = additionCategories
        .map((element) => IDSort(id: element.id, sort: element.sort));
    return productProvider.sortAdditionCategories(it);
  }
}
