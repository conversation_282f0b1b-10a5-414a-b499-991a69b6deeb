import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/composite_edit_item.dart';
import 'package:muyipork/app/components/custom_tab_bar.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/modules/addition_category_setup/controllers/addition_category_setup_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/category_setup_controller.dart';

class CategorySetupView extends GetView<CategorySetupController> {
  // @override
  // final String tag;

  CategorySetupView({
    Key key,
  }) : // tag = Get.parameters[Keys.Tag],
        super(key: key) {
    // Get.lazyPut(
    //   () => CategorySetupController(
    //     productProvider: Get.find(),
    //   ),
    //   tag: tag,
    // );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.focusScope.unfocus(),
      child: StandardPage(
        titleText: controller.title,
        child: RefreshIndicator(
          onRefresh: controller.onRefresh,
          child: controller.obx(
            (state) {
              return BottomWidgetPage.save(
                onPressed: _submit,
                child: _main(),
              );
            },
            onEmpty: ListWidget.blank(),
            onError: (message) {
              return ListWidget.message(
                message,
                buttonText: '重試',
                onPressed: controller.onRefresh,
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> _submit() async {
    try {
      final ret = await FutureProgress(
        future: controller.submit(),
      ).dialog();
      if (true == ret) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(false, _tabBar());
    children.addIf(true, Expanded(child: _slave()));
    return DefaultTabController(
      length: CategorySetupTab.values.length,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  Widget _tabBar() {
    return ColoredBox(
      color: Color(0xff3e4b5a),
      child: CustomTabBar(
        isScrollable: false,
        tabs: [
          Tab(text: '分類'),
          Tab(text: '規格'),
        ],
        onTap: controller.onTabTap,
      ),
    );
  }

  Widget _slave() {
    return ColoredBox(
      color: kColorBackground,
      child: Obx(() {
        switch (controller.currentTab) {
          case CategorySetupTab.Categories:
            return _categoryList();
          case CategorySetupTab.AdditionCategories:
            return _additionCategoriesList();
          default:
            return SizedBox();
        }
      }),
    );
  }

  Widget _categoryList() {
    final list = controller.categories;
    list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    final children = List.generate(list.length + 1, (index) {
      if (index < list.length) {
        final element = list.elementAt(index);
        return CompositeEditItem(
          CompositeEditItemArgs(
            CompositeContentMode.OneTextField,
            CompositeRightButtonMode.Remove,
            mainTextFieldInit: element.name,
            mainTextFieldHint: '分類名稱',
            mainTextFieldChanged: (value) {
              element.name = value;
              controller.updating(element.id);
            },
            onRightButtonPressed: () async {
              // 刪除
              try {
                return await controller.deletingCategories(element.id);
              } catch (e) {
                return DialogGeneral.alert('$e').dialog();
              }
            },
          ),
          key: Key('$index'),
        );
      }
      // Add new
      return CompositeEditItem(
        CompositeEditItemArgs(
          CompositeContentMode.OneTextField,
          CompositeRightButtonMode.Add,
          mainTextFieldHint: '分類名稱',
          mainTextFieldChanged: (value) {
            logger.d('[CategorySetupView] mainTextFieldChanged: $value');
            controller.categoryReq.name = value;
          },
          onRightButtonPressed: () {
            final name = controller.categoryReq.name ?? '';
            if (name.isEmpty) {
              return DialogGeneral.alert('分類名稱為必填項目').dialog();
            }
            // 新增
            // return controller.addNewCategory();
            // return controller.submit();
            return FutureProgress(
              future: controller.submit(),
            ).dialog();
          },
          showDragHandle: false,
        ),
        key: Key('$index'),
      );
    });

    return ReorderableListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      header: SizedBox(height: kPadding),
      children: children,
      onReorder: (srcIndex, destIndex) {
        // asc (0, 2)
        // desc (1, 0)
        controller.sortingCategory(srcIndex, destIndex);
      },
    );
  }

  Widget _additionCategoriesList() {
    final list = controller.additionCategories;
    list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    final children = List<Widget>.generate(list.length + 1, (index) {
      if (index < list.length) {
        final element = list.elementAt(index);
        return CompositeEditItem(
          CompositeEditItemArgs(
            CompositeContentMode.ParentButton,
            CompositeRightButtonMode.Remove,
            mainButtonText: element.name,
            onMainButtonPressed: () {
              Get.toNamed(
                Routes.ADDITION_CATEGORY_SETUP,
                arguments: AdditionCategorySetupArgs(
                  category: element,
                  kind: controller.args.kind,
                ),
              );
            },
            onRightButtonPressed: () async {
              controller.deletingAdditionCategories(element.id);
              return true;
            },
          ),
          key: Key('$index'),
        );
      }
      // Add new
      return CompositeEditItem(
        CompositeEditItemArgs(
          CompositeContentMode.OneTextField,
          CompositeRightButtonMode.Add,
          mainTextFieldHint: '規格名稱',
          mainTextFieldChanged: (value) {
            logger.d('[CategorySetupView] mainTextFieldChanged: $value');
            controller.categoryReq.name = value;
          },
          onRightButtonPressed: () {
            final name = controller.categoryReq.name ?? '';
            if (name.isEmpty) {
              return DialogGeneral.alert('規格名稱為必填項目').dialog();
            }
            // 新增
            // return controller.addNewAdditionCategory();
            return FutureProgress(
              future: controller.submit(),
            ).dialog();
          },
          showDragHandle: false,
        ),
        key: Key('$index'),
      );
    });

    return ReorderableListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      header: SizedBox(height: kPadding),
      children: children,
      //Prevent from move the last one.
      onReorder: (srcIndex, destIndex) {
        // asc (0, 2)
        // desc (1, 0)
        controller.sortAdditionCategory(srcIndex, destIndex);
      },
    );
  }
}
