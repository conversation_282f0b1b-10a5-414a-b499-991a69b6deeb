import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/member_item.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/coupon_item.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/app/modules/orders_confirm/controllers/orders_confirm_controller.dart';
import 'package:muyipork/app/modules/orders_setup/controllers/orders_setup_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

import '../controllers/coupon_detail_controller.dart';

class CouponDetailView extends GetView<CouponDetailController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '優惠券',
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: controller.obx(
          (state) {
            return BottomWidgetPage(
              child: _main(),
              bottom: Visibility(
                visible: controller.buttonBar,
                child: _buttonBar(),
              ),
            );
          },
          onError: ListWidget.message,
        ),
      ),
    );
  }

  List<Widget> _actions2() {
    final ls = <Widget>[];
    ls.addIf(
      true,
      Expanded(
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: const Color(0xff4e647e),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.horizontal(
              left: Radius.circular(9999.9),
            )),
          ),
          onPressed: _orderSetup,
          child: Text(
            '繼續選購',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    ls.addIf(
      true,
      Expanded(
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: OKColor.Tab,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.horizontal(
              right: Radius.circular(9999.9),
            )),
          ),
          onPressed: () {
            Get.back();
          },
          child: Text(
            '取消',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    return ls;
  }

  List<Widget> _actions3() {
    final ls = <Widget>[];
    ls.addIf(
      true,
      Expanded(
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: const Color(0xff4e647e),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.horizontal(
              left: Radius.circular(9999.9),
            )),
          ),
          onPressed: _orderSetup,
          child: Text(
            '繼續選購',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    ls.addIf(
      true,
      Expanded(
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: OKColor.Tab,
            shape: RoundedRectangleBorder(),
          ),
          onPressed: () {
            Get.back();
          },
          child: Text(
            '取消',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    ls.addIf(
      true,
      Expanded(
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: OKColor.Error,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.horizontal(
              right: Radius.circular(9999.9),
            )),
          ),
          onPressed: _orderConfirm,
          child: Text(
            '直接兌換',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    return ls;
  }

  ///
  /// 顯示直接兌換按鈕旗標
  ///
  bool get _useAction3 {
    // 打折及折扣不可直接兌換
    if (controller.coupon.promotionType.promotionType
        .contains([PromotionType.Off, PromotionType.Discount])) {
      return false;
    }
    // 有低消不可直接兌換
    if (controller.coupon.minPrice != null && controller.coupon.minPrice > 0) {
      return false;
    }
    return true;
  }

  Widget _actions() {
    // 線下優惠才可操作
    if (controller.coupon.isAvailable && controller.coupon.orderSource.isApp) {
      return Row(
        children: _useAction3 ? _actions3() : _actions2(),
      );
    }
    return SizedBox.shrink();
  }

  Widget _buttonBar() {
    return Container(
      decoration: const BoxDecoration(
        gradient: const LinearGradient(
          begin: const Alignment(0.0, -1.0),
          end: const Alignment(0.0, -0.45),
          colors: const [
            const Color(0x00FFFFFF),
            Colors.white,
          ],
          stops: const [0.0, 1.0],
        ),
      ),
      padding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: kPadding,
      ),
      child: Obx(() => _actions()),
    );
  }

  ///
  /// 繼續選購
  ///
  void _orderSetup() {
    if (controller.arguments != null &&
        controller.arguments.continuePressed != null) {
      controller.arguments.continuePressed.call(controller.data);
    } else {
      // 預設行為，傳送優惠券參數
      Get.toNamed(
        Routes.ORDERS_SETUP,
        arguments: OrdersSetupArgs(
          kind: controller.productKind.index,
        ),
        parameters: <String, String>{
          Keys.Data: controller.data.toRawJson(),
        },
      );
    }
  }

  ///
  /// 直接兌換
  ///
  void _orderConfirm() {
    if (controller.arguments != null &&
        controller.arguments.checkoutPressed != null) {
      controller.arguments.checkoutPressed.call(controller.data);
    } else {
      // FIXME:
      // 預設行為
      final arguments = OrdersConfirmArgs(
        ordersPostReq: OrdersPostReq(
          memberId: controller.memberId,
          memberCouponId: controller.memberCouponId,
          source: OrderSource.App.index,
        ),
        kind: controller.coupon.storeType.isDinner ? 0 : 2,
      );
      Get.toNamed(
        Routes.ORDERS_CONFIRM,
        arguments: arguments,
      );
    }
  }

  Widget _member() {
    return Obx(() {
      if (controller.member != null) {
        return DecoratedBox(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: const Color(0x29000000),
                offset: Offset(0, 0),
                blurRadius: 6,
              ),
            ],
          ),
          child: MemberItem(
            data: controller.member,
            backgroundColor: const Color(0xfff8f8f8),
          ),
        );
      }
      return SizedBox.shrink();
    });
  }

  Widget _message() {
    return Obx(() {
      if (controller.coupon.isAvailable) {
        return SizedBox.shrink();
      }
      return Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(bottom: 16),
        child: Text(
          controller.coupon.message ?? '',
          style: TextStyle(
            fontSize: 14,
            color: OKColor.Error,
          ),
          textAlign: TextAlign.right,
        ),
      );
    });
  }

  Widget _main() {
    final ls = <Widget>[];
    ls.addIf(false, _member());
    ls.addIf(true, SizedBox(height: kPadding));
    ls.addIf(
      true,
      Center(
        child: CouponItem(
          data: controller.coupon,
        ),
      ),
    );
    ls.addIf(true, SizedBox(height: 16));
    ls.addIf(true, _message());
    ls.addIf(true, _description());
    ls.addIf(true, SizedBox(height: kBottomButtonPadding));
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      // padding: EdgeInsets.symmetric(
      //   horizontal: kPadding,
      // ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: ls,
      ),
    );
  }

  Widget _description() {
    final ls = <Widget>[];
    // ls.addIf(true, SizedBox(height: 22));
    ls.addIf(
      controller?.coupon?.isLimited ?? false,
      Center(
        child: Text(
          '此為限量優惠券，尚有${controller?.coupon?.lastCount ?? 0}份，請儘速使用!',
          style: TextStyle(
            // fontFamily: 'PingFang TC',
            fontSize: 16,
            color: OKColor.Error,
            fontWeight: FontWeight.w500,
            // height: 1.875,
          ),
          // textHeightBehavior:
          //     TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.center,
        ),
      ),
    );
    ls.addIf(
      controller?.coupon?.isLimited ?? false,
      SizedBox(height: 32),
    );
    ls.addIf(
      true,
      Text(
        '優惠券名稱',
        style: TextStyle(
          // fontFamily: 'PingFang TC',
          fontSize: 16,
          // color: const Color(0xff222222),
          color: OKColor.Gray22,
          fontWeight: FontWeight.w500,
          // height: 1.875,
        ),
        // textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        // textAlign: TextAlign.left,
      ).paddingSymmetric(horizontal: kPadding),
    );
    ls.addIf(true, SizedBox(height: 8));
    ls.addIf(
      true,
      Text(
        //'中杯美式咖啡兌換券',
        controller?.coupon?.title ?? '',
        style: TextStyle(
          // fontFamily: 'PingFang TC',
          fontSize: 16,
          color: StatusColor.Normal,
          // height: 1.875,
        ),
        // textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        // textAlign: TextAlign.left,
      ).paddingSymmetric(horizontal: kPadding),
    );
    ls.addIf(true, SizedBox(height: 20));
    ls.addIf(
      true,
      Text(
        '使用期限',
        style: TextStyle(
          // fontFamily: 'PingFang TC',
          fontSize: 16,
          color: OKColor.Gray22,
          fontWeight: FontWeight.w500,
          // height: 1.875,
        ),
        // textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        // textAlign: TextAlign.left,
      ).paddingSymmetric(horizontal: kPadding),
    );
    ls.addIf(true, SizedBox(height: 8));
    ls.addIf(
      true,
      Text(
        // '2021/12/31 00:00止',
        controller?.coupon?.displayLastUseDate ?? '-',
        style: TextStyle(
          // fontFamily: 'PingFang TC',
          fontSize: 16,
          color: StatusColor.Normal,
          // height: 1.875,
        ),
        // textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        // textAlign: TextAlign.left,
      ).paddingSymmetric(horizontal: kPadding),
    );
    ls.addIf(true, SizedBox(height: 20));
    ls.addIf(
      true,
      Text(
        '優惠券說明',
        style: TextStyle(
          // fontFamily: 'PingFang TC',
          fontSize: 16,
          color: OKColor.Gray22,
          fontWeight: FontWeight.w500,
          // height: 1.875,
        ),
        // textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        // textAlign: TextAlign.left,
      ).paddingSymmetric(horizontal: kPadding),
    );
    ls.addIf(true, SizedBox(height: 8));
    ls.addIf(
      true,
      Text(
        // '凡購買任一商品，即可兌換一杯中杯美式咖啡，數量有限換完為止!',
        controller?.coupon?.description ?? '',
        style: TextStyle(
          // fontFamily: 'PingFang TC',
          fontSize: 16,
          color: StatusColor.Normal,
          // height: 1.875,
        ),
        // textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        // textAlign: TextAlign.left,
      ).paddingSymmetric(horizontal: kPadding),
    );
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: ls,
      ).paddingSymmetric(
        vertical: kPadding,
      ),
    );
  }

  // Widget _coupon() {
  //   return Container(
  //     width: 250,
  //     height: 115,
  //     decoration: ShapeDecoration(
  //       color: Colors.red,
  //       shape: CouponShapeBorder(),
  //     ),
  //   );
  // }
}

// extension ExtensionCouons on Coupon {
//   String get displayType {
//     return '${kind.storeType.name}';
//   }
// }

class _Vip extends StatelessWidget {
  const _Vip({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 2,
        horizontal: 4,
      ),
      decoration: ShapeDecoration(
        color: OKColor.Vip,
        shape: StadiumBorder(
          side: BorderSide(
            width: 1.0,
            color: Colors.white,
          ),
        ),
      ),
      child: Text(
        'VIP',
        style: TextStyle(
          fontSize: 10,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
