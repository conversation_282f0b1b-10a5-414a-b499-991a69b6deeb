import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/coupons_req.dart';
import 'package:muyipork/enums.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

class CouponsController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final _disposable = Completer();
  final CouponProvider couponProvider;
  final _title = ''.obs;
  // pagi
  final _pagi = Pagination().obs;
  num get more => _pagi.value.needToLoadMore ? 1 : 0;
  // data
  final _data = <Coupon>[].obs;
  Iterable<Coupon> get data {
    return _data.where((element) => element.isAvailable);
  }

  // fileter
  final _filter = CouponsReq().obs;

  String get title => '發放「$_title2」';

  String get _title2 {
    if (_title.value != null && _title.value.isNotEmpty) {
      return _title.value;
    }
    return '優惠券';
  }

  final checked = <num>[].obs;

  CouponsController({
    @required this.couponProvider,
  });

  final _memberId = 0.obs;

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.Title)) {
      _title.value = Get.parameters[Keys.Title];
    }
    _filter.stream
        .tap((event) => change('', status: RxStatus.loading()))
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.Id)) {
      _memberId.value = num.tryParse(Get.parameters[Keys.Id]);
    }
    if (Get.parameters.containsKey('source')) {
      final source = num.tryParse(Get.parameters['source']);
      _filter.value.source = source.orderSource;
    }
    if (Get.parameters.containsKey('type')) {
      final type = num.tryParse(Get.parameters['type']);
      _filter.value.type = type.storeType;
    }
    _filter.refresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  @override
  Future<void> onEndScroll() async {
    if (_pagi.value.needToLoadMore) {
      _filter.value.page = _pagi.value.nextPage;
      await couponProvider.getCoupons(_filter.value).then(
        (value) {
          _pagi.value.currentPage = _filter.value.page;
          _data.addAllIf(true, value);
        },
        onError: (error) {
          _pagi.value.lastPage = _pagi.value.currentPage;
        },
      ).whenComplete(() => _refreshPage());
    }
  }

  void _refreshPage() {
    // 特殊: cache 有東西，設定成功，觸發 onEndScroll 繼續讀取
    final isEmpty = _pagi.value.needToLoadMore
        ? couponProvider.cached.isEmpty
        : data.isEmpty;
    change('', status: isEmpty ? RxStatus.empty() : RxStatus.success());
  }

  @override
  Future<void> onTopScroll() async {
    // nothing...
  }

  Future<void> onRefresh() async {
    _filter.value.page = 1;
    await couponProvider.getCoupons(_filter.value).then(
      (value) {
        _pagi.value.currentPage = _filter.value.page;
        _pagi.value.lastPage = null;
        _data.clear();
        _data.addAll(value);
        _refreshPage();
      },
      onError: (error) {
        change('', status: RxStatus.error('$error'));
      },
    );
  }

  Future<bool> submit() {
    return couponProvider.postMemberCoupons(_memberId.value, checked);
  }
}

extension ExtensionCouponsReq on CouponsReq {
  set source(OrderSource value) {
    switch (value) {
      case OrderSource.App:
        isOnline = 0;
        break;
      case OrderSource.Line:
        isOnline = 1;
        break;
      case OrderSource.Max:
        isOnline = null;
        break;
    }
  }

  set type(StoreType value) {
    switch (value) {
      case StoreType.Dinner:
        kind = 1;
        break;
      case StoreType.Retail:
        kind = 2;
        break;
      case StoreType.Max:
        kind = null;
        break;
    }
  }
}
