import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/coupon_item.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

import '../controllers/coupons_controller.dart';

class CouponsView extends GetView<CouponsController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: controller.title,
      child: controller.obx(
        (state) {
          return BottomWidgetPage(
            child: _main(),
            bottom: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 8.0,
                horizontal: kPadding,
              ),
              child: _YesNoButton(
                left: _ButtonArgs(
                  text: '取消',
                  onPressed: () {
                    Get.back();
                  },
                ),
                right: _ButtonArgs(
                  text: '確認',
                  onPressed: _submit,
                ),
              ),
            ),
          );
        },
        onError: ListWidget.message,
        onEmpty: ListWidget.message('沒有資料'),
      ),
    );
  }

  void _submit() {
    FutureProgress(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (true == value) {
          Get.back();
        }
      },
    );
  }

  Widget _main() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            '請選擇欲發放的優惠券',
            style: TextStyle(
              fontSize: 16,
              color: OKColor.Primary,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: _list(),
        ),
      ],
    );
  }

  Widget _list() {
    return RefreshIndicator(
      onRefresh: controller.onRefresh,
      child: Obx(() {
        final data = controller.data;
        return ListView.separated(
          padding: EdgeInsets.only(bottom: kBottomButtonPadding),
          controller: controller.scroll,
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: data.length + controller.more,
          itemBuilder: (context, index) {
            if (index < data.length) {
              final element = data.elementAt(index);
              return Center(
                child: Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    CouponItem(
                      data: element,
                      onPressed: () {
                        Get.toNamed(
                          Routes.COUPON_DETAIL,
                          parameters: <String, String>{
                            Keys.Id: '${element.id}',
                            // Keys.Data: QrFormat(
                            //   // memberId: controller.id,
                            //   memberCouponId: element.id,
                            // ).toRawJson(),
                            'actions': '0',
                          },
                        );
                      },
                    ).paddingOnly(
                      left: 12,
                    ),
                    Obx(() {
                      return Container(
                        width: 26,
                        height: 26,
                        alignment: Alignment.center,
                        color: Colors.white,
                        child: Transform.scale(
                          scale: 1.4,
                          child: Checkbox(
                            value: controller.checked.contains(element.id),
                            onChanged: (value) {
                              if (value) {
                                controller.checked.add(element.id);
                              } else {
                                controller.checked.remove(element.id);
                              }
                            },
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              );
            }
            controller.onEndScroll();
            return ListWidget.bottomProgressing();
          },
          separatorBuilder: (context, index) {
            return SizedBox(height: 16);
          },
        );
      }),
    );
  }
}

class _ButtonArgs {
  final Function onPressed;
  final String text;
  // final Color backgroundColor;

  _ButtonArgs({
    this.onPressed,
    this.text,
    // this.backgroundColor,
  });
}

class _YesNoButton extends StatelessWidget {
  final _ButtonArgs left;
  final _ButtonArgs right;

  const _YesNoButton({
    Key key,
    this.left,
    this.right,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ls = <Widget>[];
    ls.addIf(true, Expanded(child: _leftButton()));
    ls.addIf(true, Expanded(child: _rightButton()));
    return Row(
      children: ls,
    );
  }

  Widget _rightButton() {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(9999.9),
          bottomRight: Radius.circular(9999.9),
        ),
        gradient: LinearGradient(
          begin: Alignment(-1.0, 0.0),
          end: Alignment(1.0, 0.0),
          colors: [
            OKColor.Primary,
            OKColor.PrimaryWeight,
          ],
          stops: [0.0, 1.0],
        ),
      ),
      child: TextButton(
        style: TextButton.styleFrom(
          minimumSize: Size.fromHeight(kButtonHeight),
          padding: EdgeInsets.zero,
          // backgroundColor: const Color(0xff3e4b5a),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.horizontal(
              right: Radius.circular(9999.9),
            ),
          ),
        ),
        onPressed: right.onPressed,
        child: Text(
          right.text ?? '確認',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _leftButton() {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(9999.9),
        ),
        color: OKColor.Tab,
        // gradient: LinearGradient(
        //   begin: Alignment(-1.0, 0.0),
        //   end: Alignment(1.0, 0.0),
        //   colors: [
        //     OKColor.Primary,
        //     OKColor.PrimaryWeight,
        //   ],
        //   stops: [0.0, 1.0],
        // ),
      ),
      child: TextButton(
        onPressed: left.onPressed,
        style: TextButton.styleFrom(
          minimumSize: Size.fromHeight(kButtonHeight),
          padding: EdgeInsets.zero,
          // backgroundColor: const Color(0xff3e4b5a),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.horizontal(
              left: Radius.circular(9999.9),
            ),
          ),
        ),
        child: Text(
          left.text ?? '取消',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
