import 'package:get/get.dart';
import 'package:muyipork/app/models/payment_ecpay.dart';
import 'package:muyipork/app/models/payment_ecpay_put.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/enums.dart';

class EcpayController extends GetxController with StateMixin<String> {
  final ApiProvider apiProvider;
  final _draft = Rx<PaymentEcpayPut>(null);
  PaymentEcpayPut get draft => this._draft.value;
  final _data = Rx<PaymentEcpay>(null);
  PaymentEcpay get data => _data.value;

  EcpayController({
    this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  Future<void> onRefresh() async {
    try {
      _data.value = await apiProvider.getData<PaymentEcpay>(
        unencodedPath: 'setting/payment/ecpay',
        creator: (value) {
          try {
            return PaymentEcpay.fromJson(value);
          } catch (e) {
            final ret = PaymentEcpay();
            ret.init();
            return ret;
          }
        },
      );
      // 轉換成可編輯物件(草稿)
      _draft.value = data.asPaymentEcpayPut();
      await 20.milliseconds.delay();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  void onClose() {}

  void refreshData() {
    _draft.refresh();
  }

  Future<bool> submit() {
    draft.init();
    return apiProvider.put<bool>(
      'setting/payment/ecpay',
      data: draft.toJson(),
      creator: (data) {
        if (data.containsKey(Keys.IsUpdated) && true == data[Keys.IsUpdated]) {
          return true;
        }
        return null;
      },
    );
  }
}

extension _ExtensionPaymentEcpay on PaymentEcpay {
  void init() {
    creditStatus ??= Switcher.Off.index;
    description ??= '';
    setting ??= Setting(
      hashIv: '',
      hashKey: '',
      merchantId: '',
    );
  }

  PaymentEcpayPut asPaymentEcpayPut() {
    // HACK: test
    // setting = null;
    return PaymentEcpayPut(
      merchantId: setting?.merchantId ?? '',
      hashKey: setting?.hashKey ?? '',
      hashIv: setting?.hashIv ?? '',
      description: description ?? '',
      creditStatus: creditStatus ?? Switcher.Off.index,
    );
  }
}

extension _ExtensionPaymentEcpayPut on PaymentEcpayPut {
  void init() {
    creditStatus ??= Switcher.Off.index;
    description ??= '';
    hashIv ??= '';
    hashKey ??= '';
    merchantId ??= '';
  }
}
