import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/save_page.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/ecpay_controller.dart';

class EcpayView extends GetView<EcpayController> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '綠界金流設定',
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: controller.obx(
          (state) {
            return SavePage(
              onPressed: _submit,
              child: RefreshIndicator(
                onRefresh: controller.onRefresh,
                child: Obx(() => _main()),
              ),
            );
          },
          onError: ListWidget.message,
        ),
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用綠界金流',
        value: controller.draft.creditStatus.switcher.isOn,
        onChanged: (value) {
          controller.draft.creditStatus = value ? 1 : 0;
          controller.refreshData();
        },
      ),
    );
    children.addIf(true, SettingsWidget.space());
    children.addIf(
      controller.draft.creditStatus.switcher.isOn,
      Expanded(
        child: Form(
          key: _formKey,
          child: _list(),
        ),
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: children,
    );
  }

  Widget _list() {
    final children = <Widget>[];
    children.addIf(false, const SizedBox(height: 12.0));
    children.addAllIf(true, _page1());
    children.addAllIf(false, _page2());
    children.addAllIf(false, _page3());
    children.addAllIf(false, _page4());
    children.addAllIf(false, _page5());
    children.addIf(true, const SizedBox(height: kBottomButtonPadding));
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: children,
    );
  }

  void _submit() {
    FutureProgress(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (value != null) {
          Get.back();
        }
      },
    );
  }

  List<Widget> _page5() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '超商條碼',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用「餐飲」網路商店下單',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用「零售」網路商店下單',
      ),
    );
    children.addIf(true, SettingsWidget.space());
    return children;
  }

  List<Widget> _page4() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '超商代碼',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用「餐飲」網路商店下單',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用「零售」網路商店下單',
      ),
    );
    children.addIf(true, SettingsWidget.space());
    return children;
  }

  List<Widget> _page3() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '網路ATM',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用「餐飲」網路商店下單',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用「零售」網路商店下單',
      ),
    );
    children.addIf(true, SettingsWidget.space());
    return children;
  }

  List<Widget> _page2() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '信用卡一次付清',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用「餐飲」網路商店下單',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用「零售」網路商店下單',
      ),
    );
    children.addIf(true, SettingsWidget.space());
    return children;
  }

  List<Widget> _page1() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '綠界基本資料',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.input(
        initialValue: controller.draft.merchantId,
        labelText: '商店代號',
        hintText: '請輸入商店代號',
        onChanged: (value) {
          controller.draft.merchantId = value;
        },
      ),
    );
    children.addIf(
      true,
      SettingsWidget.input(
        initialValue: controller.draft.hashKey,
        labelText: 'HashKey',
        hintText: '請輸入HashKey',
        onChanged: (value) {
          controller.draft.hashKey = value;
        },
      ),
    );
    children.addIf(
      true,
      SettingsWidget.input(
        initialValue: controller.draft.hashIv,
        labelText: 'HashIV',
        hintText: '請輸入HashIV',
        onChanged: (value) {
          controller.draft.hashIv = value;
        },
      ),
    );
    children.addIf(
      true,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          titleText: '綠界備註',
          // hintText: '綠界備註',
          initialValue: controller.draft.description,
          onChanged: (value) {
            controller.draft.description = value;
          },
        ),
      ),
    );
    children.addIf(true, SettingsWidget.space());
    return children;
  }
}
