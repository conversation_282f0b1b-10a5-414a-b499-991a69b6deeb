import 'package:flutter/foundation.dart' show AsyncValueGetter, required;
import 'package:flutter_godex_printer/flutter_godex_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/keys.dart';
import 'package:screenshot/screenshot.dart';

class GodexPrinterCategorySelectionController extends GetxController
    with StateMixin<String> {
  final PrinterProvider printerProvider;
  final ProductProvider productProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  final dinnerApp = <Category>[].obs;
  final dinnerLine = <Category>[].obs;
  final retail = <Category>[].obs;

  final _draft = Rx<SettingLabel>(null);
  SettingLabel get draft => _draft.value;
  Function get refreshDraft => _draft.refresh;

  final previewStickerScreenshotControllers = ScreenshotController();

  GodexPrinterCategorySelectionController({
    @required this.productProvider,
    @required this.printerProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.Data)) {
      _draft.value = SettingLabel.fromRawJson(Get.parameters[Keys.Data]);
      change('', status: RxStatus.success());
    } else {
      change('', status: RxStatus.error('no data'));
    }
    // _printerAliasEditingController.value = TextEditingController(
    //   text: draft?.name ?? '',
    // );
  }

  @override
  void onReady() {
    super.onReady();
    // _loadFromLocalStorage();
    onRefresh();
  }

  @override
  void onClose() {}

  //列印預覽工作單
  Future<bool> printPreviewTagSticker() async {
    try {
      final image = await previewStickerScreenshotControllers.capture(
        //這個魔術數字必須要調整到各裝置適合的比例數字.
        pixelRatio: 1.0,
        delay: 20.milliseconds,
      );
      if (image != null) {
        // 丟到列印佇列
        // draft.pushTask(image);
        // 即時列印
        final printer = draft.asGodexPrinter();
        await printer.printImages([image]);
        final status = await printer.checkStatus;
        await 600.milliseconds.delay();
        if ([PrinterStatus.Okay, PrinterStatus.Error50].contains(status)) {
          return true;
        }
        throw status.name;
      }
      return false;
    } catch (e) {
      throw e;
    }
  }

  ///
  /// 嘗試儲存名稱
  ///
  // Future<bool> savePrinterAliasName() async {
  //   //嘗試儲存名稱 (不可留空)
  //   final nextName = printerAliasEditingController.text;
  //   if (nextName != null && nextName.isNotEmpty) {
  //     if (true == await FGoDex.setPrinterAliasName(draft.ip, nextName)) {
  //       draft.name = nextName;
  //       // update cache
  //       if (printerProvider.cached.containsKey(draft.uuid)) {
  //         printerProvider.cached[draft.uuid].name = nextName;
  //       }
  //       return true;
  //     }
  //   }
  //   return false;
  // }

  //自動感測
  // Future<bool> autoCalibrate() => FGoDex.autoCalibrate(draft.ip);
  AsyncValueGetter<bool> get autoCalibrate =>
      draft.asGodexPrinter().autoCalibrate;

  //嘗試重置印表機
  // Future<bool> resetPrinter() => FGoDex.resetPrinter(draft.ip);
  AsyncValueGetter<bool> get resetPrinter => draft.asGodexPrinter().reset;

  //Prototype 列印字串 (This is hard!)
  // Future<bool> testPrintPrototype() async {
  //   if (true == await openPort(draft.ip, 1)) {
  //     await sendCommand('^L');
  //     await sendCommand('AZ1,54,62,1,1,0,0,測試中文');
  //     await sendCommand('AZ1,54,91,1,1,0,0,Test');
  //     await sendCommand('AZ1,54,120,1,1,0,0,Chinese');
  //     await sendCommand('E');
  //     await close();
  //     return true;
  //   }
  //   return false;
  // }

  // void _loadFromLocalStorage() {
  //   dinnerApp.clear();
  //   dinnerApp.addAll(productProvider.getLocalCategories(ProductKind.DinnerApp));
  //   dinnerLine.clear();
  //   dinnerLine
  //       .addAll(productProvider.getLocalCategories(ProductKind.DinnerLine));
  //   retail.clear();
  //   retail.addAll(productProvider.getLocalCategories(ProductKind.Retail));
  //   change('', status: RxStatus.success());
  // }

  //更新資料
  Future<void> onRefresh() async {
    try {
      dinnerApp.assignAll(
          productProvider.getCategoriesFromStorage(ProductKind.DinnerApp));
      dinnerLine.assignAll(
          productProvider.getCategoriesFromStorage(ProductKind.DinnerLine));
      retail.assignAll(
          productProvider.getCategoriesFromStorage(ProductKind.Retail));
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit() {
    // printerProvider.cached[draft.uuid] = draft;
    // Get.back(result: draft.toRawJson());
    if (draft.id is num && draft.id > 0) {
      // 更新
      return printerProvider.putSettingLabels(draft);
    }
    // 新增
    return printerProvider.postSettingLabels(draft);
  }

  Future<bool> delete() {
    if (draft.id is num && draft.id > 0) {
      // 刪除遠端
      return printerProvider.deleteSettingLabels(draft);
    }
    // 刪除本地
    return Future.value(true);
  }
}
