import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/amount_edit.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/checkbox_item.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/sticker_widget.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:screenshot/screenshot.dart';

import '../controllers/godex_printer_category_selection_controller.dart';

class GodexPrinterCategorySelectionView
    extends GetView<GodexPrinterCategorySelectionController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.focusScope.unfocus(),
      child: StandardPage(
        actions: _actions(),
        titleText: '標籤機詳情',
        // title: Obx(() {
        //   final titleText = controller?.data?.name ?? '-';
        //   return StandardPage.titleWidget(titleText);
        // }),
        child: controller.obx(
          (state) {
            return BottomWidgetPage.save(
              onPressed: _submit,
              child: Obx(() => _main()),
            );
          },
          onError: ListWidget.message,
        ),
      ),
    );
  }

  List<Widget> _actions() {
    final children = <Widget>[];
    children.addIf(
      true,
      TextButton(
        onPressed: _onRemovePressed,
        child: Text(
          '刪除',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
      ),
    );
    return children;
  }

  void _onRemovePressed() {
    final completer = Completer<bool>();
    DialogGeneral.quest(
      '即將刪除標籤機',
      mainButtonText: '刪除',
      secondaryButtonText: '取消',
      onMainButtonPressed: () {
        completer.complete(true);
      },
      onSecondaryButtonPress: () {
        completer.complete(false);
      },
    ).dialog().then(
      (value) async {
        if (true == await completer.future) {
          _delete();
        }
      },
    );
  }

  void _delete() {
    FutureProgress(
      future: controller.delete(),
    ).dialog().then(
      (value) {
        if (true == value) {
          Get.back(result: DataAction.Delete);
        }
      },
    );
  }

  void _submit() {
    FutureProgress(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (true == value) {
          final update = controller.draft.id is num && controller.draft.id > 0;
          Get.back(result: update ? DataAction.Update : DataAction.Create);
        }
      },
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, _screenshot());
    children.addIf(
      true,
      ColoredBox(
        color: kColorBackground,
        child: SizedBox.expand(),
      ),
    );
    children.addIf(
      true,
      ListView(
        padding: EdgeInsets.only(
          top: kPadding,
          bottom: kBottomButtonPadding,
        ),
        physics: AlwaysScrollableScrollPhysics(),
        children: _children(),
      ),
    );
    // for debug
    children.addIf(false, _screenshot());
    return Stack(
      alignment: Alignment.center,
      children: children,
    );
  }

  Widget _screenshot() {
    return Screenshot(
      controller: controller.previewStickerScreenshotControllers,
      child: StickerWidget(
        data: Sticker(
          tagStyle: StickerStyle.Filled.index,
          headerTitleNum: '123',
          headerTitleBlockText: '內用',
          headerTitleNote: '2F01',
          headerSubTitle: '時間: 12:34',
          headerSubTitleNote: 8888.currencyStyle,
          bodyTitle: '商品名稱顯示在這邊',
          bodySubTitle: '商品規格1, 商品規格2, 商品規格3, 商品規格4',
          // bodyContent: 'bodyContent',
        ),
      ),
    );
  }

  void _printPreview() {
    FutureProgress(
      future: controller.printPreviewTagSticker(),
    ).dialog().then((value) {
      if (true == value) {
        //
      } else {
        //
      }
    });
  }

  List<Widget> _children() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.input(
        labelText: '名稱',
        hintText: '請輸入名稱',
        initialValue: controller.draft.name,
        onChanged: (value) {
          controller.draft.name = value;
        },
      ),
    );
    children.addIf(
      true,
      Container(
        padding: EdgeInsets.symmetric(horizontal: kPadding, vertical: 8),
        color: Colors.white,
        child: TextButton(
          style: ButtonStyle(
            minimumSize: MaterialStateProperty.all(Size(double.infinity, 30.0)),
            padding: MaterialStateProperty.all(EdgeInsets.zero),
            shape: MaterialStateProperty.all(StadiumBorder(
              side: BorderSide(color: const Color(0xffdbdbea)),
            )),
            textStyle: MaterialStateProperty.all(Get.textTheme.subtitle1),
            backgroundColor: MaterialStateProperty.all(const Color(0xffeeeef3)),
          ),
          onPressed: _printPreview,
          child: Text(
            '列印測試頁',
            style: TextStyle(
              fontSize: 14,
              color: const Color(0xff3e4b5a),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
    children.addIf(
      true,
      SizedBox(height: 12),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '開啟列印功能',
        value: controller.draft.status.switcher.isOn,
        onChanged: (value) {
          controller.draft.status = value.switcher.index;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      true,
      SettingsWidget.space(),
    );
    children.addIf(
      true,
      SizedBox(height: 2),
    );
    children.addAllIf(
      controller.draft.status.switcher.isOn,
      _categories(),
    );
    return children;
  }

  Iterable<Widget> _categories() {
    final list = <Widget>[];
    list.addAllIf(
      controller.prefProvider.brandsType.containsDinner,
      _dinnerApp(),
    );
    list.addAllIf(
      controller.prefProvider.brandsType.containsDinner,
      _dinnerLine(),
    );
    list.addAllIf(
      controller.prefProvider.brandsType.containsRetail,
      _dinnerRetail(),
    );
    return list;
  }

  Iterable<Widget> _dinnerApp() {
    final list = <Widget>[];
    list.addIf(true, _header('餐飲店內分類'));
    list.addAllIf(true, controller.dinnerApp.map((e) => _cate(e)));
    return list;
  }

  Iterable<Widget> _dinnerLine() {
    final list = <Widget>[];
    list.addIf(true, _header('餐飲線上分類'));
    list.addAllIf(true, controller.dinnerLine.map((e) => _cate(e)));
    return list;
  }

  Iterable<Widget> _dinnerRetail() {
    final list = <Widget>[];
    list.addIf(true, _header('零售分類'));
    list.addAllIf(true, controller.retail.map((e) => _cate(e)));
    return list;
  }

  static Widget _header(String text) {
    return Text(text ?? '').paddingOnly(
      left: kPadding,
      top: 8,
      bottom: 8,
    );
  }

  Widget _cate(Category element) {
    final children = <Widget>[];
    children.add(SizedBox(width: 8));
    children.add(
      Expanded(
        child: CheckboxItem(
          checkboxTitle: element.name,
          checkboxValue: controller.draft.containsCategory(element.id),
          onCheckboxChanged: (b) {
            controller.draft.setHasCategory(element.id, b);
            controller.refreshDraft();
          },
        ),
      ),
    );
    controller.draft.categorySettings['${element.id}'] ??= CategorySetting();
    final categorySettings = controller.draft.categorySettings['${element.id}'];
    children.addIf(
      controller.draft.containsCategory(element.id),
      AmountEdit(
        editingValue: categorySettings.printCount ?? 1,
        onChanged: (value) {
          categorySettings.printCount = max(1, value);
        },
      ),
    );
    children.add(SizedBox(width: kPadding));
    return ColoredBox(
      color: Colors.white,
      child: Row(children: children),
    );
  }

  //Display printer info
  // Widget _printerInfo(BuildContext context) {
  //   return Padding(
  //     padding: kContentPadding,
  //     child: Column(
  //       mainAxisSize: MainAxisSize.min,
  //       crossAxisAlignment: CrossAxisAlignment.stretch,
  //       children: _others(context),
  //     ),
  //   );
  // }

  // Widget _cats(Iterable<Category> it) {
  //   return SliverFixedExtentList(
  //     itemExtent: kButtonHeight,
  //     delegate: SliverChildBuilderDelegate(
  //       (context, index) {
  //         final element = it.elementAt(index);
  //         return Obx(() => CheckboxItem(
  //               checkboxTitle: element.name,
  //               checkboxValue: controller.draft.containsCategory(element.id),
  //               onCheckboxChanged: (b) {
  //                 print('onCheckboxChanged [' +
  //                     element.id.toString() +
  //                     '] [' +
  //                     b.toString() +
  //                     ']');
  //                 controller.draft.setHasCategory(element.id, b);
  //                 controller.refreshData();
  //               },
  //             ));
  //       },
  //       childCount: it.length,
  //     ),
  //   );
  // }

  // Widget _header(String titleText) {
  //   return SliverPersistentHeader(
  //     pinned: false,
  //     delegate: _SliverPersistentHeaderDelegate(
  //       titleText: titleText,
  //     ),
  //   );
  // }

  // List<Widget> _slivers(BuildContext context) {
  //   final children = <Widget>[];
  //   children.addIf(
  //     controller.prefProvider.storeType.isDinner,
  //     _header('餐飲店內分類'),
  //   );
  //   children.addIf(
  //     controller.prefProvider.storeType.isDinner,
  //     _cats(controller.dinnerApp),
  //   );
  //   children.addIf(
  //     controller.prefProvider.storeType.isDinner,
  //     _header('餐飲線上分類'),
  //   );
  //   children.addIf(
  //     controller.prefProvider.storeType.isDinner,
  //     _cats(controller.dinnerLine),
  //   );
  //   children.addIf(
  //     controller.prefProvider.storeType.isRetail,
  //     _header('零售分類'),
  //   );
  //   children.addIf(
  //     controller.prefProvider.storeType.isRetail,
  //     _cats(controller.retail),
  //   );
  //   children.addIf(true, _printerInfo(context).sliverBox);
  //   children.addIf(true, SizedBox(height: kBottomButtonPadding).sliverBox);
  //   return children;
  // }

  // List<Widget> _others(BuildContext context) {
  //   final children = <Widget>[];
  //   children.addIf(true, SizedBox(height: 8));
  //   children.addIf(
  //     true,
  //     Text(
  //       'Mac Address',
  //       style: Get.textTheme.headline6.copyWith(color: kColorPrimary),
  //     ),
  //   );
  //   children.addIf(
  //     true,
  //     Text(
  //       controller?.draft?.macAddress ?? '',
  //       style: Get.textTheme.subtitle1,
  //     ),
  //   );
  //   children.addIf(true, SizedBox(height: 8));
  //   children.addIf(
  //     true,
  //     Text(
  //       'Local IP',
  //       style: Get.textTheme.headline6.copyWith(color: kColorPrimary),
  //     ),
  //   );
  //   children.addIf(
  //     true,
  //     Text(
  //       controller?.draft?.ip ?? '',
  //       style: Get.textTheme.subtitle1,
  //     ),
  //   );
  //   children.addIf(true, SizedBox(height: 8));
  //   children.addIf(
  //     true,
  //     Text(
  //       '列印機網路名稱',
  //       style: Get.textTheme.headline6.copyWith(color: kColorPrimary),
  //     ),
  //   );
  //   children.addIf(
  //     true,
  //     Padding(
  //       padding: const EdgeInsets.all(8.0),
  //       child: TextField(
  //         controller: controller.printerAliasEditingController,
  //         decoration: InputDecoration(
  //           filled: true,
  //           hintText: '列印機網路名稱',
  //           hintStyle: Get.textTheme.bodyText1.copyWith(color: Colors.grey),
  //           helperText: '限 5 中文或 15 英數字元',
  //           border: InputBorder.none,
  //           focusedBorder: InputBorder.none,
  //           enabledBorder: InputBorder.none,
  //           errorBorder: InputBorder.none,
  //           disabledBorder: InputBorder.none,
  //           contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
  //           isDense: true,
  //         ),
  //         textInputAction: TextInputAction.done,
  //       ),
  //     ),
  //   );
  //   children.addIf(
  //     true,
  //     RoundedButton(
  //       buttonText: '儲存名稱',
  //       onPressed: () {
  //         FutureProgress(
  //           future: controller.savePrinterAliasName(),
  //         ).dialog().then(
  //           (value) {
  //             if (value != true) {
  //               final currentName = controller.draft.name;
  //               controller.printerAliasEditingController.text = currentName;
  //             }
  //           },
  //         );
  //       },
  //     ).paddingOnly(bottom: 8),
  //   );
  //   children.addIf(
  //     true,
  //     Padding(
  //       padding: const EdgeInsets.only(
  //         top: 8,
  //         bottom: 12,
  //       ),
  //       child: Center(
  //         child: Container(
  //           width: 300,
  //           //Just for visually debug.
  //           decoration: BoxDecoration(
  //             color: Colors.white,
  //             border: Border.all(width: 0.5),
  //             borderRadius: BorderRadius.circular(30),
  //           ),
  //           child: Screenshot(
  //             controller: controller.previewStickerSreenshotControllers,
  //             child: TagSticker(
  //               tagStyle: TagStyle.Filled,
  //               headerTitleNum: '123',
  //               headerTitleBlockText: '內用',
  //               headerTitleNote: '2F01',
  //               headerSubTitle: '時間: 12:33',
  //               headerSubTitleNote: 8888.currencyStyle,
  //               bodyTitle: '您的商品名稱將會被顯示在這邊',
  //               bodySubTitle: '商品規格1, 商品規格2, 商品規格3, 商品規格4',
  //               // bodyContent: 'bodyContent',
  //             ),
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  //   children.addIf(
  //     true,
  //     RoundedButton(
  //       buttonText: '列印測試工作單',
  //       onPressed: () {
  //         //Print this TagSticker preview.
  //         controller.printPreviewTagSticker();
  //       },
  //     ).paddingOnly(bottom: 8),
  //   );
  //   children.addIf(
  //     true,
  //     RoundedButton(
  //       buttonText: '重新自動校正',
  //       onPressed: controller.autoCalibrate,
  //     ).paddingOnly(bottom: 8),
  //   );
  //   children.addIf(
  //     true,
  //     RoundedButton(
  //       buttonText: '列印訊號字元測試頁面',
  //       onPressed: controller.testPrintPrototype,
  //     ).paddingOnly(bottom: 8),
  //   );
  //   children.addIf(
  //     false,
  //     RoundedButton(
  //       buttonText: '列印背景測試頁面',
  //       onPressed: BlocProvider.of<PrintOrderCubit>(context).printTestSticker,
  //     ).paddingOnly(bottom: 8),
  //   );
  //   return children;
  // }
}

// class _SliverPersistentHeaderDelegate extends SliverPersistentHeaderDelegate {
//   final String titleText;

//   _SliverPersistentHeaderDelegate({
//     @required this.titleText,
//   });

//   @override
//   Widget build(
//       BuildContext context, double shrinkOffset, bool overlapsContent) {
//     return Container(
//       alignment: Alignment.center,
//       child: Text(
//         titleText ?? '',
//         style: Get.textTheme.subtitle1.copyWith(color: kColorPrimary),
//       ),
//     );
//   }

//   @override
//   double get maxExtent => kButtonHeight;

//   @override
//   double get minExtent => kButtonHeight;

//   @override
//   bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) =>
//       false; // 如果内容需要更新，设置为true
// }
