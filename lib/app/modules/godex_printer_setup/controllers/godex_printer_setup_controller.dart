import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:stream_transform/stream_transform.dart';

class GodexPrinterSetupController extends GetxController
    with StateMixin<String> {
  final PrinterProvider printerProvider;
  final _disposable = Completer();

  final data = <SettingLabel>[].obs;

  GodexPrinterSetupController({
    @required this.printerProvider,
  });

  @override
  void onInit() {
    super.onInit();
    data.stream.takeUntil(_disposable.future).listen((value) {
      final status = data.isNotEmpty ? RxStatus.success() : RxStatus.empty();
      change('', status: status);
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  ///
  /// 重新整理
  ///
  Future<void> onRefresh() async {
    try {
      final ls = await printerProvider.getSettingLabels();
      final it = ls.where((element) => !PrinterType.all.contains(element.type));
      final printers = it.distinct((element) => element.uuid);
      data.assignAll(printers);
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  void delete(SettingLabel printer) {
    final index = data.indexWhere((element) => printer.uuid == element.uuid);
    if (index >= 0) {
      data.removeAt(index);
    }
  }

  void addAll(Iterable<SettingLabel> printers) {
    printers ??= <SettingLabel>[];
    final ls = [...data, ...printers];
    final it = ls.distinct((element) => element.uuid);
    data.assignAll(it);
  }

  void reload(SettingLabel printer) {
    final index = data.indexWhere((element) => printer.uuid == element.uuid);
    final newPrinter = printerProvider.getPrinter(printer.uuid);
    if (index >= 0 && newPrinter != null) {
      data[index] = newPrinter;
    }
  }

  Future<bool> submit() {
    // 使用這個會蓋掉全部的設定，包含印表機
    // return printerProvider.putSettingLabelsBatchWithTruncate(data);
    final it = data.where((element) => element.id == null || element.id <= 0);
    final futures = it.map((e) => printerProvider.postSettingLabels(e));
    return Future.wait(futures)
        .then((value) => value.every((element) => element));
  }
}
