import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

import '../controllers/godex_printer_setup_controller.dart';

class GodexPrinterSetupView extends GetView<GodexPrinterSetupController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      actions: _actions(),
      titleText: '標籤機設定',
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: controller.obx(
          (state) {
            return BottomWidgetPage.save(
              onPressed: _submit,
              child: Obx(() => _main()),
            );
          },
          onError: (message) {
            return ListWidget.message(
              message,
              buttonText: '重新整理',
              onPressed: controller.onRefresh,
            );
          },
          onEmpty: ListWidget.blank(
            buttonText: '新增',
            onPressed: _onAddPressed,
          ),
        ),
      ),
    );
  }

  List<Widget> _actions() {
    final children = <Widget>[];
    children.addIf(
      true,
      TextButton(
        onPressed: _onAddPressed,
        child: Text(
          '新增',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
      ),
    );
    return children;
  }

  Future<void> _onAddPressed() async {
    try {
      final ids = controller.data.map((element) => element.uuid);
      final value = await Get.toNamed(
        Routes.PRINTER_PICKER,
        parameters: <String, String>{
          Keys.Data: jsonEncode([...ids]),
          'godex': '${Switcher.On.index}',
        },
      );
      if (value is String) {
        final it = List.from(jsonDecode(value))
            .map((json) => SettingLabel.fromJson(json));
        controller.addAll(it);
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  ///
  /// 儲存
  ///
  Future<void> _submit() async {
    try {
      final value = await FutureProgress(
        future: controller.submit(),
      ).dialog();
      if (true == value) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  void _toDetail(SettingLabel printer) {
    Get.toNamed(
      Routes.GODEX_PRINTER_CATEGORY_SELECTION,
      parameters: {
        Keys.Data: printer.toRawJson(),
      },
    ).then(
      (value) {
        switch (value) {
          case DataAction.Delete:
            controller.delete(printer);
            break;
          case DataAction.Update:
            controller.reload(printer);
            break;
          case DataAction.Create:
            break;
          default:
        }
      },
    );
  }

  Widget _main() {
    final list = controller.data;
    return ListView.separated(
      padding: EdgeInsets.only(
        top: kPadding,
        bottom: kBottomButtonPadding,
      ),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: list.length,
      itemBuilder: (context, index) {
        final element = list.elementAt(index);
        return ListTile(
          tileColor: Colors.white,
          title: Text(element.name ?? ''),
          subtitle: Text(element.ip),
          trailing: Icon(Icons.navigate_next),
          onTap: () => _toDetail(element),
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(height: 8);
      },
    );
  }
}
