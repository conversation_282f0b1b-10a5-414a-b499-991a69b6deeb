import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/circle_button.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/modules/members/views/members_view.dart';
import 'package:muyipork/app/modules/orders/views/orders_view.dart';
import 'package:muyipork/app/modules/orders_setup/controllers/orders_setup_controller.dart';
import 'package:muyipork/app/modules/settings/views/settings_view.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  final _pages = <Widget>[].obs;

  @override
  Widget build(BuildContext context) {
    return controller.obx(
      (state) => Obx(() => _main()),
      onLoading: StandardPage(
        appBar: AppBar(
          title: Text(
            'OKSHOP',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          automaticallyImplyLeading: false,
        ),
        child: Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _main() {
    return Scaffold(
      // body: Navigator(
      //   key: Get.nestedKey(1),
      //   initialRoute: Routes.ORDERS,
      //   onGenerateRoute: controller.onGenerateRoute,
      // ),
      body: _body(),
      resizeToAvoidBottomInset: true,
      floatingActionButton: _circleButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: BottomAppBar(
        color: OKColor.GrayF7,
        shape: const CircularNotchedRectangle(),
        child: Obx(() => _bottomBar()),
      ),
    );
  }

  Iterable<Widget> _buildPages() sync* {
    yield MembersView();
    yield StreamBuilder(
      // TODO: replace with controller.prefProvider.checkoutTypeStream
      stream: controller.prefProvider.userDefault
          .watch(key: Keys.Setting.value)
          .map((event) => controller.prefProvider.brandsType)
          .distinct(),
      builder: (context, snapshot) {
        String title = controller.ordersViewTitle;
        return OrdersView(
          orderStatusList: [
            OrderStatus.Padding,
            OrderStatus.Accepted,
          ],
          defaultTitleText: title,
          tag: '1',
          ordersViewMode: OrdersViewMode.ActiveOrders,
        );
      },
    );
    yield OrdersView(
      orderStatusList: [
        OrderStatus.Completed,
        OrderStatus.CancelByApp,
        OrderStatus.Exception,
        OrderStatus.Rejection,
        OrderStatus.CancelByLine,
      ],
      defaultTitleText:
          controller.prefProvider.storeType.isDinner ? '消費紀錄' : '訂單紀錄',
      tag: '2',
      ordersViewMode: OrdersViewMode.CompletedOrders,
    );
    yield SettingsView();
  }

  Widget _body() {
    if (_pages.isEmpty) {
      _pages.addAll(_buildPages());
    }
    return IndexedStack(
      index: controller.menu.index,
      children: _pages,
    );
  }

  Widget _circleButton() {
    return CircleButton(
      onPressed: () {
        // Read Check out type from setting.
        Get.toNamed(
          Routes.ORDERS_SETUP,
          arguments: OrdersSetupArgs(
            kind: controller.prefProvider.storeType.productKind.index,
          ),
        );
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset('assets/images/icon_order.svg'),
          StreamBuilder<StoreType>(
            initialData: controller.prefProvider.storeType,
            stream: controller.prefProvider.storeTypeStream(),
            builder: (context, snapshot) {
              return Text(
                controller.prefProvider.storeType.actionName,
                style: const TextStyle(
                  fontSize: 15,
                  color: OKColor.GrayF7,
                ),
                textAlign: TextAlign.center,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _bottomBar() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _memberButton(),
        _preOrder(),
        Spacer(),
        _order(),
        _settingButton(),
      ],
    );
  }

  Widget _settingButton() {
    return Expanded(
      child: _Button(
        text: '設定',
        iconData: Icons.settings_outlined,
        checked: controller.menu.isSetting,
        onPressed: () => controller.menu = Menu.Setting,
        checkedColor: controller.prefProvider.themeColor,
      ),
    );
  }

  Widget _order() {
    return Expanded(
      child: StreamBuilder<StoreType>(
        initialData: controller.prefProvider.storeType,
        stream: controller.prefProvider.storeTypeStream(),
        builder: (context, snapshot) {
          final mode = snapshot.data ?? StoreType.Dinner;
          return _Button(
            text: mode.listName,
            iconData: Icons.clear_all,
            checked: controller.menu.isOrder,
            onPressed: () => controller.menu = Menu.Order,
            checkedColor: controller.prefProvider.themeColor,
          );
        },
      ),
    );
  }

  Widget _preOrder() {
    return Expanded(
      child: ValueListenableBuilder(
        valueListenable: controller.prefProvider
            .getListenalbe(keys: [kKeyStoreType, kKeySetting]),
        builder: (context, box, child) {
          return _Button(
            // text: '點餐列表',
            text: controller.ordersViewTitle,
            icon: 'assets/images/icon_order_pre.svg',
            checked: controller.menu.isPreOrder,
            onPressed: () => controller.menu = Menu.PreOrder,
            checkedColor: controller.prefProvider.themeColor,
          );
        },
      ),
    );
  }

  Widget _memberButton() {
    return Expanded(
      child: _Button(
        text: '品牌會員',
        iconData: Icons.people_outline,
        checked: controller.menu.isMember,
        onPressed: () => controller.menu = Menu.Member,
        checkedColor: controller.prefProvider.themeColor,
      ),
    );
  }
}

class _Button extends StatelessWidget {
  final String text;
  final String icon;
  final Function onPressed;
  final bool checked;
  final Color checkedColor;
  final IconData iconData;

  const _Button({
    Key key,
    this.text,
    this.icon,
    this.onPressed,
    this.checked = false,
    this.checkedColor,
    this.iconData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final color = this.checked ? checkedColor : const Color(0xff333333);
    return TextButton(
      onPressed: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _icon(color),
          Text(
            text ?? '',
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _icon(Color color) {
    if (iconData != null) {
      return Icon(iconData, color: color);
    }
    return SvgPicture.asset(icon, color: color);
  }
}

extension _MenuX on Menu {
  bool get isMember => Menu.Member == this;
  bool get isPreOrder => Menu.PreOrder == this;
  bool get isOrder => Menu.Order == this;
  bool get isSetting => Menu.Setting == this;
}
