import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/brands_invoice.dart';
import 'package:muyipork/app/models/brands_invoice_req.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

class InvoiceSettingsController extends GetxController with StateMixin<String> {
  final SettingProvider settingProvider;
  final _data = Rx<BrandsInvoice>(null);
  final _draft = Rx<BrandsInvoiceReq>(null);
  final editingController = TextEditingController();

  PrefProvider get prefProvider => settingProvider.apiProvider.prefProvider;
  BrandsInvoice get data => _data.value;
  BrandsInvoiceReq get draft => _draft.value;

  InvoiceSettingsController({
    @required this.settingProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {}

  Future<num> submit() async {
    final ret = await settingProvider.putBrandsInvoice(_draft.value);
    if (ret is num && ret > 0) {
      // 取得最新發票設定資料
      await settingProvider.getBrandsInvoice();
      return ret;
    }
    return ret;
  }

  Future<void> onRefresh() async {
    try {
      final value = await settingProvider.getBrandsInvoice();
      _data.value = value;
      _draft.value = BrandsInvoiceReq(
        taxType: value.taxType ?? TaxType.TX.index,
        guiException: value.guiException ?? Switcher.Off.index,
        status: value.status ?? Switcher.Off.index,
      );
      editingController.text = _draft.value.displayTaxType;
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  void refreshData() {
    _draft.refresh();
  }
}
