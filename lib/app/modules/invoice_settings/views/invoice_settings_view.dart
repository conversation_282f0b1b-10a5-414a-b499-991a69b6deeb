import 'dart:async';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/dialog_actions.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/message_page.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

import '../controllers/invoice_settings_controller.dart';

class InvoiceSettingsView extends GetView<InvoiceSettingsController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '發票設定',
      child: controller.obx(
        (state) {
          return BottomWidgetPage.save(
            onPressed: _submit,
            child: Obx(() => _main()),
          );
        },
        onError: (message) {
          return MessagePage(
            icon: DialogContentIcon.Alert,
            message: message,
          );
        },
      ),
    );
  }

  Future<void> _submit() async {
    try {
      final ret =
          await FutureProgress(future: controller.submit()).dialog<num>();
      if (ret is num && ret > 0) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      Center(
        child: Text(
          '請填寫發票資訊',
          style: TextStyle(
            fontSize: 16.0,
            color: kColorPrimary,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ).paddingSymmetric(
          vertical: 24.0,
        ),
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用電子發票功能',
        value: controller.draft.statusEnabled,
        onChanged: (value) {
          controller.draft.statusEnabled = value;
          controller.refreshData();
        },
      ),
    );
    children.addIf(
      controller.draft.statusEnabled,
      SettingsWidget.title(
        titleText: '商家資訊',
      ),
    );
    children.addIf(
      controller.draft.statusEnabled,
      SettingsWidget.input(
        readonly: true,
        labelText: '統一編號',
        // initialValue: '12345678',
        initialValue: controller.data.taxId ?? '',
      ),
    );
    children.addIf(
      controller.draft.statusEnabled,
      SettingsWidget.title(
        titleText: '加值中心',
      ),
    );
    children.addIf(
      controller.draft.statusEnabled,
      SettingsWidget.input(
        readonly: true,
        labelText: '請選擇加值中心',
        initialValue: '金財通',
        suffix: Icon(
          Icons.expand_more,
          color: const Color(0xFF333333),
        ),
        onTap: this._showInvoiceProviderPicker,
      ),
    );
    children.addIf(
      controller.draft.statusEnabled,
      SettingsWidget.title(
        titleText: '稅率',
      ),
    );
    children.addIf(
      controller.draft.statusEnabled,
      SettingsWidget.input(
        readonly: true,
        controller: controller.editingController,
        // initialValue: controller.editing.value.displayTaxType,
        labelText: '請選擇稅率',
        onTap: _showTaxTypePicker,
        suffix: Icon(
          Icons.expand_more,
          color: const Color(0xFF333333),
        ),
      ),
    );
    children.addIf(
      controller.draft.statusEnabled,
      SettingsWidget.switcher(
        titleText: '單筆訂單免開發票',
        value: controller.draft.guiEnabled,
        onChanged: (value) {
          controller.draft.guiEnabled = value;
          controller.refreshData();
        },
      ),
    );
    children.addIf(
      controller.draft.statusEnabled,
      SizedBox(height: kBottomButtonPadding),
    );
    return ListView(children: children);
  }

  Future<void> _showInvoiceProviderPicker() async {
    try {
      await DialogActions(
        titleText: '請選擇加值中心',
        actions: [
          '金財通',
        ],
      ).dialog();
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Future<void> _showTaxTypePicker() async {
    try {
      final value = await DialogActions(
        titleText: '請選擇稅率',
        actions: [
          TaxType.TX.name,
          TaxType.Free.name,
          TaxType.Mix.name,
        ],
      ).dialog<num>();
      final selectedTaxType = TaxType.values[value + 1];
      if (TaxType.Mix == selectedTaxType) {
        controller.draft.taxType = selectedTaxType.index;
        controller.editingController.text = selectedTaxType.name;
      }
      if (TaxType.TX == selectedTaxType || TaxType.Free == selectedTaxType) {
        if (Button.Positive == await this._showAlert(selectedTaxType)) {
          controller.draft.taxType = selectedTaxType.index;
          controller.editingController.text = selectedTaxType.name;
        }
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Future<Button> _showAlert(TaxType taxType) {
    final result = Completer<Button>();
    DialogGeneral(DialogArgs(
      contentIcon: DialogContentIcon.Alert,
      mainButtonText: '確認',
      secondaryButtonText: '取消',
      onMainButtonPress: () {
        result.complete(Button.Positive);
      },
      onSecondaryButtonPress: () {
        result.complete(Button.Negative);
      },
      content: Text(
        '儲存後所有商品的稅率將變更為${taxType.name}',
        textAlign: TextAlign.center,
      ),
    )).dialog(
      barrierDismissible: true,
    );
    return result.future;
  }
}
