import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/models/brands_info.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/line_at_settings_controller.dart';

class LineAtSettingsView extends GetView<LineAtSettingsController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: 'LINE@設定',
      child: controller.obx((state) {
        return BottomWidgetPage.save(
          onPressed: _submit,
          child: _main(),
        );
      }),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.add(
      Text(
        '請填寫相關資料', // TODO: i18n
        style: TextStyle(
          fontSize: 16,
          color: OKColor.Primary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ).paddingSymmetric(
        vertical: 12,
      ),
    );
    children.add(Expanded(child: _list()));
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _list() {
    final children = <Widget>[];
    final data = controller.data ?? BrandsInfo();
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          initialValue: data.name,
          labelText: '品牌名稱', // TODO: i18n
          hintText: '請輸入品牌名稱', // TODO: i18n
          onChanged: (value) {
            controller.draft.name = value;
          },
          validator: (value) {
            if (controller.draft.name == null ||
                controller.draft.name.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          initialValue: data.lineName,
          labelText: 'Line@ 名稱', // TODO: i18n
          hintText: '請輸入名稱', // TODO: i18n
          onChanged: (value) {
            controller.draft.lineName = value;
          },
          validator: (value) {
            if (controller.draft.lineName == null ||
                controller.draft.lineName.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          initialValue: data.lineId,
          labelText: 'Line ID', // TODO: i18n
          hintText: '請輸入 Line ID', // TODO: i18n
          onChanged: (value) {
            controller.draft.lineId = value;
          },
          validator: (value) {
            if (controller.draft.lineId == null ||
                controller.draft.lineId.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          initialValue: data.lineChannelId,
          // obscureText: true,
          labelText: 'Channel ID', // TODO: i18n
          hintText: '請輸入 Channel ID', // TODO: i18n
          onChanged: (value) {
            controller.draft.lineChannelId = value;
          },
          validator: (value) {
            if (controller.draft.lineChannelId == null ||
                controller.draft.lineChannelId.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          initialValue: data.lineSecretCode,
          // obscureText: true,
          labelText: 'Secret Code', // TODO: i18n
          hintText: '請輸入 Secret Code', // TODO: i18n
          onChanged: (value) {
            controller.draft.lineSecretCode = value;
          },
          validator: (value) {
            if (controller.draft.lineSecretCode == null ||
                controller.draft.lineSecretCode.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          initialValue: data.lineAccessToken,
          labelText: 'Access Token', // TODO: i18n
          hintText: '請輸入 Access Token', // TODO: i18n
          onChanged: (value) {
            controller.draft.lineAccessToken = value;
          },
          validator: (value) {
            if (controller.draft.lineAccessToken == null ||
                controller.draft.lineAccessToken.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(
      ListTile(
        contentPadding: kContentPadding,
        tileColor: Colors.white,
        title: CustomEditor(
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          initialValue: data.lineLiffId,
          labelText: 'Liff ID', // TODO: i18n
          hintText: '請輸入 Liff ID', // TODO: i18n
          onChanged: (value) {
            controller.draft.lineLiffId = value;
          },
          validator: (value) {
            if (controller.draft.lineLiffId == null ||
                controller.draft.lineLiffId.isEmpty) {
              return '必填項目';
            }
            return null;
          },
        ),
      ),
    );
    children.add(SettingsWidget.link(
      titleText: '線上下單網址',
      subtitleText: data.lineBaseUrl,
    ));
    children.addIf(
      data.type.brandsType.containsRetail,
      SettingsWidget.link(
        titleText: '零售商店網址',
        subtitleText: data.okshopShopUrl,
      ),
    );
    children.addIf(
      data.type.brandsType.containsDinner,
      SettingsWidget.link(
        titleText: '餐飲商店網址',
        subtitleText: data.okshopDinerUrl,
      ),
    );
    return ListView(
      physics: AlwaysScrollableScrollPhysics(),
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: children,
    );
  }

  Future<void> _submit() async {
    try {
      final value = await FutureProgress(
        future: controller.submit(),
      ).dialog<bool>();
      if (true == value) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }
}
