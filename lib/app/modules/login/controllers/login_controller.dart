import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:muyipork/app/models/login_req.dart';
import 'package:muyipork/app/models/login_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:package_info/package_info.dart';

class LoginController extends GetxController {
  final ApiProvider apiProvider;
  final PackageInfo packageInfo;
  final obscureText = true.obs;
  final _draft = LoginReq().obs;
  LoginReq get draft => _draft.value;

  PrefProvider get prefProvider => apiProvider.prefProvider;
  Box get userDefault => prefProvider.userDefault;
  bool get rememberMe => userDefault.get(kKeyRememberMe, defaultValue: false);

  LoginController({
    @required this.apiProvider,
    @required this.packageInfo,
  });

  @override
  void onInit() {
    super.onInit();
    if (true == rememberMe) {
      draft.code = userDefault.get(kKeyCode, defaultValue: '');
      draft.username = userDefault.get(kKeyUsername, defaultValue: '');
    } else {
      draft.code = '';
      draft.username = '';
    }
    draft.password = '';
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {}

  Future<LoginRes> submit() {
    return apiProvider.login(draft);
  }

  void resetRememberMe() {
    userDefault.put(kKeyCode, '');
    userDefault.put(kKeyUsername, '');
  }

  void onLogin() {
    _saveRememberMe();
    Get.offAllNamed(Routes.SPLASH);
  }

  void _saveRememberMe() {
    if (rememberMe) {
      userDefault.put(kKeyCode, draft.code);
      userDefault.put(kKeyUsername, draft.username);
    }
  }
}
