import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/keys.dart';
import 'package:muyipork/extension.dart';

class _Filter {
  StoreType type;
  OrderSource source;
  num id;

  _Filter({
    this.type,
    this.source,
    this.id,
  });
}

class MemberCouponsController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final _disposable = Completer();
  final CouponProvider couponProvider;
  // title
  final _title = ''.obs;
  String get title {
    if (_title.value != null && _title.value.isNotEmpty) {
      return _title.value;
    }
    return '優惠券';
  }

  // fileter
  final _filter = _Filter().obs;
  // id
  num get id => _filter.value.id;
  // pagi
  final _pagi = Pagination().obs;
  num get more => _pagi.value.needToLoadMore ? 1 : 0;
  // data
  final _data = <Coupon>[].obs;
  Iterable<Coupon> get data {
    return _data.where((element) {
      // HACK:
      // return true;
      // 餐飲/零售
      if (element.storeType != _filter.value.type) {
        return false;
      }
      // 門市/線上
      if (element.orderSource != _filter.value.source) {
        return false;
      }
      // 可用
      if (element.status == null || element.status.switcher.isOn == false) {
        return false;
      }
      if (element.isExpired) {
        return false;
      }
      return true;
    });
  }

  // stream
  // Stream<Iterable<Coupon>> get stream {
  //   return _data.stream.map((event) {
  //     return event.where((element) {
  //       // HACK:
  //       // return true;
  //       // 餐飲/零售
  //       if (element.storeType != _filter.value.type) {
  //         return false;
  //       }
  //       // 門市/線上
  //       if (element.orderSource != _filter.value.source) {
  //         return false;
  //       }
  //       // 啟用
  //       if (element.status != null && element.status.switcher.isOff) {
  //         return false;
  //       }
  //       return true;
  //     });
  //   });
  // }

  MemberCouponsController({
    @required this.couponProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.Title)) {
      _title.value = Get.parameters[Keys.Title];
    }
    _filter.stream
        .tap((event) => change('', status: RxStatus.loading()))
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey('source')) {
      final source = num.tryParse(Get.parameters['source']);
      _filter.value.source = source.orderSource;
    }
    if (Get.parameters.containsKey('type')) {
      final type = num.tryParse(Get.parameters['type']);
      _filter.value.type = type.storeType;
    }
    if (Get.parameters.containsKey(Keys.Id)) {
      _filter.value.id = num.tryParse(Get.parameters[Keys.Id]) ?? 0;
    }
    _filter.refresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    await couponProvider.getMemberCoupons(id, 1).then(
      (value) {
        _pagi.value.currentPage = 1;
        _pagi.value.lastPage = null;
        _data.clear();
        _data.addAll(value);
        _refreshPage();
      },
      onError: (error) {
        change('', status: RxStatus.error('$error'));
      },
    );
  }

  @override
  Future<void> onEndScroll() async {
    if (_pagi.value.needToLoadMore) {
      await couponProvider.getMemberCoupons(id, _pagi.value.nextPage).then(
        (value) {
          _pagi.value.currentPage = _pagi.value.nextPage;
          _data.addAll(value);
        },
        onError: (error) {
          _pagi.value.lastPage = _pagi.value.currentPage;
          _data.refresh();
        },
      ).whenComplete(() => _refreshPage());
    }
  }

  @override
  Future<void> onTopScroll() async {
    // nothing...
  }

  void _refreshPage() {
    // 特殊: cache 有東西，設定成功，觸發 onEndScroll 繼續讀取
    final isEmpty = _pagi.value.needToLoadMore
        ? couponProvider.getMemberCached(id).isEmpty
        : data.isEmpty;
    change('', status: isEmpty ? RxStatus.empty() : RxStatus.success());
  }
}
