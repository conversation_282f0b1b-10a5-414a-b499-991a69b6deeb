import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';

enum PointAction {
  Add,
  Remove,
  AddInfo,
  RemoveInfo,
}

class PointDialog extends StatelessWidget {
  final num point;
  final num delta;
  final PointAction action;
  final ValueChanged<num> onValueChanged;
  final _draft = 0.obs;

  PointDialog({
    Key key,
    this.point,
    this.delta,
    this.action,
    this.onValueChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DialogGeneral(
      DialogArgs(
        header: _header(),
        content: _content(),
        mainButtonText: action.buttonText,
        secondaryButtonText: '取消',
        contentPadding: const EdgeInsets.only(top: 12),
        onMainButtonPress: () {
          onValueChanged?.call(_draft.value);
        },
        onSecondaryButtonPress: () {
          onValueChanged?.call(0);
        },
      ),
    );
  }

  Widget _header() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 20,
          color: action.textColor,
        ),
        children: [
          TextSpan(
            // text: '扣除',
            text: action.name,
            style: const TextStyle(
              fontWeight: FontWeight.w700,
            ),
          ),
          const TextSpan(
            text: '會員積點',
            style: TextStyle(
              color: OKColor.Gray33,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _content() {
    switch (action) {
      case PointAction.Add:
      case PointAction.Remove:
        return _actionContent();
      case PointAction.AddInfo:
      case PointAction.RemoveInfo:
        return _infoContent();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _actionContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _actionContentChildren().toList(growable: false),
    );
  }

  Iterable<Widget> _actionContentChildren() sync* {
    yield Text(
      '${point ?? 0}',
      style: const TextStyle(
        fontSize: 46,
        color: OKColor.Primary,
      ),
      textAlign: TextAlign.center,
    );
    yield const Text(
      '會員積點',
      style: TextStyle(
        fontSize: 14,
        color: OKColor.Primary,
      ),
      textAlign: TextAlign.center,
    );
    yield const SizedBox(height: kPadding);
    yield Container(
      padding: const EdgeInsets.symmetric(
        vertical: kPadding,
        horizontal: 28,
      ),
      decoration: BoxDecoration(
        color: action.backgroundColor,
        boxShadow: const [
          BoxShadow(
            color: Color(0x4d000000),
            offset: Offset(0, 0),
            blurRadius: 6,
          ),
        ],
      ),
      child: CustomEditor.number(
        textAlign: TextAlign.center,
        decoration: InputDecoration(
          hintText: action.description,
          hintStyle: const TextStyle(
            fontSize: 16,
            color: OKColor.Gray66,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 8.0,
          ),
          filled: true,
          fillColor: Colors.white,
          border: const OutlineInputBorder(
            borderRadius: kBorderRadius10,
            borderSide: BorderSide(
              width: 1.0,
              color: OKColor.GrayDD,
            ),
          ),
        ),
        onChanged: (value) {
          _draft.value = action.toDelta(num.tryParse(value));
        },
      ),
    );
  }

  Iterable<Widget> _rowChildren() sync* {
    yield Expanded(
      child: Text(
        // '988',
        '${point ?? 0}',
        style: const TextStyle(
          fontSize: 32,
          color: OKColor.Primary,
        ),
        textAlign: TextAlign.right,
      ),
    );
    yield const SizedBox(width: kPadding);
    yield const Icon(Icons.arrow_forward);
    yield const SizedBox(width: kPadding);
    yield Expanded(
      child: Text(
        // '960',
        '${(point ?? 0) + (delta ?? 0)}',
        style: TextStyle(
          fontSize: 32,
          color: action.textColor,
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  Iterable<Widget> _infoContentChildren() sync* {
    _draft.value = delta;
    yield const SizedBox(height: kPadding);
    yield Text(
      '${action.name}${delta.abs() ?? 0}點',
      style: TextStyle(
        fontSize: 20,
        color: action.textColor,
      ),
      textAlign: TextAlign.center,
    );
    yield const SizedBox(height: 12);
    yield Row(
      mainAxisSize: MainAxisSize.min,
      children: _rowChildren().toList(growable: false),
    );
    yield const SizedBox(height: kPadding);
    yield Text(
      '按下確認後，系統將${action.name}會員積點。',
      style: const TextStyle(
        fontSize: 14,
        color: OKColor.Gray66,
      ),
      textAlign: TextAlign.center,
    );
    yield const SizedBox(height: 30);
  }

  Widget _infoContent() {
    _draft.value = delta;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _infoContentChildren().toList(growable: false),
    );
  }
}

extension _PointActionX on PointAction {
  String get description {
    switch (this) {
      case PointAction.Add:
        return '請輸入增加點數';
      case PointAction.AddInfo:
        return '按下確認後，系統將增加會員積點。';
      case PointAction.Remove:
        return '請輸入扣除點數';
      case PointAction.RemoveInfo:
        return '按下確認後，系統將扣除會員積點。';
      default:
        return '';
    }
  }

  Color get backgroundColor {
    switch (this) {
      case PointAction.Add:
        return const Color(0xffc9f4ef);
      case PointAction.Remove:
        return const Color(0xfff8e4df);
      case PointAction.AddInfo:
      case PointAction.RemoveInfo:
      default:
        return Colors.transparent;
    }
  }

  Color get textColor {
    switch (this) {
      case PointAction.Add:
      case PointAction.AddInfo:
        return const Color(0xff44beb0);
      case PointAction.Remove:
      case PointAction.RemoveInfo:
        return OKColor.Error;
      default:
        return Colors.transparent;
    }
  }

  String get buttonText {
    switch (this) {
      case PointAction.Add:
        return '確認增加';
      case PointAction.Remove:
        return '確認扣除';
      case PointAction.AddInfo:
      case PointAction.RemoveInfo:
        return '確認';
      default:
        return '';
    }
  }

  String get name {
    switch (this) {
      case PointAction.Add:
      case PointAction.AddInfo:
        return '增加';
      case PointAction.Remove:
      case PointAction.RemoveInfo:
        return '扣除';
      default:
        return '';
    }
  }

  num toDelta(num value) {
    switch (this) {
      case PointAction.Add:
        return value;
      case PointAction.Remove:
        return -value;
      case PointAction.AddInfo:
      case PointAction.RemoveInfo:
      default:
        return 0;
    }
  }
}
