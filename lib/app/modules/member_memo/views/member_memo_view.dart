import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/album_editor.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_tab_bar.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/models/image_model.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';
import '../controllers/member_memo_controller.dart';

class MemberMemoView extends GetView<MemberMemoController> {
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.memoModified || controller.albumModified) {
          final completer = Completer<bool>();
          DialogGeneral.quest(
            '是否需要「儲存」或是離開？',
            mainButtonText: '儲存',
            secondaryButtonText: '離開',
            onMainButtonPressed: () {
              _submitAll().then(
                (value) {
                  completer.complete(true);
                },
                onError: (error) {
                  completer.completeError(error);
                },
              );
            },
            onSecondaryButtonPress: () {
              completer.complete(true);
            },
          ).dialog();
          return completer.future;
        }
        return true;
      },
      child: StandardPage(
        titleText: '店家備註',
        child: controller.obx((state) => _main()),
      ),
    );
  }

  Future _submitAll() {
    return FutureProgress(future: controller.submitAll()).dialog();
  }

  Widget _main() {
    return DefaultTabController(
      length: controller.tabs.length,
      child: Column(
        children: [
          _header(),
          Expanded(
            child: TabBarView(
              children: _pages(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _header() {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: OKColor.Tab,
        boxShadow: [
          BoxShadow(
            color: OKColor.Shadow,
            offset: Offset(0, 0),
            blurRadius: 6,
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 4.0,
            ),
            child: _tabBar(),
          ),
          SizedBox(
            height: 52,
            child: VerticalDivider(
              indent: 12,
              endIndent: 12,
              color: const Color(0xFF7A92A5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _tabBar() {
    return CustomTabBar(
      tabs: controller.tabs.map((e) {
        return Text(e).paddingSymmetric(horizontal: 8);
      }).toList(),
      onTap: controller.index,
    );
  }

  List<Widget> _pages() {
    final ls = <Widget>[];
    ls.addIf(
      true,
      _MemoPage(
        initialValue: controller.member.memo,
        onValueChanged: (value) {
          controller.member.memo = value;
          controller.memoModified = true;
        },
        onSavePressed: _submitMemo,
      ),
    );
    ls.addIf(
      true,
      _AlbumPage(
        tiles: controller.tiles,
        imagesToRemove: controller.imagesToRemove,
        editing: controller.editing,
        onSavePressed: () {
          logger.d('${controller.tiles.length}');
          FutureProgress(
            future: controller.submitImage(),
          ).dialog();
        },
      ),
    );
    return ls;
  }

  void _submitMemo() {
    FutureProgress(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (true == value) {
          controller.saveToLocalStorage();
        }
      },
    );
  }
}

class _MemoPage extends StatelessWidget {
  final ValueChanged<String> onValueChanged;
  final String initialValue;
  final Function onSavePressed;

  const _MemoPage({
    Key key,
    this.onSavePressed,
    this.onValueChanged,
    this.initialValue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox.expand(
      child: BottomWidgetPage.save(
        onPressed: onSavePressed,
        child: Container(
          margin: EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 12,
          ),
          color: Colors.white,
          alignment: Alignment.topLeft,
          child: TextFormField(
            initialValue: initialValue ?? '',
            onChanged: onValueChanged,
            keyboardType: TextInputType.multiline,
            maxLines: null,
            decoration: InputDecoration(
              hintText: '請為此顧客輸入備註，僅店家能觀看及編輯…',
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              // filled: true,
              // fillColor: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}

class _AlbumPage extends GetView<MemberMemoController> {
  final RxList<ImageModel> tiles;
  final RxList<ImageModel> imagesToRemove;
  final RxBool editing;
  final Function onSavePressed;

  _AlbumPage({
    Key key,
    @required this.tiles,
    @required this.imagesToRemove,
    @required this.editing,
    this.onSavePressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BottomWidgetPage.save(
      onPressed: onSavePressed,
      child: Obx(() => _main()),
    );
  }

  Widget _main() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _header(),
        Expanded(
          child: AlbumEditor(
            tiles: tiles,
            imagesToRemove: imagesToRemove,
            editing: editing.value,
            onPressed: (value) {
              Get.toNamed(
                Routes.ALBUM,
                parameters: {
                  Keys.Data: jsonEncode(tiles),
                  'initialPage': '${tiles.indexOf(value)}',
                },
              );
            },
          ).paddingSymmetric(
            horizontal: kPadding,
          ),
        ),
      ],
    );
  }

  Widget _header() {
    return Row(
      children: [
        SizedBox(
          width: kPadding,
        ),
        Expanded(
          child: Text(
            '顧客照片：',
            style: TextStyle(
              fontSize: 14,
              color: OKColor.Gray22,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        _edit(),
        SizedBox(
          width: kPadding,
        ),
      ],
    );
  }

  Widget _edit() {
    return TextButton.icon(
      onPressed: editing.toggle,
      icon: Icon(
        editing.value ? Icons.check : Icons.edit,
        color: OKColor.Primary,
      ),
      label: Text(
        editing.value ? '完成' : '編輯',
        style: TextStyle(
          fontSize: 14,
          color: OKColor.Primary,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
