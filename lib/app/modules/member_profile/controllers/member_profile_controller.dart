import 'dart:async';

import 'package:get/get.dart';
import 'package:muyipork/app/models/city.dart';
import 'package:muyipork/app/models/district.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/keys.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/member_provider.dart';

class MemberProfileController extends GetxController with StateMixin<String> {
  final MemberProvider memberProvider;
  final _disposable = Completer();
  final _id = 0.obs;
  final _data = Rx<MemberProfile>(null);
  final _nickname = ''.obs;
  final _needUpdate = false.obs;

  PrefProvider get prefProvider => memberProvider.apiProvider.prefProvider;
  MemberProfile get data => _data.value;
  set nickname(String value) => _nickname.value = value;

  set id(num value) => _id.value = value;
  num get id => _id.value;

  MemberProfileController({
    this.memberProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _data.stream.takeUntil(_disposable.future).listen(
      (event) {
        _nickname.value = event?.nicknameStore ?? '';
      },
    );
    _id.stream
        .distinct()
        .where((event) {
          if (memberProvider.profiles.containsKey(id)) {
            // 使用 cache
            logger.d('[MemberProfileController] use cache: id($id)');
            _data.value = memberProvider.profiles[id];
            change('', status: RxStatus.success());
            return false;
          }
          return true;
        })
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.Id)) {
      _id.value = num.tryParse(Get.parameters[Keys.Id]) ?? 0;
    }
  }

  @override
  void onClose() {
    _disposable.complete();
    if (true == _needUpdate.value) {
      memberProvider.getMemberProfile(id);
    }
  }

  Future<void> onRefresh() async {
    try {
      logger.d('[MemberProfileController] onRefresh: id($id)');
      _data.value = await memberProvider.getMemberProfile(id ?? 0);
      change('', status: RxStatus.success());
    } catch (e) {
      // 重新整理可顯示錯誤頁面
      change('', status: RxStatus.error('$e'));
      // 不需要: 這裡是終點，做錯誤呈現處理
      // rethrow;
    }
  }

  String get displayAddress {
    return '$_displayCity$_displayDist$_displayAddress';
  }

  String get _displayCity {
    if (data != null) {
      final city = prefProvider.cities.firstWhere(
        (element) => element.id == data?.addresses?.cityId,
        orElse: () => City(),
      );
      return city?.name ?? '';
    }
    return '';
  }

  String get _displayDist {
    if (data != null) {
      final dist = prefProvider.districts.firstWhere(
        (element) => element.id == data?.addresses?.cityareaId,
        orElse: () => District(),
      );
      return dist?.name ?? '';
    }
    return '';
  }

  String get _displayAddress => data?.addresses?.address ?? '';

  Future<bool> submit() async {
    final ret =
        await memberProvider.putMemberNicknameStore(id, _nickname.value);
    if (ret == null || ret <= 0) {
      return false;
    }
    _needUpdate.value = true;
    return true;
  }
}
