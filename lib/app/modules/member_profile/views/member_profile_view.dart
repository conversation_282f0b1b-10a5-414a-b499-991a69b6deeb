import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/member_avatar.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/underline_divider.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/member_profile_controller.dart';

class MemberProfileView extends GetView<MemberProfileController> {
  const MemberProfileView({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '基本資料',
      child: ColoredBox(
        color: Colors.white,
        child: RefreshIndicator(
          onRefresh: controller.onRefresh,
          child: controller.obx(
            (state) => BottomWidgetPage.save(
              child: Obx(() => _body()),
              onPressed: _submit,
            ),
            onError: ListWidget.message,
          ),
        ),
      ),
    );
  }

  // 儲存
  Future<void> _submit() async {
    final res = await FutureProgress(
      future: controller.submit(),
    ).dialog();
    if (res == true) {
      Get.back();
    }
  }

  Widget _body() {
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      physics: AlwaysScrollableScrollPhysics(),
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 24);
    yield MemberAvatar(
      imageUrl: controller.data?.avatar,
      size: 120.0,
    );
    yield const SizedBox(height: 24);
    yield SettingsWidget.input(
      labelText: '店家自訂名稱',
      hintText: '請輸入店家自訂名稱',
      initialValue: controller.data.nicknameStore,
      onChanged: (value) => controller.nickname = value,
    );
    yield const SizedBox(height: 8);
    yield _readonly();
  }

  Widget _readonly() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(height: 8);
      yield const Text(
        '以下為顧客自行填寫之資料，僅開放檢視。',
        style: TextStyle(
          fontSize: 16,
          color: OKColor.Primary,
        ),
        textAlign: TextAlign.center,
      );
      yield const SizedBox(height: 8);
      yield _ListTile(
        titleText: '姓名',
        valueText: controller.data?.name,
      );
      yield const SizedBox(
        width: double.infinity,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: kPadding),
          child: Text(
            '*此為最近一次使用之姓名',
            style: TextStyle(
              fontSize: 12,
              color: OKColor.Primary,
            ),
            textAlign: TextAlign.left,
          ),
        ),
      );
      yield _ListTile(
        titleText: '生日',
        valueText: controller.data?.birthday?.localAt?.yMd,
      );
      yield _ListTile(
        titleText: '性別',
        valueText: controller.data?.gender?.genderType?.name,
      );
      yield _ListTile(
        titleText: '電子信箱',
        valueText: controller.data?.email,
      );
      yield _ListTile(
        titleText: '手機門號',
        valueText: controller.data?.mobilePhone,
      );
      yield _ListTile(
        titleText: '收件人地址',
        valueText: controller.displayAddress,
      );
      yield _ListTile(
        titleText: '公司統編',
        valueText: controller.data?.vatNumber,
      );
      yield _ListTile(
        titleText: '手機條碼載具',
        valueText: controller.data?.invoiceCarrier,
      );
      yield _ListTile(
        titleText: '愛心碼',
        valueText: controller.data?.npoBan,
      );
      yield const SizedBox(height: 8);
    }

    return ColoredBox(
      color: const Color(0xffeeeef3),
      child: Column(
        // crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }
}

class _ListTile extends StatelessWidget {
  final String titleText;
  final String valueText;

  const _ListTile({
    Key key,
    this.titleText,
    this.valueText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: kButtonHeight,
      child: UnderlineDivider(
        backgroundColor: Colors.transparent,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(width: kPadding);
    yield Text(
      // '姓名',
      titleText ?? '',
      style: const TextStyle(
        fontSize: 16,
        color: Colors.black,
      ),
      textAlign: TextAlign.left,
    );
    yield const SizedBox(width: 36);
    yield Expanded(
      child: Text(
        // '李修坊',
        valueText != null && valueText.isNotEmpty ? valueText : '尚未填寫',
        style: const TextStyle(
          fontSize: 16,
          color: Color(0xff929ca8),
        ),
        maxLines: 2,
        softWrap: false,
        overflow: TextOverflow.ellipsis,
        textAlign: TextAlign.right,
      ),
    );
    yield const SizedBox(width: kPadding);
  }
}
