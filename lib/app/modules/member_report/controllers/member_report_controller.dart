import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/keys.dart';

class MemberReportController extends GetxController with StateMixin<String> {
  final MemberProvider memberProvider;
  ApiProvider get apiProvider => memberProvider.apiProvider;
  final _id = 0.obs;
  final _disposable = Completer();

  MemberReportController({
    @required this.memberProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .distinct()
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.Id)) {
      _id.value = num.tryParse(Get.parameters[Keys.Id]);
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    await apiProvider.getMemberReports(_id.value).then(
      (value) {
        change('', status: RxStatus.success());
      },
      onError: (error) {
        change('', status: RxStatus.error('$error'));
      },
    );
  }
}
