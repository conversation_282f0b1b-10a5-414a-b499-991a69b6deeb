import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/models/member_req.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

class MembersController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final OrderProvider orderProvider;
  final _filter = MemberReq(limit: kLimit).obs;
  final _disposable = Completer();
  final _cached = <Member>[].obs;
  final devtool = false.obs;
  // flat
  MemberProvider get memberProvider => orderProvider.memberProvider;
  ApiProvider get apiProvider => memberProvider.apiProvider;
  BoxProvider get boxProvider => memberProvider.boxProvider;
  MemberReq get filter => _filter.value;
  Iterable<Member> get data => _cached;

  MembersController({
    @required this.orderProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 監聽 mem，有變動時，同步到 cache
    boxProvider
        .getGsBox(kBoxMember)
        .watch()
        .debounce(300.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      // logger.d('[MembersController] mem changed, key($event)');
      final box = boxProvider.getGsBox(kBoxMember);
      final it = List.from(box.getValues(), growable: false)
          .map((e) => Member.fromJson(e));
      final list = List<Member>.from(it, growable: false);
      logger.d('[MembersController] mem changed, length(${list.length})');
      // sort by id
      list.sort((a, b) => (b.id ?? 0).compareTo(a.id ?? 0));
      _cached.clear();
      _cached.addAll(list);
      _refreshPage();
    });
    // 監聽過濾器，變更時，重新查詢並顯示讀取中
    _filter.stream
        .debounce(700.milliseconds)
        .asBroadcastStream()
        .tap((event) => change('', status: RxStatus.loading()))
        .asyncMapSample((event) async {
          await onRefresh();
          final box = boxProvider.getGsBox(kBoxMember);
          await for (var member in memberProvider.getMemberStream(event)) {
            box.write('${member.id}', member.toJson());
          }
        })
        .takeUntil(_disposable.future)
        .listen((event) {
          logger.d('[MembersController] 過濾器變更完成');
        });
  }

  @override
  void onReady() {
    super.onReady();
    refreshFilter();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    logger.d('[MembersController] onRefresh');
    try {
      // 重新整理: 一定是第一頁
      await _getMemberList(1);
      await 500.milliseconds.delay();
      _refreshPage();
    } catch (e) {
      logger.e('[MembersController] onRefresh', e);
      change('', status: RxStatus.error(e.toString()));
    } finally {
      logger.d('[MembersController] onRefresh done');
    }
  }

  @override
  Future<void> onEndScroll() async {
    logger.d('[MembersController] onEndScroll');
    if (filter.hasMore == true) {
      logger.d('[MembersController] onEndScroll: has more data');
      try {
        await _getMemberList(filter.nextPage);
      } catch (e) {
        logger.e('[MembersController] onEndScroll', e);
        // 續載失敗不用顯示錯誤
      } finally {
        logger.d('[MembersController] onEndScroll: finally');
      }
    } else {
      logger.d('[MembersController] onEndScroll: no more data');
    }
  }

  Future<void> _getMemberList(num page) async {
    logger.d('[MembersController] getting page($page)');
    filter.page = page;
    filter.hasMore = false;
    final it = await memberProvider.getMemberList(filter);
    // 判斷最後一頁
    filter.hasMore = (it?.length ?? 0) >= filter.nnLimit;
    // save to mem
    final box = boxProvider.getGsBox(kBoxMember);
    if (page == 1) {
      logger.d('[MembersController] clear mem');
      box.erase();
    }
    for (var member in it) {
      box.write('${member.id}', member.toJson());
    }
    logger.d('[MembersController] save to mem, length(${it.length})');
  }

  @override
  Future<void> onTopScroll() async {
    logger.d('[MembersController] onTopScroll');
  }

  void refreshFilter() {
    logger.d('[MembersController] refreshFilter');
    _filter.refresh();
  }

  void _refreshPage() {
    logger.d('[MembersController] _refreshPage count(${data.length})');
    change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
  }
}
