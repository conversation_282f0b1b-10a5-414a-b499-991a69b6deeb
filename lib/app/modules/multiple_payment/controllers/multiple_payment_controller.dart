import 'dart:async';
import 'dart:math';

import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/models/multiple_payment.dart';
import 'package:muyipork/app/models/setting_pay.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/extension.dart';

class MultiplePaymentController extends GetxController {
  final PrefProvider prefProvider;
  final _disposable = Completer();
  final list = <Pay>[].obs;
  // 購買總額
  final _total = RxNum(0);
  // 支付總額
  final _paid = RxNum(0);
  final Iterable<MultiplePayment> multiplePayment;

  num get total => _total.value;
  set total(num value) => _total.value = value;

  // 剩餘金額
  num get remain => max(0, total - _paid.value);

  num get change => _paid.value - total;

  bool get needShowHint => change != 0;

  ///
  /// 提示文字
  ///
  String get hintText {
    if (change > 0) {
      // return '須找零：330';
      return '須找零：${change.decimalStyle}';
    }
    if (change < 0) {
      // return '總額不可低於1670';
      return '總額不可低於${total.decimalStyle}';
    }
    return '';
  }

  MultiplePaymentController({
    this.prefProvider,
    this.multiplePayment,
  });

  void _initObservable() {
    list.stream.takeUntil(_disposable.future).listen(
      (event) {
        _paid.value = list.fold<num>(0, (previousValue, element) {
          return previousValue + element.multiplePayment.money;
        });
      },
    );
  }

  @override
  void onInit() {
    super.onInit();
    _initObservable();
    // 過濾支付列表及排序
    final ls = prefProvider.settingPay.toList();
    ls.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    final itOn =
        ls.where((element) => element.status.switcher.isOn).map((element) {
      return Pay(
        settingPay: element,
        multiplePayment: multiplePayment.firstWhere(
          (e) => e.paymentMethodId == element.id,
          orElse: () => MultiplePayment(
            paymentMethodId: element.id,
            money: 0,
          ),
        ),
        // multiplePayment: MultiplePayment(
        //   paymentMethodId: element.id,
        //   money: 0,
        // ),
      );
    });
    list.addAll(itOn);
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {}

  ///
  /// 自訂更新方式
  ///
  void customRefresh() {
    final index = list.indexWhere(
        (element) => element.settingPay.payMethodId.appPayMethod.isCash);
    if (index >= 0) {
      final data = list.elementAt(index);
      num paid = list.fold<num>(0, (previousValue, element) {
        return previousValue + (element.multiplePayment.money ?? 0);
      });
      // 排除現金
      paid -= data.multiplePayment.money ?? 0;
      data.multiplePayment.money = max(0, total - paid);
    }
  }
}

class Pay {
  // 支付金額
  final MultiplePayment multiplePayment;
  // 支付方式
  final SettingPay settingPay;

  Pay({
    this.multiplePayment,
    this.settingPay,
  });
}
