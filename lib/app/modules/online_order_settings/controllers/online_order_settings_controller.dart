import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/setting_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';

class OnlineOrderSettingsController extends GetxController {
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => this.apiProvider.prefProvider;
  SettingGetRes get settings => this.prefProvider.setting;

  final autoAbandon = false.obs;
  final autoAccept = false.obs;
  final autoClose = false.obs;
  final autoCloseMin = ''.obs;
  final autoPrintReceipt = false.obs;
  final autoPrintTag = false.obs;
  final autoAbandonMin = ''.obs;

  OnlineOrderSettingsController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    //
  }

  Future<void> save() async {
    final auto = this.settings.data.other.auto;
    // 棄單
    auto.abandon = this.autoAbandon.value ? 1 : 0;
    auto.abandonMin = int.tryParse(this.autoAbandonMin.value) ?? 0;
    // 接單
    auto.order = this.autoAccept.value ? 1 : 0;
    auto.orderPrint = this.autoPrintTag.value ? 1 : 0;
    // auto.orderPrint = this.autoPrintReceipt.value ? 1 : 0;
    // 關門
    auto.close = this.autoClose.value ? 1 : 0;
    auto.closeMin = int.tryParse(this.autoCloseMin.value) ?? 0;
  }
}
