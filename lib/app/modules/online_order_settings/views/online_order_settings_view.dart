import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bg_default.dart';
import 'package:muyipork/app/components/bottom_button.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/general_appbar.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/online_order_settings_controller.dart';

class OnlineOrderSettingsView extends GetView<OnlineOrderSettingsController> {
  @override
  Widget build(BuildContext context) {
    return BgDefault(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: GeneralAppbar(
          title: Text(
            '接單設定',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
        ),
        body: DecoratedBox(
          decoration: const BoxDecoration(
            borderRadius: const BorderRadius.vertical(
              top: kRadiusCircular,
            ),
            color: const Color(0xffeeeef3),
          ),
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    width: double.infinity,
                  ),
                  Text(
                    '自動棄單、自動接單功能請2擇1',
                    style: const TextStyle(
                      fontSize: 16,
                      color: const Color(0xfff89321),
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ).paddingSymmetric(
                    vertical: 16.0,
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: _Pages(),
                    ),
                  ),
                ],
              ),
              BottomButton(
                buttonText: '儲存', // TODO: i18n
                onPressed: () {
                  controller.save();
                  Get.back();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _Pages extends GetView<OnlineOrderSettingsController> {
  const _Pages({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _SubPage1(),
        _SubPage2(),
        _SubPage3(),
        const SizedBox(
          height: kBottomButtonPadding,
        ),
      ],
    );
  }
}

class _SubPage1 extends GetView<OnlineOrderSettingsController> {
  const _SubPage1({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SettingsWidget.title(
          titleText: '自動棄單設定',
        ),
        Obx(() {
          return SwitchListTile(
            tileColor: Colors.white,
            title: Text(
              '啟用',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
              textAlign: TextAlign.left,
            ),
            value: controller.autoAbandon.value,
            onChanged: (value) {
              controller.autoAbandon.value = value;
            },
          );
        }),
        Obx(() {
          return Visibility(
            visible: controller.autoAbandon.value,
            child: ListTile(
              contentPadding: const EdgeInsets.only(
                left: 28.0,
                right: kPadding,
              ),
              tileColor: Colors.white,
              title: CustomEditor(
                initialValue: '${controller.settings?.closeMin ?? 0}',
                labelText: '自動棄單時間(分鐘)',
                hintText: '請輸入數字',
                onChanged: (value) {
                  //
                },
              ),
            ),
          );
        }),
      ],
    );
  }
}

class _SubPage2 extends GetView<OnlineOrderSettingsController> {
  const _SubPage2({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        SettingsWidget.title(
          titleText: '自動接單設定',
        ),
        Obx(() {
          return SwitchListTile(
            contentPadding: kContentPadding,
            tileColor: Colors.white,
            title: Text(
              '啟用',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
              textAlign: TextAlign.left,
            ),
            value: controller.autoAccept.value,
            onChanged: (value) {
              controller.autoAccept.value = value;
            },
          );
        }),
        Obx(() {
          return Visibility(
            visible: controller.autoAccept.value,
            child: SwitchListTile(
              contentPadding: const EdgeInsets.only(
                left: 28.0,
                right: kPadding,
              ),
              tileColor: Colors.white,
              title: Text(
                '自動列印工作單',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
                textAlign: TextAlign.left,
              ),
              value: controller.autoPrintTag.value,
              onChanged: (value) {
                controller.autoPrintTag.value = value;
              },
            ),
          );
        }),
        Obx(() {
          return Visibility(
            visible: controller.autoAccept.value,
            child: SwitchListTile(
              contentPadding: const EdgeInsets.only(
                left: 28.0,
                right: kPadding,
              ),
              tileColor: Colors.white,
              title: Text(
                '自動列印消費紀錄',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
                textAlign: TextAlign.left,
              ),
              value: controller.autoPrintReceipt.value,
              onChanged: (value) {
                controller.autoPrintReceipt.value = value;
              },
            ),
          );
        }),
      ],
    );
  }
}

class _SubPage3 extends GetView<OnlineOrderSettingsController> {
  const _SubPage3({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '關門前提前不接單',
          style: const TextStyle(
            fontSize: 16,
            color: kColorPrimary,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ).paddingSymmetric(
          vertical: kPadding,
        ),
        Obx(() {
          return SwitchListTile(
            contentPadding: kContentPadding,
            tileColor: Colors.white,
            title: Text(
              '啟用',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
              textAlign: TextAlign.left,
            ),
            value: controller.autoClose.value,
            onChanged: (value) {
              controller.autoClose.value = value;
            },
          );
        }),
        SettingsWidget.divider(),
        Obx(() {
          return Visibility(
            visible: controller.autoClose.value,
            child: ListTile(
              contentPadding: const EdgeInsets.only(
                left: 28.0,
                right: kPadding,
              ),
              tileColor: Colors.white,
              title: CustomEditor(
                initialValue: '${controller.settings?.closeMin ?? 0}',
                labelText: '提前幾分鐘關閉線上接單',
                hintText: '請輸入數字',
                onChanged: (value) {
                  //
                },
              ),
            ),
          );
        }),
      ],
    );
  }
}
