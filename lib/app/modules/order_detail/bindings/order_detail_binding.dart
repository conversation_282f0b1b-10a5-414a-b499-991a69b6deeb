import 'package:get/get.dart';

import '../controllers/order_detail_controller.dart';

class OrderDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<OrderDetailController>(
      () => OrderDetailController(
        orderProvider: Get.find(),
        invoiceProvider: Get.find(),
        productProvider: Get.find(),
        couponProvider: Get.find(),
        printerProvider: Get.find(),
        linePayProvider: Get.find(),
        tableProvider: Get.find(),
      ),
    );
  }
}
