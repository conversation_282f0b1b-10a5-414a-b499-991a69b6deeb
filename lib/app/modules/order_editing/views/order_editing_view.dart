import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/amount_edit.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/square_toggle.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/models/products_post_req.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/order_editing_controller.dart';

class OrderEditingView extends GetView<OrderEditingController> {
  final OrderEditingArgs _arguments;

  OrderEditingView({
    OrderEditingArgs arguments,
  }) : _arguments = arguments;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderEditingController>(
      init: OrderEditingController(
        productProvider: Get.find(),
        args: _arguments,
      ),
      builder: (controller) {
        return GestureDetector(
          onTap: () => Get.focusScope.unfocus(),
          child: DraggableScrollableSheet(
            initialChildSize: 0.8,
            builder: (BuildContext context, ScrollController scrollController) {
              return DecoratedBox(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                child: controller.obx((state) {
                  return BottomWidgetPage(
                    child: Obx(() => _list(scrollController)),
                    bottom: _bottomBar(),
                  );
                }),
              );
            },
          ),
        );
      },
    );
  }

  Widget _bottomBar() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(height: 12);
      yield AmountEdit(
        editingValue: controller.orderItem.quantity,
        onChanged: (value) {
          if (value != null) {
            controller.orderItem.quantity = value;
            HapticFeedback.lightImpact();
          }
        },
      );
      yield const SizedBox(height: kPadding);
      yield YesNoButton(
        leftButtonText: '取消',
        rightButtonText: '確認',
        onLeftPressed: () => Get.back(result: null),
        onRightPressed: () async {
          try {
            if (controller.checkIfAllRequiredCategoryHasProduct() &&
                controller.checkIfAllMultipleCategoryHasEnoughSelection()) {
              controller.finalizeOrderItem();
              //Go back and return the result.
              // if (controller.orderItem.value == null) {
              //   print('OOps! Return item is null?!');
              // }
              Get.back(result: controller.orderItem);
            }
          } catch (e) {
            Get.showAlert('$e');
          }
        },
      );
      yield const SizedBox(height: 12);
    }

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, -0.8),
          colors: [
            Color(0x00ffffff),
            Colors.white,
          ],
          stops: [0.0, 1.0],
        ),
      ),
      padding: const EdgeInsets.all(8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _list(ScrollController scrollController) {
    Iterable<Widget> children() sync* {
      // yield ProductInfoItem(
      //   large: true,
      //   themeColor: controller.prefProvider.themeColor,
      //   vipPrice: controller.productsPostReq.vipPrice,
      //   vipOnly: controller.productsPostReq.isVip?.switcher,
      //   vip: controller.vip,
      //   title: controller.productsPostReq.title,
      //   summary: controller.productsPostReq.summary,
      //   price: controller.productsPostReq.price,
      // );
      yield* _additionProductsSelection();
    }

    return ListView(
      controller: scrollController,
      padding: const EdgeInsets.only(
        top: kPadding,
        bottom: 140,
      ),
      physics: const AlwaysScrollableScrollPhysics(),
      children: children().toList(growable: false),
    );
  }

  //Draw the addition products selection UIs.
  Iterable<Widget> _additionProductsSelection() sync* {
    final length = controller.productsPostReq.additionCategories.length;
    for (int i = 0; i < length; ++i) {
      //Try get the additionProductSet first.
      final additionProductsGetRes = controller.getAdditionProductSet(i);
      if (additionProductsGetRes != null && additionProductsGetRes.isNotEmpty) {
        //We got the product list.
        //這邊做聰明一點，如果一個附加品分類事實上沒有任何東西，就乾脆跳過他不顯示
        yield Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: _additionProductSelector(
            controller.productsPostReq.additionCategories[i],
            additionProductsGetRes,
          ),
        );
        // );
      }
    }
  }

  Widget _additionProductSelector(AdditionCategorySetting category,
      Iterable<AdditionProduct> additionProductList) {
    Iterable<Widget> children() sync* {
      //Title text.
      String title = category.title;
      String note1 = (category.required == 1) ? ' (必選)' : '';
      String note2 = (category.option == 1)
          ? ' (複選) (' +
              category.optionMin.toString() +
              '~' +
              category.optionMax.toString() +
              ')'
          : '';
      yield Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Flexible(
            fit: FlexFit.loose,
            child: Text(
              title,
              style: Get.textTheme.headline6,
              overflow: TextOverflow.ellipsis,
              softWrap: false,
            ),
          ),
          Text(
            note1,
            style: Get.textTheme.headline6.copyWith(color: Colors.red),
          ),
          Text(
            note2,
            style: Get.textTheme.headline6.copyWith(color: Colors.green),
          ),
        ],
      );
      yield const SizedBox(height: 12);
      yield Wrap(
        spacing: 12,
        runSpacing: 12,
        children: _buildAdditionProductSelection(category, additionProductList)
            .toList(growable: false),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _buildAdditionProductSelection(
      AdditionCategorySetting category,
      Iterable<AdditionProduct> additionProductList) sync* {
    for (var element in additionProductList) {
      yield SizedBox(
        height: kMinInteractiveDimension,
        child: SquareToggle<num>(
          value: element.id,
          text: element.orderSelectionButtonText(),
          checked: controller.isAdditionProductSelected(element.id),
          onPressed: (value) {
            //加入此商品
            controller.setAdditionProduct(
              element,
              !controller.isAdditionProductSelected(element.id),
              category.option == 1,
              additionProductList,
            );
          },
          style: TextButton.styleFrom(
            // side: BorderSide(color: Color(0xffe2e2e2)),
            // shape: const StadiumBorder(
            //   side: BorderSide(color: Color(0xffe2e2e2)),
            // ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: Color(0xffe2e2e2)),
            ),
            minimumSize: Size.zero,
            backgroundColor: controller.isAdditionProductSelected(element.id)
                ? OKColor.Primary
                : Colors.white,
            padding: EdgeInsets.symmetric(
              vertical: 4,
              horizontal: 24,
            ),
          ),
        ),
      );
    }
  }
}
