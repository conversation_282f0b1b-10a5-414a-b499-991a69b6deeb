import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/table_provider.dart';
import 'package:muyipork/keys.dart';
import 'package:okshop_model/okshop_model.dart';

class OrderFilterPickerController extends GetxController
    with StateMixin<String> {
  final TableProvider tableProvider;
  PrefProvider get prefProvider => tableProvider.apiProvider.prefProvider;
  final data = <Table>[].obs;

  List<Table> getTableList(num partitionId) {
    final partition = data.firstWhere(
      (element) => element.id == partitionId,
      orElse: () => Table(),
    );
    return partition?.child ?? <Table>[];
  }

  List<Table> get currentTableList => getTableList(draft.table1Id);

  void trySelectTheFirstTable() {
    final ls = currentTableList;
    final table = ls.isNotEmpty ? ls.elementAt(0) : Table(id: 0);
    draft.table2Id = table.id;
  }

  final _draft = OrderReq().obs;
  OrderReq get draft => _draft.value;
  final name = ''.obs;
  final mobilePhone = ''.obs;

  Function get refreshDraft => _draft.refresh;

  final _titleText = ''.obs;
  String get titleText => _titleText.value;

  OrderFilterPickerController({
    @required this.tableProvider,
  });

  @override
  void onInit() {
    super.onInit();
    final parameters = Get.parameters ?? <String, String>{};
    if (parameters.containsKey(Keys.Title)) {
      _titleText.value = parameters[Keys.Title];
    }
    if (parameters.containsKey(Keys.Data)) {
      _draft.value = OrderReq.fromRawJson(parameters[Keys.Data]);
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {}

  Future<void> onRefresh() async {
    try {
      // 直接从缓存中读取数据，按parentId重建层级关系
      final cachedTables = tableProvider.cached.values;
      final partitions = <Table>[];
      
      // 找出所有区域（parentId = 0）
      final areas = cachedTables.where((table) => table.parentId == 0).toList();
      areas.sort((a, b) => (a.sort ?? 0).compareTo(b.sort ?? 0));
      
      // 为每个区域添加其子桌号
      // for (final area in areas) {
      //   final tables = cachedTables.where((table) => table.parentId == area.id).toList();
      //   tables.sort((a, b) => (a.sort ?? 0).compareTo(b.sort ?? 0));
        
      //   final partition = Table(
      //     id: area.id,
      //     name: area.name,
      //     parentId: area.parentId,
      //     sort: area.sort,
      //     child: tables,
      //   );
      //   partitions.add(partition);
      // }
      
      data.clear();
      data.addAll(areas);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }
}
