import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bg_default.dart';
import 'package:muyipork/app/components/general_appbar.dart';
import 'package:muyipork/app/components/receipt_view.dart';
import 'package:screenshot/screenshot.dart';

import '../controllers/order_receipt_controller.dart';

class OrderReceiptView extends GetView<OrderReceiptController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderReceiptController>(
      init: OrderReceiptController(
        apiProvider: Get.find(),
      ),
      builder: (controller) {
        return BgDefault(
          child: Scaffold(
            backgroundColor: Colors.transparent,
            appBar: GeneralAppbar(
              title: Text(
                '消費明細',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
              actions: [
                IconButton(
                  icon: Icon(
                    Icons.print,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    this.controller.print();
                  },
                ),
              ],
            ),
            body: Container(
              alignment: Alignment.center,
              color: Colors.white,
              child: controller.data.obx((state) {
                return SingleChildScrollView(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Screenshot(
                      controller: this.controller.screenshotController,
                      child: Container(
                        width: 375.0,
                        // width: 300.0,
                        color: Colors.white,
                        child: ReceiptView(
                          accountName: controller.accountName,
                          storeName: controller.storeName,
                          ordersOrderIdGetRes: state,
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }
}
