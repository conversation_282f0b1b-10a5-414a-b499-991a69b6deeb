// 餐飲, 處理中, 前結帳, 含全部 Tab 顯示定義
import 'package:okshop_common/okshop_common.dart';

final TAB_DEFINE_MEAL_PRE_CHECKOUT = [
  {
    'order_type': [
      OrderType.DinnerHere.index,
      OrderType.DinnerToGo.index,
      OrderType.DinnerDelivery.index,
      OrderType.DinnerOrder.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '全部',
  },
  {
    'order_type': [
      OrderType.DinnerHere.index,
      OrderType.DinnerOrder.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '內用',
  },
  {
    'order_type': [
      OrderType.DinnerToGo.index,
    ],
    'order_source': [
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '自取',
  },
  {
    'order_type': [
      OrderType.DinnerDelivery.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '外送',
  },
];

// 餐飲, 處理中, 後結帳, 含全部 Tab 顯示定義
final TAB_DEFINE_MEAL_POST_CHECKOUT = [
  {
    'order_type': [
      OrderType.DinnerHere.index,
      OrderType.DinnerToGo.index,
      OrderType.DinnerDelivery.index,
      OrderType.DinnerOrder.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '全部',
  },
  {
    'order_type': [
      OrderType.DinnerHere.index,
      OrderType.DinnerOrder.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '內用',
  },
  {
    'order_type': [
      OrderType.DinnerToGo.index,
    ],
    'order_source': [
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '自取',
  },
  {
    'order_type': [
      OrderType.DinnerDelivery.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '外送',
  },
  {
    'order_type': [
      OrderType.DinnerToGo.index,
    ],
    'order_source': [
      OrderSource.App.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '外帶',
  },
];

// 餐飲, 已完成 Tab 顯示定義
final TAB_DEFINE_MEAL_ALL = [
  {
    'order_type': [
      OrderType.DinnerHere.index,
      OrderType.DinnerOrder.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'checkout_at',
    'sort': 'DESC',
    'name': '內用',
  },
  {
    'order_type': [
      OrderType.DinnerToGo.index,
    ],
    'order_source': [
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'checkout_at',
    'sort': 'DESC',
    'name': '自取',
  },
  {
    'order_type': [
      OrderType.DinnerDelivery.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'checkout_at',
    'sort': 'DESC',
    'name': '外送',
  },
  {
    'order_type': [
      OrderType.DinnerToGo.index,
    ],
    'order_source': [
      OrderSource.App.index,
    ],
    'store_type': StoreType.Dinner.index,
    'sort_type': 'checkout_at',
    'sort': 'DESC',
    'name': '外帶',
  },
];

// 零售, 處理中, 含全部 Tab 顯示定義
final TAB_DEFINE_ACTIVE_RETAIL = [
  {
    'order_type': [
      OrderType.RetailToGo.index,
      OrderType.RetailDelivery.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Retail.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '全部',
  },
  {
    'order_type': [
      OrderType.RetailToGo.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Retail.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '自取',
  },
  {
    'order_type': [
      OrderType.RetailDelivery.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Retail.index,
    'sort_type': 'meal_at',
    'sort': 'ASC',
    'name': '宅配',
  },
  // TODO: Implement this in the future.
  // {
  //   'order_type': OrderType.RetailInStore,
  //   'order_source': [OrderSource.App, OrderSource.Line],
  //   'sort_type': 'meal_at',
  //   'sort': 'ASC',
  //   'name': '外帶',
  // },
];
// 零售, 已完成 Tab 顯示定義
final TAB_DEFINE_RETAIL = [
  {
    'order_type': [
      OrderType.RetailToGo.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Retail.index,
    'sort_type': 'meal_at',
    'sort': 'DESC',
    'name': '自取',
  },
  {
    'order_type': [
      OrderType.RetailDelivery.index,
    ],
    'order_source': [
      OrderSource.App.index,
      OrderSource.Line.index,
    ],
    'store_type': StoreType.Retail.index,
    'sort_type': 'meal_at',
    'sort': 'DESC',
    'name': '宅配',
  },
  // TODO: Implement this in the future.
  // {
  //   'order_type': OrderType.RetailInStore,
  //   'order_source': [OrderSource.App, OrderSource.Line],
  //   'sort_type': 'meal_at',
  //   'sort': 'ASC',
  //   'name': '外帶',
  // },
];
