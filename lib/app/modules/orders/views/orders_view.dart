import 'dart:async';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/badge.dart';
import 'package:muyipork/app/components/blank_page.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/custom_tab_bar.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/general_appbar.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/order_list_item.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/models/qr_format.dart';
import 'package:muyipork/app/models/orders_orderid_status_put_qry.dart';
import 'package:muyipork/app/models/orders_orderid_status_put_req.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/app/models/orders_collection.dart';
import 'package:muyipork/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:muyipork/app/modules/orders_selector/controllers/orders_selector_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

import '../controllers/orders_controller.dart';

class OrdersView extends GetView<OrdersController> {
  final Iterable<OrderStatus> orderStatusList;
  final String defaultTitleText;
  final OrdersViewMode ordersViewMode;

  @override
  final String tag;

  OrdersView({
    Key key,
    this.orderStatusList,
    this.defaultTitleText,
    this.tag,
    this.ordersViewMode,
  }) : super(key: key);

  // 選擇取消訂單原因 Dialog (cancel)
  // 0: 處理中
  // 3: 店家取消
  // 6: 顧客取消
  Future<OrderStatus> _showCancelDialog() {
    final completer = new Completer<OrderStatus>();
    final selected = Rx<OrderStatus>(rejectActions.first);
    DialogGeneral(DialogArgs(
      header: Text(
        '訂單狀態',
        style: const TextStyle(
          fontSize: 20,
          color: OKColor.Gray33,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
      mainButtonText: '確認',
      secondaryButtonText: '取消',
      onMainButtonPress: () {
        completer.complete(selected.value);
      },
      content: ObxValue<Rx<OrderStatus>>((value) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(rejectActions.length, (index) {
            final data = rejectActions.elementAt(index);
            return RadioListTile(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: kPadding,
                vertical: 0.0,
              ),
              title: Text(
                data.dialogName ?? '',
                style: const TextStyle(
                  fontSize: 20,
                  color: StatusColor.Normal,
                ),
                textAlign: TextAlign.left,
              ),
              value: data,
              groupValue: selected.value,
              onChanged: (value) => selected.value = value,
            );
          }),
        );
      }, selected),
    )).dialog();
    return completer.future;
  }

  // 退貨/退款確認 Dialog (reject)
  Future<Switcher> _showRejectDialog() {
    final completer = Completer<Switcher>();
    DialogGeneral(DialogArgs(
      header: Text(
        '訂單退貨/退款',
        style: const TextStyle(
          fontSize: 20,
          color: OKColor.Gray33,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
      mainButtonText: '確認',
      secondaryButtonText: '取消',
      onMainButtonPress: () {
        completer.complete(Switcher.On);
      },
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '您確定要退貨/退款嗎？',
            style: const TextStyle(
              fontSize: 16,
              color: kColorError,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 22.0,
          ),
          Text(
            '按下確認後，訂單將顯示為退貨/退款。',
            style: const TextStyle(
              fontSize: 16,
              color: OKColor.Gray66,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    )).dialog();
    return completer.future;
  }

  // 提示退點資訊 Dialog
  Future<void> _showRejectHint(OrderSummary orderSummary) async {
    await DialogGeneral(
      DialogArgs(
        header: DialogGeneral.titleText('此訂單已取消'),
        contentIcon: DialogContentIcon.None,
        content: Column(
          children: [
            Text(
              '是否進入該會員主頁\n進行退點?\n',
              style: TextStyle(
                fontSize: 16,
                color: OKColor.Gray66,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              '訂單金額:\$${orderSummary.total.decimalStyle}元',
              style: TextStyle(
                fontSize: 16,
                color: OKColor.Primary,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        mainButtonText: '進入會員主頁',
        secondaryButtonText: '完成離開',
        onMainButtonPress: () {
          Get.toNamed(
            Routes.MEMBER_DETAIL,
            parameters: <String, String>{
              Keys.Tag: '${orderSummary.member.id}',
              Keys.Data: QrFormat(memberId: orderSummary.member.id).toRawJson(),
            },
          );
        },
        // onSecondaryButtonPress: () {},
      ),
    ).dialog();
  }

  // FIXME: GetX error
  Widget _itemReceipt(OrderSummary orderSummary) {
    final localSettings = controller.prefProvider.localSettings;
    final switcher = localSettings?.printReceipt?.switcher ?? Switcher.On;
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 12,
      ),
      color: const Color(0xffdedee6),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: const Color(0x29000000),
              offset: Offset(0, 3),
              blurRadius: 6,
            ),
          ],
        ),
        child: ObxValue<RxBool>((value) {
          return SwitchListTile(
            value: switcher.isOn,
            onChanged: (value) {
              final localSettings = controller.prefProvider.localSettings;
              localSettings.printItemReceipt = value.switcher.index;
              controller.prefProvider.localSettings = localSettings;
            },
            title: Row(
              children: [
                Text(
                  '列印「${orderSummary.orderTypeName}明細」',
                  style: TextStyle(
                    fontSize: 16,
                    color: const Color(0xff6d7278),
                  ),
                  textAlign: TextAlign.left,
                ),
                Expanded(
                  child: CustomEditor.number(
                    scrollPadding:
                        EdgeInsets.only(bottom: kBottomButtonPadding),
                    textAlign: TextAlign.center,
                    controller: controller.itemReceiptEditing,
                    onChanged: (value) {
                      final count = int.tryParse(value) ?? 0;
                      final localSettings =
                          controller.prefProvider.localSettings;
                      localSettings.printItemReceiptCount = count;
                      controller.prefProvider.localSettings = localSettings;
                    },
                  ).paddingSymmetric(
                    horizontal: 8,
                  ),
                ),
                Text(
                  '張',
                  style: TextStyle(
                    fontSize: 16,
                    color: const Color(0xff6d7278),
                  ),
                  textAlign: TextAlign.left,
                ),
              ],
            ),
          );
        }, switcher.isOn.obs),
      ),
    );
  }

  // FIXME: GetX error
  Widget _receipt(OrderSummary orderSummary) {
    final localSettings = controller.prefProvider.localSettings;
    final switcher = localSettings?.printReceipt?.switcher ?? Switcher.On;
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 12,
      ),
      color: const Color(0xffdedee6),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: const Color(0x29000000),
              offset: Offset(0, 3),
              blurRadius: 6,
            ),
          ],
        ),
        child: ObxValue<RxBool>((value) {
          return SwitchListTile(
            value: value.value,
            onChanged: (value) {
              final localSettings = controller.prefProvider.localSettings;
              localSettings.printReceipt = value.switcher.index;
              controller.prefProvider.localSettings = localSettings;
            },
            title: Row(
              children: [
                Text(
                  '列印「消費明細」',
                  style: TextStyle(
                    fontSize: 16,
                    color: const Color(0xff6d7278),
                  ),
                  textAlign: TextAlign.left,
                ),
                Expanded(
                  child: CustomEditor.number(
                    scrollPadding:
                        EdgeInsets.only(bottom: kBottomButtonPadding),
                    textAlign: TextAlign.center,
                    controller: controller.receiptEditing,
                    onChanged: (value) {
                      final count = int.tryParse(value) ?? 0;
                      final localSettings =
                          controller.prefProvider.localSettings;
                      localSettings.printReceiptCount = count;
                      controller.prefProvider.localSettings = localSettings;
                    },
                  ).paddingSymmetric(
                    horizontal: 8,
                  ),
                ),
                Text(
                  '張',
                  style: TextStyle(
                    fontSize: 16,
                    color: const Color(0xff6d7278),
                  ),
                  textAlign: TextAlign.left,
                ),
              ],
            ),
          );
        }, switcher.isOn.obs),
      ),
    );
  }

  // TODO: status -> OrderStatus
  Future<void> changeStatus(OrderSummary data, num status) async {
    Get.showLoading();
    try {
      final res = await controller.orderProvider.putOrderStatus(
        data.id,
        OrdersOrderIdStatusPutQry(isPushMsg: Switcher.Off.index),
        OrdersOrderIdStatusPutReq(status: status),
      );
      Get.back();
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  void _search() {
    final filter = OrderReq(
      status: orderStatusList.map((e) => e.index).toList(growable: false),
    );
    final titleText = () {
      if ('1' == tag) {
        return '訂單搜尋';
      }
      if ('2' == tag) {
        switch (controller.prefStoreType) {
          case StoreType.Dinner:
            return '消費紀錄搜尋';
          case StoreType.Retail:
            return '訂單紀錄搜尋';
          default:
        }
      }
      return '搜尋';
    };
    Get.toNamed(
      Routes.ORDER_FILTER_PICKER,
      parameters: {
        Keys.Data: filter.toRawJson(),
        Keys.Title: titleText(),
      },
    ).then(
      (value) {
        if (value is String && value.isNotEmpty) {
          Get.toNamed(
            Routes.ORDER_LIST,
            parameters: <String, String>{
              Keys.Data: value,
              Keys.Title: '搜尋結果',
            },
          );
        }
      },
    );
  }

  Widget _main() {
    var displayBusinessHoursToggle = true;
    if (controller.prefProvider.storeType.isRetail) {
      displayBusinessHoursToggle = false;
    }
    if (OrdersViewMode.CompletedOrders == controller.ordersViewMode) {
      displayBusinessHoursToggle = false;
    }
    return StandardPage(
      backgroundColor: controller.currentThemeColor,
      appBar: GeneralAppbar(
        title: _title(),
        leading: displayBusinessHoursToggle == true
            ? _businessHourToggle()
            : SizedBox.shrink(),
        leadingWidth: 104,
        actions: _actions(),
      ),
      child: _mainext(),
    );
  }

  Widget _mainext() {
    return SizedBox.expand(
      child: DecoratedBox(
        decoration: const BoxDecoration(
          borderRadius: const BorderRadius.vertical(
            top: const Radius.circular(30.0),
          ),
          gradient: const LinearGradient(
            begin: const Alignment(0.0, -1.0),
            end: const Alignment(0.0, 1.0),
            colors: const [
              const Color(0xff3e4b5a),
              const Color(0xffeeeef3),
            ],
            stops: const [
              0.0,
              1.0,
            ],
          ),
        ),
        child: Column(
          children: [
            //The TabBar
            SizedBox(
              width: double.infinity,
              child: _tabWrap(),
            ),
            //The order item list.
            // TODO: obx()
            Expanded(
              // child: _listWrap(),
              child: RefreshIndicator(
                onRefresh: controller.onRefresh,
                child: controller.obx(
                  (state) => Obx(() => _list()),
                  onError: ListWidget.message,
                  onEmpty: ListWidget.blank(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // FIXME: try remove me.
  Widget _tabWrap() {
    return CustomTabBar(
      themeColor: controller.prefProvider.themeColor,
      isScrollable: false,
      tabs: _buildTabs().toList(growable: false),
      controller: controller.tabController,
      onTap: (value) => controller.selectedTabType = value,
      indicatorPadding: EdgeInsets.only(
        top: 42,
        left: 28,
        right: 28,
        bottom: 6,
      ),
    );
  }

  // FIXME: try remove me.
  // Widget _listWrap() {
  //   return SmartRefresher(
  //     physics: const AlwaysScrollableScrollPhysics(),
  //     scrollController: controller.scroll,
  //     controller: controller.refreshController,
  //     enablePullDown: true,
  //     enablePullUp: true,
  //     onRefresh: controller.onRefresh,
  //     onLoading: controller.onEndScroll,
  //     footer: ClassicFooter(loadStyle: LoadStyle.ShowWhenLoading),
  //     child: _list(),
  //   );
  // }

  Widget _list() {
    final it = controller.cached;
    if (it.isEmpty) return BlankPage();
    return ListView.separated(
      padding: EdgeInsets.only(bottom: kBottomPadding),
      controller: controller.scroll,
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: it.length + (controller.activeCollection.nnHasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < it.length) {
          return _item(it.elementAt(index));
        }
        return ListWidget.bottomProgressing();
      },
      separatorBuilder: (context, index) {
        return OrderListItem.divider();
      },
    );
  }

  Widget _item(OrderSummary order) {
    return OrderListItem(
      // 進行中訂單才需要顯示按鈕
      showActionButtons:
          ORDERS_STATUS_ACTIVE.contains(order.status.orderStatus),
      data: order,
      checked: false,
      showCheckBox: false,
      onDetailPressed: () async {
        final id = order.masterId ?? order.id;
        final shortCut = await Get.toNamed(
          Routes.ORDER_DETAIL,
          parameters: <String, String>{
            Keys.Tag: '$id',
            Keys.Id: '$id',
          },
        ) as OrderDetailViewShortCut;

        if (shortCut != null) {
          //Do as the shortcut ask
          switch (shortCut) {
            case OrderDetailViewShortCut.AcceptOrder:
              await controller.acceptOrder(order);
              break;
            case OrderDetailViewShortCut.RejectOrder:
              if (PaymentStatus.Paid == order.paymentStatus.paymentStatus) {
                // 已付款單，執行退點
                await _rejectOrder(order);
              } else {
                await _cancelOrder(order);
              }
              break;
            case OrderDetailViewShortCut.CheckoutOrder:
              // 如果是已付費單，執行歸檔
              if (PaymentStatus.Paid == order.paymentStatus.paymentStatus) {
                await _archive(order);
              } else {
                checkOutOrder(order);
              }
              break;
            case OrderDetailViewShortCut.Refresh:
              await controller.onRefresh();
              break;
            default:
              break;
          }
        }
        controller.update(null, true);
      },
      onAcceptPressed: () async {
        // 如果是已付費單，執行歸檔
        if (PaymentStatus.Paid == order.paymentStatus.paymentStatus) {
          await _archive(order);
        } else {
          await controller.acceptOrder(order);
        }
      },
      onRejectPressed: () async {
        if (PaymentStatus.Paid == order.paymentStatus.paymentStatus) {
          await _rejectOrder(order);
        } else {
          await _cancelOrder(order);
        }
      },
      onCheckoutPressed: () => checkOutOrder(order),
    );
  }

  // TODO: 執行歸檔
  Future<void> _archive(OrderSummary orderSummary) async {
    // 變更訂單狀態已完成
    await FutureProgress(
      // future: changeStatus(orderSummary, OrderStatus.Completed.index),
      future: controller.archive(orderSummary),
    ).dialog();
    Get.back();
    // 執行封存 (改變 order status)
    // 判斷是否需發票
    // 不需發票則列印
    // FIXME: temporary fix
    // await controller.acceptOrder(orderSummary);
    // await _archiveWithInvoice(orderSummary);
    // await _archiveWithoutInvoice(orderSummary);
  }

  Future<void> _archiveWithInvoice(OrderSummary orderSummary) async {
    final children = <Widget>[];
    children.addIf(true, _itemReceipt(orderSummary));
    children.addIf(true, _receipt(orderSummary));
    await DialogGeneral(
      DialogArgs(
        header: DialogGeneral.titleText('是否列印?'),
        contentIcon: DialogContentIcon.None,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: children,
        ),
        mainButtonText: '列印',
        secondaryButtonText: '離開',
        onMainButtonPress: () {
          // TODO: 執行列印
        },
        // onSecondaryButtonPress: () {},
      ),
    ).dialog();
  }

  Future<void> _archiveWithoutInvoice(OrderSummary orderSummary) async {
    final children = <Widget>[];
    children.addIf(true, _itemReceipt(orderSummary));
    children.addIf(true, _receipt(orderSummary));
    await DialogGeneral(
      DialogArgs(
        header: DialogGeneral.titleText('是否列印?'),
        contentIcon: DialogContentIcon.None,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: children,
        ),
        mainButtonText: '列印',
        secondaryButtonText: '離開',
        onMainButtonPress: () {
          // TODO: 執行列印
        },
        // onSecondaryButtonPress: () {},
      ),
    ).dialog();
  }

  List<Widget> _actions() {
    final children = <Widget>[];

    children.addIf(
      true,
      IconButton(
        icon: Icon(
          Icons.search,
          color: Colors.white,
        ),
        onPressed: _search,
        // onPressed: () {
        //   showSearch(
        //     context: context,
        //     delegate: CustomSearchDelegate(
        //       tag: this.tag,
        //       status: this.status,
        //     ),
        //   );
        // },
      ),
    );
    return children;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersController>(
      tag: tag,
      init: OrdersController(
        orderProvider: Get.find(),
        printerProvider: Get.find(),
        ordersViewMode: ordersViewMode,
        filterStatus: orderStatusList,
        productProvider: Get.find(),
      ),
      builder: (controller) {
        return controller.obx(
          (state) => Obx(() => _main()),
          onEmpty: ListWidget.blank(),
          onError: (error) => ListWidget.message(error),
        );
      },
    );
  }

  Iterable<Widget> _buildTabs() {
    return controller.prefCollections.map((e) {
      return _Tab(
        titleText: e.displayName,
        badge: e.cached.length,
        showBadge: e.cached.length > 0 && ordersViewMode.isActiveOrders,
        backgroundColor: controller.currentThemeColor,
      );
    });
  }

  Future<void> _cancelOrder(OrderSummary orderSummary) async {
    try {
      final value = await _showCancelDialog();
      switch (value) {
        case OrderStatus.CancelByApp:
        case OrderStatus.CancelByLine:
          await changeStatus(orderSummary, value.index);
          break;
        default:
      }
    } catch (e) {
      DialogGeneral.alert('$e');
    }
  }

  Future<void> _rejectOrder(OrderSummary orderSummary) async {
    try {
      final value = await _showRejectDialog();
      if (value == Switcher.On) {
        await FutureProgress(
          future: changeStatus(orderSummary, OrderStatus.Rejection.index),
        ).dialog();
        Get.back();
        // 提示退點資訊
        await _showRejectHint(orderSummary);
        // TODO: 作廢發票
      }
    } catch (e) {
      DialogGeneral.alert('$e');
    }
  }

  void checkOutOrder(OrderSummary order) {
    if (order.combineAvailable) {
      // 傳入過濾條件
      Get.toNamed(
        Routes.ORDERS_SELECTOR,
        parameters: {Keys.Tag: '${order.id}'},
        arguments: OrdersSelectorArgs(
          filter: OrderSelectorFilter(
            id: order.id,
            table1Id: order.table1Id,
            table2Id: order.table2Id,
            table1Name: order.table1Name,
            table2Name: order.table2Name,
            sourceOrKind: order.source,
          ),
        ),
      );
    } else {
      Get.toNamed(
        Routes.ORDERS_SUM_UP,
        arguments: [order],
        parameters: <String, String>{
          Keys.Tag: '${DateTime.now().millisecondsSinceEpoch}',
        },
      );
    }
  }

  Widget _title() {
    final children = <Widget>[];
    children.addIf(
      true,
      Text(
        controller.prefStoreType.displayName ?? '',
        style: TextStyle(
          fontSize: 16.0,
          color: Colors.white,
        ),
      ),
    );
    final isMixedType = controller.prefProvider.brandsType?.isMixed ?? false;
    children.addIf(
      isMixedType,
      SizedBox(width: 4),
    );
    children.addIf(
      isMixedType,
      Badge(
        textColor: controller.currentThemeColor,
        // text: '',
        // text: '123',
        // show badge count on active orders only
        text: OrdersViewMode.ActiveOrders == ordersViewMode
            ? controller.appBusinessModeTitleNotifyNum.decimalStyle
            : '',
        leading: Icon(
          Icons.keyboard_arrow_down,
          size: 16,
          color: controller.currentThemeColor,
        ),
      ),
    );
    return TextButton(
      onPressed: isMixedType ? controller.prefProvider.toggleStoreType : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: children,
      ),
    );
  }

  // 切換營業時間顯示模式按鈕
  Widget _businessHourToggle() {
    final children = <Widget>[];
    children.addIf(
      true,
      Text(
        controller.prefOrdersBusinessHoursMode.toggleDisplayName,
        style: Get.textTheme.subtitle1.copyWith(color: Colors.white),
        softWrap: false,
      ),
    );
    children.addIf(
      controller.activeOrderBadgeCount > 0,
      Align(
        alignment: Alignment.topRight,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Transform.translate(
              offset: Offset(12, 0),
              child: Badge(
                textColor: controller.currentThemeColor,
                text: controller.activeOrderBadgeCount.decimalStyle,
              ),
            ),
          ],
        ),
      ),
    );
    return TextButton(
      onPressed: () {
        // TODO: 可能要做防止連續切換
        controller.prefProvider.toggleOrdersBusinessHoursMode();
      },
      child: Stack(
        alignment: Alignment.center,
        // clipBehavior: Clip.none,
        children: children,
      ),
    );
  }
}

class _Tab extends StatelessWidget {
  final String titleText;
  final num badge;
  final bool showBadge;
  final Color backgroundColor;

  const _Tab({
    Key key,
    this.titleText,
    this.badge,
    this.showBadge,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[];
    children.addIf(
      true,
      Align(
        alignment: Alignment.bottomCenter,
        child: Text(
          titleText ?? '',
          style: TextStyle(
            fontSize: 17,
          ),
        ),
      ),
    );
    children.addIf(
      showBadge == true,
      Container(
        alignment: Alignment.topCenter,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Badge(
              backgroundColor: backgroundColor,
              textColor: Colors.white,
              text: '$badge',
            ),
          ],
        ).paddingOnly(left: 42),
      ),
    );
    return SizedBox(
      height: 32,
      child: Stack(
        alignment: Alignment.center,
        children: children,
      ),
    );
  }
}

extension _ExtensionOrderSummary on OrderSummary {
  String get orderTypeName {
    if (type.orderType.isDinner) {
      return '餐點';
    }
    if (type.orderType.isRetail) {
      return '商品';
    }
    return '';
  }
}

extension _ExtensionOrderStatus on OrderStatus {
  String get dialogName {
    switch (this) {
      case OrderStatus.Padding:
        return '訂單確認';
      case OrderStatus.CancelByApp:
        return '店家取消';
      case OrderStatus.CancelByLine:
        return '顧客取消';
      default:
        return '';
    }
  }
}
