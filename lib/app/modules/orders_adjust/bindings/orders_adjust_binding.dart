import 'package:get/get.dart';

import '../controllers/orders_adjust_controller.dart';

class OrdersAdjustBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<OrdersAdjustController>(
      () => OrdersAdjustController(
        productProvider: Get.find(),
        orderProvider: Get.find(),
        couponProvider: Get.find(),
        invoiceProvider: Get.find(),
        printerProvider: Get.find(),
        tableProvider: Get.find(),
      ),
    );
  }
}
