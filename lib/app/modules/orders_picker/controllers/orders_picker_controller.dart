import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:tuple/tuple.dart';

class OrdersPickerArgs {
  OrdersPickerArgs({
    @required this.sourceOrders,
    @required this.titleText,
  });

  //傳入所有訂單作為過濾前的來源
  final List<OrderSummary> sourceOrders;
  final String titleText;
}

class OrdersPickerController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final OrderProvider orderProvider;
  ApiProvider get apiProvider => orderProvider.apiProvider;
  final isFetching = false.obs;

  //The actual display orders after been filtered.
  final displayingOrderList = <Tuple2<OrderSummary, bool>>[].obs;

  //頁面運作參數
  final Rx<OrdersPickerArgs> args = Rx<OrdersPickerArgs>(null);

  String get titleText => '新增合併帳單';
  String get headerText => args.value.titleText ?? '';

  OrdersPickerController({
    @required this.orderProvider,
  });

  @override
  void onInit() {
    super.onInit();

    //嘗試取得頁面參數
    args.value = Get.arguments;

    //This will try filter the source and added to displayingOrderList.
    // addOrders(args.value.sourceOrders);
  }

  @override
  void onReady() {
    super.onReady();
    try {
      args.value.sourceOrders
          .removeWhere((element) => true == element.usingPoints);
      _addOrders(args.value.sourceOrders);
      if (displayingOrderList.isNotEmpty) {
        change('', status: RxStatus.success());
      } else {
        change('', status: RxStatus.empty());
      }
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  //This will try add things to the displayingOrderList without any filter!.
  void _addOrders(Iterable<OrderSummary> orders) {
    final ids = displayingOrderList.map((element) => element.item1.id);
    final entries = orders
        .where((element) => !ids.contains(element.id))
        .map((e) => MapEntry(e.id, Tuple2(e, true)));
    final it = Map.fromEntries(entries).values;
    displayingOrderList.addAll(it);
  }

  //取得當前勾選的 Orders.
  Iterable<OrderSummary> getToggledOrders() {
    final entries = displayingOrderList
        .where((element) => element.item2)
        .map((element) => MapEntry(element.item1.id, element.item1));
    return Map.fromEntries(entries).values;
  }
}
