import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/order_list_item.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:tuple/tuple.dart';

import '../controllers/orders_picker_controller.dart';

class OrdersPickerView extends GetView<OrdersPickerController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: controller.titleText,
      child: controller.obx(
        (state) => BottomWidgetPage(
          child: _children().column(),
          bottom: _bottom(),
        ),
        onEmpty: _empty(),
      ),
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield ListWidget.header(controller.headerText ?? '');
      yield Expanded(
        child: Center(
          child: Text(
            '查無訂單',
            style: Get.textTheme.headline5.copyWith(
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _bottom() {
    return BottomWrapper(
      child: YesNoButton(
        leftButtonText: '取消',
        rightButtonText: '加入',
        onLeftPressed: () => Get.back(),
        onRightPressed: () {
          //PickOrders 用
          //將選擇好的 Orders 做回傳
          Get.back(result: controller.getToggledOrders());
        },
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ListWidget.header(controller.headerText ?? '');
    yield Obx(() => _list()).expanded();
  }

  Widget _list() {
    final it = controller.displayingOrderList;
    return ListView.separated(
      padding: const EdgeInsets.only(bottom: kBottomButtonPadding),
      itemBuilder: (context, index) {
        final data = it.elementAt(index);
        return OrderListItem(
          showActionButtons: false,
          data: data.item1,
          showCheckBox: true,
          checked: data.item2,
          onCheckChanged: (b) {
            it[index] = Tuple2<OrderSummary, bool>(data.item1, b);
            it.refresh();
          },
          onDetailPressed: () {
            final order = data.item1;
            final id = order.masterId ?? order.id;
            Get.toNamed(
              Routes.ORDER_DETAIL,
              parameters: {
                Keys.Tag: '$id',
                Keys.Id: '$id',
              },
            );
          },
        );
      },
      separatorBuilder: (context, index) {
        return OrderListItem.divider();
      },
      itemCount: it.length,
    );
  }
}
