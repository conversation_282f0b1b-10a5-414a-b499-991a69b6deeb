import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/providers/table_provider.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/extension.dart';

class OrdersSelectorArgs {
  OrdersSelectorArgs({
    @required this.filter,
  });

  //初始作為過濾來源的那張訂單
  final OrderSelectorFilter filter;
}

class OrdersSelectorController extends GetxController with StateMixin<String> {
  final OrderProvider orderProvider;
  final TableProvider tableProvider;
  ApiProvider get apiProvider => orderProvider.apiProvider;
  // 列表 <order.id, checked>
  final checkedList = <num, bool>{}.obs;
  final _filter = Rx<OrderSelectorFilter>(null);

  // 取得 order instance
  Iterable<OrderSummary> get data {
    final ids = checkedList.keys; // 取得列表中的 order.id
    return orderProvider.activeOrders.where((order) => ids.contains(order.id));
  }

  // 取得當前勾選的 Orders.
  Iterable<OrderSummary> get selectedOrders {
    return data.where((element) => true == checkedList[element.id]);
  }

  final _disposable = Completer();

  OrdersSelectorController({
    @required this.orderProvider,
    @required this.tableProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _filter.stream
        .asBroadcastStream()
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.arguments is OrdersSelectorArgs) {
      final args = Get.arguments as OrdersSelectorArgs;
      _filter.value = args.filter;
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  // 加入 checked list
  Future<void> onRefresh() async {
    // 重新搜尋清空所有內容
    checkedList.clear();
    final orders = getFilteredOrders(_filter.value);
    addOrders(orders);
    final status = checkedList.isEmpty ? RxStatus.empty() : RxStatus.success();
    change('', status: status);
  }

  // The filter tables display string.
  String filterDisplayName(num table1Id, num table2Id, String queryNum) {
    final tableName = tableProvider.getDisplayName(table1Id, table2Id);
    final orderNum = queryNum ?? '';
    return '$tableName($orderNum)';
  }

  // 重要: 過濾可合併結帳的條件
  // 嘗試回傳被過濾過後的 Orders
  Iterable<OrderSummary> getFilteredOrders(OrderSelectorFilter filter) {
    return orderProvider.activeOrders.where(filter.validate);
  }

  // This will try add things to the displayingOrderList without any filter!.
  // 加入新的訂單，預設為選取
  void addOrders(Iterable<OrderSummary> orders) {
    final entries = orders.map((e) => MapEntry(e.id, true));
    checkedList.addEntries(entries);
  }
}
