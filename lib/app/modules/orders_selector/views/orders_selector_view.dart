import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/order_list_item.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/app/modules/orders_picker/controllers/orders_picker_controller.dart';
import 'package:muyipork/app/modules/tables_selection/controllers/tables_selection_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:tuple/tuple.dart';

import '../controllers/orders_selector_controller.dart';

class OrdersSelectorView extends GetView<OrdersSelectorController> {
  const OrdersSelectorView({
    Key key,
  }) : super(key: key);

  Iterable<Widget> _actions() sync* {
    yield IconButton(
      icon: Icon(
        Icons.add_circle_outline,
        color: Colors.white,
      ),
      onPressed: _onAddPressed,
    );
  }

  Future<void> _onAddPressed() async {
    //Open tables selection for select filter table.
    //開啟搜尋介面
    //回傳 <PartitionID, TableId, queryNum>
    // TODO: 置換參數，使用結構，移除 Tuple3
    Tuple3<int, int, String> resultPartitionTable = await Get.toNamed(
        Routes.TABLES_SELECTION,
        arguments: TablesSelectionArgs(
            table1Id: -1,
            table2Id: -1,
            withNumQueryField: true)) as Tuple3<int, int, String>;

    if (resultPartitionTable != null) {
      //這邊過濾過後再給 Picker 去處理一次
      final filteredOrders = controller.getFilteredOrders(
        OrderSelectorFilter(
          table1Id: resultPartitionTable.item1,
          table2Id: resultPartitionTable.item2,
          last3Digits: resultPartitionTable.item3,
        ),
      );

      //過濾後取得該讓使用者選擇的 Orders 丟入 Picker 選擇頁面。
      // print('[Open ORDERS_PICKER]');
      //把搜尋結果再開一個 Orders Selector 讓使用者選擇。
      final selectedOrders = await Get.toNamed(
        Routes.ORDERS_PICKER,
        arguments: OrdersPickerArgs(
          sourceOrders: filteredOrders.toList(),
          titleText: controller.filterDisplayName(
            resultPartitionTable.item1,
            resultPartitionTable.item2,
            resultPartitionTable.item3,
          ),
        ),
      ) as Iterable<OrderSummary>;

      //嘗試加入所選取的東西
      if (selectedOrders != null && selectedOrders.isNotEmpty) {
        controller.addOrders(selectedOrders);
      }
    } else {
      print('Got resultPartitionTable is null!');
    }
  }

  Widget _bottom() {
    return BottomWrapper(
      child: YesNoButton(
        leftButtonText: '取消',
        rightButtonText: '去結帳',
        rightColor: Colors.red,
        onLeftPressed: () => Get.back(),
        onRightPressed: _onCheckoutPressed,
      ),
    );
  }

  Future<void> _onCheckoutPressed() async {
    final it = controller.selectedOrders;
    if (it.isNotEmpty) {
      await Get.toNamed(
        Routes.ORDERS_SUM_UP,
        arguments: [...it],
        parameters: <String, String>{
          Keys.Tag: '${DateTime.now().millisecondsSinceEpoch}',
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      actions: _actions().toList(growable: false),
      titleText: '結帳',
      child: controller.obx((state) {
        return BottomWidgetPage(
          child: _children().column(),
          bottom: _bottom(),
        );
      }),
    );
  }

  Iterable<Widget> _children() sync* {
    yield ListWidget.header('選擇帳單');
    yield Obx(() => _list()).expanded();
  }

  Widget _list() {
    final it = controller.data;
    return ListView.separated(
      padding: const EdgeInsets.only(bottom: kBottomButtonPadding),
      physics: AlwaysScrollableScrollPhysics(),
      itemCount: it.length,
      itemBuilder: (context, index) {
        final order = it.elementAt(index);
        return OrderListItem(
          showActionButtons: false,
          data: order,
          showCheckBox: true,
          checked: true == controller.checkedList[order.id],
          onCheckChanged: (value) {
            controller.checkedList[order.id] = value;
          },
          onDetailPressed: () async {
            await Get.toNamed(
              Routes.ORDER_DETAIL,
              parameters: {
                Keys.Tag: '${order.id}',
                Keys.Id: '${order.id}',
              },
            );
          },
        );
      },
      separatorBuilder: (context, index) {
        return OrderListItem.divider();
      },
    );
  }
}
