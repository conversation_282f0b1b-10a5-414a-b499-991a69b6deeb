import 'dart:async';

import 'package:flutter/foundation.dart' show required, listEquals;
import 'package:get/get.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/models/qr_format.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/keys.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/app/models/products_get_qry.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

enum OrdersSetupMode {
  NewOrder,
  EditExisting,
}

class OrdersSetupArgs {
  OrdersSetupArgs({
    @required this.kind,
    this.mode = OrdersSetupMode.NewOrder,
    this.existOrdersPostReq,
  });

  //  類型
  // 0：餐飲店內
  // 1：餐飲線上 (這個在此處應該是不會有)
  // 2：零售
  // final int kind;
  num kind;
  //新增 or 編輯既有
  final OrdersSetupMode mode;
  //只對 mode 是編輯既有有意義
  final OrdersPostReq existOrdersPostReq;
}

class OrdersSetupController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final _disposable = Completer();
  final ProductProvider productProvider;
  final MemberProvider memberProvider;
  final OrderProvider orderProvider;
  final CouponProvider couponProvider;

  ApiProvider get apiProvider => productProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  // final Rx<TabController> tabController = Rx<TabController>(null);

  //菜單編輯 depend on 兩個 model.
  //1. CategoriesGetRes (分類列表)
  //2. ProductsGetRes (已有清單)
  //3. Partition 桌號清單
  // final Rx<CategoriesGetRes> categoriesGetRes = Rx<CategoriesGetRes>(null);
  // TODO: remove me
  // final isFetchingCategories = false.obs;
  // final Rx<ProductsGetRes> productsGetRes = Rx<ProductsGetRes>(null);
  // TODO: remove me
  // final isFetchingProducts = false.obs;
  // final Rx<TablesGetRes> tablesGetRes = Rx<TablesGetRes>(null);
  // final isFetchingTables = false.obs;
  final devtool = false.obs;
  final _cates = <Category>[].obs;
  final products = <ProductInfo>[].obs;
  final _filter = ProductsGetQry().obs;
  final _pagi = Pagination().obs;
  final index = 0.obs;
  num get more => _pagi.value.needToLoadMore ? 1 : 0;
  final _member = Rx<Member>(null);
  Member get member => _member.value;
  // set member(Member value) => _member.value = value;

  Iterable<Category> get cates {
    final list = _cates ?? <Category>[];
    list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    return list.where((element) => element.kind == args.kind);
  }

  List<ProductInfo> productWithCurrentCate() {
    if (index.value < _cates.length) {
      final cate = _cates.elementAt(index.value);
      return productWithCate(cate);
    }
    return <ProductInfo>[];
  }

  // 客制過濾及排序
  List<ProductInfo> productWithCate(Category cate) {
    final it = _productWithCate(cate).where((element) {
      // 售完
      if (element.isSoldOut) {
        return false;
      }
      // vip only 商品
      if ((Switcher.On.index == element.isVip) && (false == vip)) {
        return false;
      }
      return true;
    });
    final ls = List<ProductInfo>.from(it);
    ls.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    return ls;
  }

  Iterable<ProductInfo> _productWithCate(Category cate) {
    // return products.where((e) => e.categoryId == cate.id);
    return products.where((e) {
      // 特殊: 有可能 cate id 有對上，kind 卻對不上
      return e.categoryId == cate.id && cate.kind == e.kind;
    });
  }

  ///
  /// FIXME: 修正優惠券價格顯示
  ///
  String get displayTotal {
    final price = draft.itemsTotal.currencyStyle;
    final count = draft.itemsCount.decimalStyle;
    final coupon = (_coupon.value?.extraPrice ?? 0).currencyStyle;
    final couponText = draft.memberCouponId != null ? ' + $coupon(券)' : '';
    return '小計 $price($count)$couponText';
    // if (draft.memberCouponId != null) {
    // return '小計 $price($count) + $coupon(券)';
    // } else {}
  }

  String get titleText {
    switch (args.kind.productKind) {
      case ProductKind.DinnerApp:
      case ProductKind.DinnerLine:
        return '點餐';
      case ProductKind.Retail:
        return '選擇商品';
      default:
        return '';
    }
  }

  // Stream<Member> memberStream() => _member.stream.asBroadcastStream();
  // Stream<Member> memberStream() {
  //   return _draft.stream
  //       .asBroadcastStream()
  //       .where((event) => event?.memberId != null)
  //       .asyncMap((event) => memberProvider.getMember(event.memberId));
  // }

  ProductsGetQry get filter => _filter.value;

  //正在編輯當中準備要送出的 Orders 結構
  //因為數量選擇的新需求，OrderItem 編輯必須要用另外的結構處理
  final _draft = Rx<OrdersPostReq>(null);
  OrdersPostReq get draft => _draft.value;

  // final currentTabIndex = 0.obs;
  final _args = Rx<OrdersSetupArgs>(null);
  OrdersSetupArgs get args => _args.value;
  final _vip = false.obs;
  bool get vip => _vip.value;

  // bool get containsVip => _vip.value;
  // void toggleVip() => _vip.value = !containsVip;
  // 優惠券
  final _coupon = Rx<Coupon>(null);
  Coupon get coupon => _coupon.value;

  OrdersSetupController({
    @required this.productProvider,
    @required this.memberProvider,
    @required this.orderProvider,
    @required this.couponProvider,
  });

  void refreshVip() {
    _vip.refresh();
  }

  void toggleVip() {
    _vip.toggle();
  }

  @override
  void onInit() async {
    super.onInit();
    // 監聽分類變化
    // productProvider.cateCached.stream
    //     .debounce(100.milliseconds)
    //     .takeUntil(_disposable.future)
    //     .listen((event) {
    //   _loadFromLocalStorage();
    // });

    _vip.stream.asBroadcastStream().takeUntil(_disposable.future).listen(
      (event) {
        // 非 vip 則移除 vip 商品
        if (!event) {
          draft.items.removeWhere((element) {
            final p = productProvider.getProduct(element.productId);
            return p.isVip != null && p.isVip.switcher.isOn;
          });
        }
        // 重新計算價格
        draft.items.forEach((element) {
          final product = productProvider.getProduct(element.productId);
          final price = event ? product.vipPrice : product.price;
          element.calFinalPrice(price ?? 0);
        });
        // 更新
        _draft.refresh();
      },
    );

    _member.stream.asBroadcastStream().takeUntil(_disposable.future).listen(
      (event) {
        _vip.value = event?.isVip?.switcher?.isOn ?? false;
      },
    );

    final draftStream = _draft.stream.asBroadcastStream();

    draftStream
        .map((event) => event.memberCouponId)
        .distinct()
        .asyncMap((event) {
          if (event != null) {
            return couponProvider.getMemberCoupon(
                draft.memberId, draft.memberCouponId);
          } else {
            resetCoupon();
          }
        })
        .takeUntil(_disposable.future)
        .listen((event) {
          if (event == null || event.id == null) {
            resetCoupon();
          } else {
            _coupon.value = event;
            // FIXME: 計算優惠
          }
        });

    draftStream
        .map((event) => event.memberId)
        .takeUntil(_disposable.future)
        .listen(
      (event) {
        _member.value = memberProvider.getMemberFromLocalStorage(event);
      },
    );

    // _draft.stream.asBroadcastStream().takeUntil(_disposable.future).listen(
    //   (event) {
    //     // 非 vip 則移除 vip 商品
    //     // 重新計算價格
    //     event.items.forEach((element) {
    //       containsVip ? element.productId

    //       element.finalizePrice(productPrice)
    //       //
    //     });
    //   },
    // );

    _filter.stream
        .asBroadcastStream()
        .tap((event) {
          change('', status: RxStatus.loading());
        })
        .asyncMap((event) {
          if (productProvider.isEmptyWithProductKind(event.kind.productKind) ||
              productProvider.isEmpty) {
            return onRefresh();
          } else {
            return _loadFromLocalStorage();
          }
        })
        .takeUntil(_disposable.future)
        .listen(
          (event) {},
        );

    //Store the arguments.
    _args.value = Get.arguments;
    _filter.value.kind = args.kind;
    //我們需要設定初始值嗎? (注意這邊 items 一定要給空值)
    //如果參數有帶現有的進來，就編輯現有的，沒有的話就做一個新的
    // if (args.existOrdersPostReq != null) {
    //   //這邊一定是店內所以 source 設為 0
    //   _draft.value = args.existOrdersPostReq;
    // } else {
    //   //這邊一定是店內所以 source 設為 0
    //   _draft.value = OrdersPostReq(items: [], source: 0);
    // }
    _draft.value = args?.existOrdersPostReq ??
        OrdersPostReq(
          items: [],
          source: 0,
          invoice: prefProvider.invoiceEnabled,
        );

    //取得設定桌號清單.
    // reFetchTables();

    //這邊要等確認有幾個 Categories 之後才初始化 tabController.
    // await reFetchCategories();

    // //看來得先搞定這個動態 tab 東西
    // tabController.value =
    //     TabController(vsync: this, length: categoriesLength());

    // await reFetchProducts();
  }

  ///
  /// 清除優惠券
  ///
  void resetCoupon() {
    draft.memberCouponId = null;
    draft.memberCouponDiscount = null;
    draft.memberCouponExtraPrice = null;
    _coupon.value = null;
    refreshDraft();
  }

  @override
  void onReady() {
    super.onReady();
    _filter.refresh();
    if (Get.parameters.containsKey(Keys.Data)) {
      final qr = QrFormat.fromRawJson(Get.parameters[Keys.Data]);
      draft.memberId = qr.memberId;
      draft.memberCouponId = qr.memberCouponId;
      _draft.refresh();
    }
  }

  Future<void> _loadFromLocalStorage() async {
    _cates.clear();
    _cates.addAll(
        productProvider.getCategoriesFromStorage(args.kind.productKind));
    _cates.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    products.clear();
    products.addAll(productProvider.getProductsFromStorage());
    _pagi.value.currentPage = 1;
    _pagi.value.lastPage = _pagi.value.currentPage;
    _refreshPage();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  //--

  // int categoriesLength() {
  //   if (categoriesGetRes.value != null) {
  //     // print('categoriesLength: ' + categoriesGetRes.value.data.length.toString());
  //     return categoriesGetRes.value.data.length;
  //   }
  //   // print('categoriesLength: 0');
  //   return 0;
  // }

  // onTabTap(int tabIndex) async {
  //   //這會讓顯示頁面切換
  //   currentTabIndex.value = tabIndex;
  // }

  //過濾取得當前 tab 內要顯示的 Product info
  // List<ProductInfo> productInfoInCurrentTab() {
  //   if (currentTabIndex.value != null) {
  //     return productInfoInTab(currentTabIndex.value);
  //   }
  //   return [];
  // }

  //取得某個 tab 內的商品列表
  // List<ProductInfo> productInfoInTab(int tabIndex) {
  //   List<ProductInfo> returnList = [];

  //   if (productsGetRes.value != null &&
  //       productsGetRes.value.data != null &&
  //       categoriesGetRes.value != null &&
  //       tabIndex != null) {
  //     if (tabIndex < categoriesGetRes.value.data.length) {
  //       int currentTabCatId = categoriesGetRes.value.data[tabIndex].id;
  //       returnList.addAll(productsGetRes.value
  //           .getProductInfosInTabWithStock(currentTabCatId));
  //     }
  //   }

  //   return returnList;
  // }

  //取得當前選取桌面按鈕上的顯示文字
  // String selectedTableDisplayName() {
  //   String returnStr = '桌號';
  //   if (_draft.value != null &&
  //       _draft.value.table1Id != null &&
  //       _draft.value.table2Id != null &&
  //       tablesGetRes.value != null) {
  //     return tablesGetRes.value
  //         .getDisplayName(_draft.value.table1Id, _draft.value.table2Id);
  //   }
  //   //在無資料或者沒有選擇好的狀況下自然只會回預設值。
  //   return returnStr;
  // }

  //是否已經選擇了合法的桌號
  // bool hasSelectTable() {
  //   if (_draft.value != null) {
  //     return _draft.value.hasSelectLegalTable();
  //   }
  //   return false;
  // }

  //嘗試新增商品。
  void addNewOrderItem(OrderItem orderItem) {
    //Prevent from 0 quantity.
    if (orderItem != null &&
        orderItem.quantity is num &&
        orderItem.quantity > 0) {
      // 相同規格商品疊加數量
      final equalItems = draft.items.where((element) {
        if (element.productId != orderItem.productId) {
          return false;
        }
        if (!listEquals(element.productSpec1Ids, orderItem.productSpec1Ids)) {
          return false;
        }
        return true;
      });
      if (equalItems.length == 1) {
        equalItems.forEach((element) {
          element.quantity += orderItem.quantity;
          element.finalPrice += orderItem.finalPrice;
        });
      } else {
        draft.items.add(orderItem);
      }
      _draft.refresh();
      // print('addNewOrderItem: [' + orderItem.productName + '][' + orderItem.id.toString() + '][' + count.toString() + '] count after added [' + orderItemsCart.value.length.toString() + ']');
      // index.refresh();
    }
  }

  ///
  /// 取得商品下單數量
  ///
  num getProductCount(num productId) {
    final items = draft?.items ?? <OrderItem>[];
    return items.where((element) => element.productId == productId).fold(
          0,
          (previousValue, element) => previousValue + (element.quantity ?? 0),
        );
  }

  ///
  /// 取得某分類內的已選取商品數量。
  ///
  num getCategoryProductCount(Category category) {
    return products.where((e) => e.categoryId == category.id).fold<num>(
      0,
      (previousValue, element) {
        return previousValue + getProductCount(element.productId);
      },
    );
  }

  // 取得所有商品總和數量
  num get allProductCount => draft?.itemsCount ?? 0;

  // 計算當前商品總金額
  String get totalPrice => (draft?.itemsTotal ?? 0).decimalStyle;

  ///
  /// 取得最新分類
  ///
  Future<void> _fetchCategories() async {
    try {
      final it = await productProvider.getCategories(args.kind.productKind);
      _cates.clear();
      _cates.addAll(it);
      _cates.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    } catch (e) {
      throw e;
    }
  }

  ///
  /// 取得最新商品
  ///
  Future<void> _fetchProducts([num page = 1]) async {
    try {
      filter.page = page ?? 1;
      final it = await productProvider.getProducts(filter);
      _pagi.value.currentPage = page;
      _pagi.value.lastPage = null;
      if (1 == page) {
        products.clear();
      }
      products.addAll(it);
    } catch (e) {
      _pagi.value.lastPage = _pagi.value.currentPage;
      throw e;
    }
  }

  ///
  /// 重新整理
  ///
  Future<void> onRefresh() async {
    try {
      await _fetchCategories();
      await _fetchProducts();
      _refreshPage();
    } catch (e) {
      // 首讀可顯示錯誤頁面
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  Future<void> onEndScroll() async {
    if (_pagi.value.needToLoadMore) {
      try {
        await _fetchProducts(_pagi.value.nextPage);
      } catch (e) {
        // end of page, prevent error message
        logger.e(e);
      } finally {
        _refreshPage();
      }
    }
  }

  @override
  Future<void> onTopScroll() async {
    // nothing to do
  }

  ///
  /// 更新頁面
  ///
  void _refreshPage() {
    // 特殊: cache 有東西，設定成功，觸發 onEndScroll 繼續讀取
    final isEmpty =
        _pagi.value.needToLoadMore ? productProvider.isEmpty : products.isEmpty;
    logger.d('[OrdersSetupController] _refreshPage isEmpty($isEmpty)');
    change('', status: isEmpty ? RxStatus.empty() : RxStatus.success());
  }

  void refreshDraft() {
    _draft.refresh();
  }

  bool get hasMultipleStoreType {
    return prefProvider.setting.checkoutType.storeTypeCount > 1;
  }

  void toggleProductType() {
    if (args.kind.productKind.isDinnerApp) {
      if (prefProvider.setting.checkoutType.containsRetail) {
        args.kind = ProductKind.Retail.index;
        _args.refresh();
      }
    } else if (args.kind.productKind.isRetail) {
      if (prefProvider.setting.checkoutType.containsDinner) {
        args.kind = ProductKind.DinnerApp.index;
        _args.refresh();
      }
    }
    prefProvider.toggleStoreType();
  }
}
