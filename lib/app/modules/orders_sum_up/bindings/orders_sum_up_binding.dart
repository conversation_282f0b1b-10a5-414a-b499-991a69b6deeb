import 'package:get/get.dart';

import '../controllers/orders_sum_up_controller.dart';

class OrdersSumUpBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<OrdersSumUpController>(
      () => OrdersSumUpController(
        productProvider: Get.find(),
        memberProvider: Get.find(),
        invoiceProvider: Get.find(),
        orderProvider: Get.find(),
        couponProvider: Get.find(),
        printerProvider: Get.find(),
        settingProvider: Get.find(),
        linePayProvider: Get.find(),
        localInvoiceProvider: Get.find(),
      ),
    );
  }
}
