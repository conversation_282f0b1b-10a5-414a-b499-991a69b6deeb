import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:okshop_model/okshop_model.dart';

class PasswordController extends GetxController {
  final ApiProvider apiProvider;
  final _draft = PasswordReset().obs;
  PasswordReset get draft => _draft.value;

  PasswordController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {}

  Future<bool> submit() async {
    try {
      if (draft.validate()) {
        final ret = await apiProvider.passwordReset(draft);
        return ret is num && ret > 0;
      }
      return false;
    } catch (e) {
      rethrow;
    }
  }
}

extension _ExtPasswordReset on PasswordReset {
  bool validate() {
    if (oldPassword == null || oldPassword.isEmpty) {
      throw '舊密碼是必填項目';
    }
    if (newPassword == null || newPassword.isEmpty) {
      throw '新密碼是必填項目';
    }
    if (checkPassword == null || checkPassword.isEmpty) {
      throw '再次輸入新密碼是必填項目';
    }
    if (newPassword != checkPassword) {
      throw '請輸入相同的新密碼';
    }
    return true;
  }
}
