import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/payment_bank.dart';
import 'package:muyipork/app/models/payment_bank_put.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/extension.dart';

class PaymentBankController extends GetxController
    with StateMixin<PaymentBank> {
  final ApiProvider apiProvider;
  final _disposable = Completer();
  final _data = Rx<PaymentBankPut>(null);

  PaymentBankPut get data => this._data.value;

  PaymentBankController({
    @required this.apiProvider,
  });

  void refreshData() => this._data.refresh();

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    final f = () {
      return this.apiProvider.getPaymentBank().then(
        (value) {
          _data.value = value.toPaymentBankPut();
          return value;
        },
      );
    };
    super.append(() => f);
  }

  @override
  void onClose() {
    this._disposable.complete();
  }

  Future<bool> submit() {
    return this.apiProvider.putPaymentBank(this.data);
  }
}
