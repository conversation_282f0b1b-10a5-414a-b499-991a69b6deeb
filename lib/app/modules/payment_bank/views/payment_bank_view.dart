import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/save_page.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/payment_bank_controller.dart';

class PaymentBankView extends GetView<PaymentBankController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.focusScope.unfocus();
      },
      child: StandardPage(
        titleText: Get.parameters['name'],
        child: controller.obx((state) {
          return SavePage(
            onPressed: this._submit,
            child: Obx(() => _main()),
          );
        }),
      ),
    );
  }

  void _submit() {
    FutureBuilder(
      future: controller.submit(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          Get.back(result: snapshot.data);
          return SizedBox.shrink();
        }
        if (snapshot.hasError) {
          return DialogGeneral(
            DialogArgs(
              contentIcon: DialogContentIcon.Error,
              content: Text('${snapshot.error}'),
              mainButtonText: '確定',
            ),
          );
        }
        return Center(
          child: CircularProgressIndicator(),
        );
      },
    )
        .dialog(
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          Get.back();
        }
      },
    );
  }

  Widget _main() {
    final children = <Widget>[
      const SizedBox(
        height: 8.0,
      ),
      SettingsWidget.title(
        titleText: '零售商店',
      ),
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，${Get.parameters['name']}',
        value: controller.data.status != 0,
        onChanged: (value) {
          controller.data.status = value ? 1 : 0;
          controller.refreshData();
        },
      ),
    ];
    children.addAllIf(
      controller.data.status != 0,
      [
        SettingsWidget.input(
          labelText: '銀行',
          initialValue: controller.data.name,
          onChanged: (value) => controller.data.name = value,
        ),
        SettingsWidget.input(
          labelText: '分行',
          initialValue: controller.data.branch,
          onChanged: (value) => controller.data.branch = value,
        ),
        SettingsWidget.input(
          labelText: '帳戶',
          initialValue: controller.data.accountName,
          onChanged: (value) => controller.data.accountName = value,
        ),
        SettingsWidget.input(
          labelText: '帳號',
          initialValue: controller.data.account,
          onChanged: (value) => controller.data.account = value,
        ),
        const Divider(
          thickness: 16.0,
          height: 16.0,
          color: Colors.white,
        ),
        ColoredBox(
          color: Colors.white,
          child: SettingsWidget.comment(
            titleText: '店家備註',
            hintText: '請輸入200字內文字',
            initialValue: controller.data.description,
            onChanged: (value) => controller.data.description = value,
          ),
        ),
        const SizedBox(
          height: kBottomButtonPadding,
        ),
      ],
    );
    return ListView(
      children: children,
    );
  }
}
