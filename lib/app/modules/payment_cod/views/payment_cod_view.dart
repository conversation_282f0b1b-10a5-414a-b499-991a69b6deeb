import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/save_page.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/payment_cod_controller.dart';

class PaymentCodView extends GetView<PaymentCodController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.focusScope.unfocus(),
      child: StandardPage(
        titleText: Get.parameters['name'],
        child: controller.obx(
          (state) {
            return SavePage(
              onPressed: _submit,
              child: RefreshIndicator(
                onRefresh: controller.onRefresh,
                child: _main(),
              ),
            );
          },
          onError: ListWidget.message,
        ),
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, const SizedBox(height: 8.0));
    children.addIf(false, Obx(() => _dinner()));
    children.addIf(true, Obx(() => _retail()));
    children.addIf(true, const SizedBox(height: kBottomButtonPadding));
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: children,
    );
  }

  void _submit() {
    FutureProgress(future: controller.submit()).dialog().then(
      (value) {
        if (value != null) {
          Get.back();
        }
      },
    );
  }

  Widget _dinner() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '餐飲商店',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，${Get.parameters['name']}',
        // value: controller.data.status != 0,
        // onChanged: (value) {
        //   controller.data.status = value ? 1 : 0;
        //   controller.refreshData();
        // },
      ),
    );
    children.addIf(
      true,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(
          left: kPadding + kInsetSize,
          right: kPadding,
          // bottom: 4,
        ),
        labelText: '貨到付款金流手續費',
        hintText: '請輸入數字',
        // initialValue: '${controller.data.fee.toInt()}',
        // onChanged: (value) => controller.data.fee = num.tryParse(value) ?? 0,
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
      ),
    );
    children.addIf(
      true,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(
          left: kPadding + kInsetSize,
          right: kPadding,
        ),
        labelText: '貨到付款免金流手續費門檻金額',
        hintText: '請輸入數字',
        // initialValue: '${controller.data.free.toInt()}',
        // onChanged: (value) => controller.data.free = num.tryParse(value) ?? 0,
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
      ),
    );
    children.addIf(
      controller.data.status.switcher.isOn ?? false,
      SettingsWidget.space(),
    );
    children.addIf(
      true,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          contentPadding: EdgeInsets.only(
            left: kPadding + kInsetSize,
            right: kPadding,
          ),
          titleText: '店家備註',
          hintText: '請輸入200字內文字',
          initialValue: '',
          // FIXME:
          // onChanged: controller.description,
        ),
      ),
    );
    children.addIf(
      controller.data.status.switcher.isOn ?? false,
      SettingsWidget.space(),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _retail() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '零售商店',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，${Get.parameters['name']}',
        value: controller.data.status != 0,
        onChanged: (value) {
          controller.data.status = value ? 1 : 0;
          controller.refreshData();
        },
      ),
    );
    children.addIf(
      controller.data.status.switcher.isOn ?? false,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(
          left: kPadding + kInsetSize,
          right: kPadding,
          // bottom: 4,
        ),
        labelText: '貨到付款金流手續費',
        hintText: '請輸入數字',
        initialValue: '${controller.data.fee.toInt()}',
        onChanged: (value) {
          controller.data.fee = num.tryParse(value);
        },
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
      ),
    );
    children.addIf(
      controller.data.status.switcher.isOn ?? false,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(
          left: kPadding + kInsetSize,
          right: kPadding,
        ),
        labelText: '貨到付款免金流手續費門檻金額',
        hintText: '請輸入數字',
        initialValue: '${controller.data.free.toInt()}',
        onChanged: (value) => controller.data.free = num.tryParse(value) ?? 0,
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
      ),
    );
    children.addIf(
      controller.data.status.switcher.isOn ?? false,
      SettingsWidget.space(),
    );
    children.addIf(
      controller.data.status.switcher.isOn ?? false,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          contentPadding: EdgeInsets.only(
            left: kPadding + kInsetSize,
            right: kPadding,
          ),
          titleText: '店家備註',
          hintText: '請輸入200字內文字',
          initialValue: controller.data.description,
          onChanged: (value) => controller.data.description = value,
        ),
      ),
    );
    children.addIf(
      controller.data.status.switcher.isOn ?? false,
      SettingsWidget.space(),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
