import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/payment_instore.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';
import 'package:okshop_common/okshop_common.dart';

class PaymentInstoreController extends GetxController with StateMixin<String> {
  final SettingProvider settingProvider;
  ApiProvider get apiProvider => settingProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  // final dinerDesc = ''.obs;
  // final dinerEnabled = false.obs;
  final _disposable = Completer();
  final _data = Rx<PaymentInstore>(null);

  PaymentInstore get data => this._data.value;

  // void refreshData() => this._data.refresh();

  PaymentInstoreController({
    @required this.settingProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  Future<void> onRefresh() async {
    try {
      _data.value =
          await settingProvider.getPayment(AppPayMethod.CashAtStore.index);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<bool> submit() {
    return apiProvider.putPaymentInstore(this.data);
  }

  void refreshDraft() {
    _data.refresh();
  }
}
