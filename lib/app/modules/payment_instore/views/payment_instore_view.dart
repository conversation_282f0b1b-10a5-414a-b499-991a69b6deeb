import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/payment_instore_controller.dart';

class PaymentInstoreView extends GetView<PaymentInstoreController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.focusScope.unfocus();
      },
      child: StandardPage(
        titleText: Get.parameters['name'],
        child: controller.obx(
          (state) {
            return BottomWidgetPage.save(
              child: Obx(() => _main()),
              onPressed: _submit,
            );
          },
        ),
      ),
    );
  }

  Future<void> _submit() async {
    try {
      final ret = await FutureProgress(
        future: controller.submit(),
      ).dialog();
      if (true == ret) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      controller.prefProvider.brandsType.containsDinner,
      _dinner(),
    );
    children.addIf(
      controller.prefProvider.brandsType.containsRetail,
      _retail(),
    );
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding, top: 8),
      children: children,
    );
  }

  Widget _dinner() {
    final children = <Widget>[];
    children.addIf(true, SettingsWidget.title(titleText: '餐飲商店'));
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，${Get.parameters['name']}',
        value: Switcher.On.index == controller.data.statusDiner,
        onChanged: (value) {
          controller.data.statusDiner = value.switcher.index;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      Switcher.On.index == controller.data.statusDiner,
      const Divider(
        height: kPadding,
        thickness: kPadding,
        color: Colors.white,
      ),
    );
    children.addIf(
      Switcher.On.index == controller.data.statusDiner,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          titleText: '店家備註',
          hintText: '請輸入200字內文字',
          initialValue: controller.data.descriptionDiner,
          onChanged: (value) {
            controller.data.descriptionDiner = value;
          },
        ),
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _retail() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '零售商店',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，${Get.parameters['name']}',
        value: controller.data.status != 0,
        onChanged: (value) {
          controller.data.status = value ? 1 : 0;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      Switcher.On.index == controller.data.status,
      Divider(
        height: 20.0,
        thickness: 20.0,
        color: Colors.white,
      ),
    );
    children.addIf(
      Switcher.On.index == controller.data.status,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          titleText: '店家備註',
          hintText: '請輸入200字內文字',
          initialValue: controller.data.description,
          onChanged: (value) => controller.data.description = value,
        ),
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
