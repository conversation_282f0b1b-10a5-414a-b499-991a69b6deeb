import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/payment_line_pay_controller.dart';

class PaymentLinePayView extends GetView<PaymentLinePayController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: 'LINE Pay 付款',
      child: controller.obx((state) {
        return BottomWidgetPage.save(
          child: Obx(() => _main()),
          onPressed: _submit,
        );
      }),
    );
  }

  Future<void> _submit() async {
    final ret = await FutureProgress<bool>(
      future: controller.submit(),
    ).dialog();
    if (ret == true) {
      Get.back();
    }
  }

  Widget _main() {
    return ListView(
      children: _list(),
    );
  }

  Iterable<Widget> _list() {
    final children = <Widget>[];
    children.addIf(
      true,
      Align(
        alignment: Alignment.center,
        child: SettingsWidget.title(titleText: controller.titleText),
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，LINE Pay 付款',
        value: controller.switcher.isOn,
        onChanged: (value) {
          controller.switcher = value.switcher;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      controller.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2),
        initialValue: controller.linePaySetting.channelId,
        labelText: 'Channel ID',
        hintText: '請輸入Channel ID',
        onChanged: (value) {
          controller.linePaySetting.channelId = value;
        },
      ),
    );
    children.addIf(
      controller.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2),
        initialValue: controller.linePaySetting.channelSecretKey,
        labelText: 'Channel Secret Key',
        hintText: '請輸入Channel Secret Key',
        onChanged: (value) {
          controller.linePaySetting.channelSecretKey = value;
        },
      ),
    );
    children.addIf(true, SettingsWidget.space());
    children.addIf(
      controller.switcher.isOn,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          titleText: '店家備註',
          initialValue: controller.description,
          onChanged: (value) {
            controller.description = value;
          },
        ),
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用POS掃描，LINE Pay 付款',
        value: controller.linePayOffline.isOn,
        onChanged: (value) {
          controller.linePayOffline = value.switcher;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      controller.linePayOffline.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2),
        initialValue: controller.linePaySetting.offlineChannelId,
        labelText: 'Channel ID',
        hintText: '請輸入Channel ID',
        onChanged: (value) {
          controller.linePaySetting.offlineChannelId = value;
        },
      ),
    );
    children.addIf(
      controller.linePayOffline.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2),
        initialValue: controller.linePaySetting.offlineChannelSecretKey,
        labelText: 'Channel Secret Key',
        hintText: '請輸入Channel Secret Key',
        onChanged: (value) {
          controller.linePaySetting.offlineChannelSecretKey = value;
        },
      ),
    );
    return children;
  }
}
