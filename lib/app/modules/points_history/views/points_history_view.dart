import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/points_history_controller.dart';

class PointsHistoryView extends GetView<PointsHistoryController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '積點紀錄',
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _header(),
          Expanded(child: _body()),
        ],
      ),
    );
  }

  Widget _header() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(width: kPadding);
      yield SizedBox.fromSize(
        size: const Size.square(20),
        child: const DecoratedBox(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: OKColor.Gray66,
          ),
          child: Text(
            'P',
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
      yield const SizedBox(width: 4);
      yield const Expanded(
        child: Text(
          '現有積點',
          style: TextStyle(
            fontSize: 14,
            color: OKColor.Gray66,
          ),
        ),
      );
      yield Obx(
        () => Text.rich(
          TextSpan(
            style: const TextStyle(
              fontSize: 26,
              color: OKColor.Primary,
            ),
            children: [
              TextSpan(
                // text: '988',
                text: controller.total?.decimalStyle ?? '-',
                style: TextStyle(
                  fontSize: 26,
                  color: OKColor.Primary,
                  // fontWeight: FontWeight.w700,
                ),
              ),
              const TextSpan(
                text: ' 點',
                style: TextStyle(
                  fontSize: 14,
                  color: OKColor.Gray66,
                  // fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
        ),
      );
      yield const SizedBox(width: kPadding);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    return ColoredBox(
      color: Colors.white,
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: controller.obx(
          (state) => Obx(() => _list()),
          onError: ListWidget.message,
          onEmpty: ListWidget.message('沒有資料'),
        ),
      ),
    );
  }

  Widget _list() {
    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: controller.scroll,
      padding: const EdgeInsets.symmetric(
        vertical: 12,
        horizontal: kPadding,
      ),
      itemCount: controller.data.length + (controller.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < controller.data.length) {
          return _Item(data: controller.data.elementAt(index));
        }
        return ListWidget.bottomProgressing();
      },
      separatorBuilder: (context, index) {
        return const Divider();
      },
    );
  }
}

class _Item extends StatelessWidget {
  final MemberPoint data;

  const _Item({
    Key key,
    this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Row(
      children: [
        Expanded(
          child: Text(
            // '店家扣點',
            data?.comment ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Text(
          // '扣除積點',
          data?.type?.pointType?.name ?? '',
          style: const TextStyle(
            fontSize: 14,
            color: OKColor.Gray66,
          ),
          textAlign: TextAlign.right,
        ),
      ],
    );
    yield const SizedBox(height: 4);
    yield Row(
      children: [
        Expanded(
          child: Text(
            // '2021-08-17 12:32',
            data?.createdAt?.localAt?.yMdHms ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: OKColor.Gray33,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Text(
          // '-28',
          data?.points?.decimalStyle ?? '',
          style: TextStyle(
            fontSize: 16,
            // color: OKColor.Error,
            color: data.color,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.right,
        ),
      ],
    );
    if (data.storeAccount != null && data.storeAccount.isNotEmpty) {
      yield const SizedBox(height: 2);
      yield Text(
        // '處理者：xx店長',
        '處理者：${data.storeAccount ?? ''}',
        style: const TextStyle(
          fontSize: 14,
          color: OKColor.Primary,
        ),
        textAlign: TextAlign.left,
      );
    }
  }
}

extension _MemberPointX on MemberPoint {
  Color get color => points < 0 ? OKColor.Error : Colors.black;
}
