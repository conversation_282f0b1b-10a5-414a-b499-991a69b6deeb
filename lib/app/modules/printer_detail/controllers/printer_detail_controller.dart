import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';

class PrinterDetailController extends GetxController with StateMixin<String> {
  final PrefProvider prefProvider;
  final PrinterProvider printerProvider;
  final ProductProvider productProvider;

  final _draft = Rx<SettingLabel>(null);
  SettingLabel get draft => _draft.value;

  final dinnerApp = <Category>[].obs;
  final dinnerLine = <Category>[].obs;
  final retail = <Category>[].obs;

  final _containsCloud = false.obs;
  bool get containsCloud => _containsCloud.value;
  bool get normalPrinter => !containsCloud;
  String get displayPrintCount {
    draft.printCount ??= 1;
    return '${draft.printCount}張';
  }

  PrinterDetailController({
    @required this.prefProvider,
    @required this.printerProvider,
    @required this.productProvider,
  });

  @override
  void onInit() {
    super.onInit();
    final switcher =
        num.tryParse(Get.parameters['cloud'] ?? '') ?? Switcher.Off.index;
    _containsCloud.value = switcher.switcher.isOn;
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {}

  void refreshDraft() {
    _draft.refresh();
  }

  Future<bool> submit() {
    if (draft.id is num && draft.id > 0) {
      // 更新
      final asyncFunction = containsCloud
          ? printerProvider.putSettingPrinters
          : printerProvider.putSettingLabels;
      return asyncFunction(draft);
    } else {
      // 新增
      final asyncFunction = containsCloud
          ? printerProvider.postSettingPrinters
          : printerProvider.postSettingLabels;
      return asyncFunction(draft);
    }
  }

  Future<bool> delete() {
    if (draft.id is num && draft.id > 0) {
      // 刪除遠端
      final asyncFunction = containsCloud
          ? printerProvider.deleteSettingPrinters
          : printerProvider.deleteSettingLabels;
      return asyncFunction(draft);
    }
    // 刪除本地
    return Future.value(true);
  }

  Future<void> onRefresh() async {
    try {
      if (Get.parameters.containsKey(Keys.Data)) {
        _draft.value = SettingLabel.fromRawJson(Get.parameters[Keys.Data]);
      } else {
        throw 'no data';
      }
      dinnerApp.assignAll(
          productProvider.getCategoriesFromStorage(ProductKind.DinnerApp));
      dinnerLine.assignAll(
          productProvider.getCategoriesFromStorage(ProductKind.DinnerLine));
      retail.assignAll(
          productProvider.getCategoriesFromStorage(ProductKind.Retail));
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }
}

// extension _ExtensionNum on num {
//   PaperSize get paperSize {
//     if (PaperSize.mm80.value == this) {
//       return PaperSize.mm80;
//     }
//     return PaperSize.mm58;
//   }
// }
