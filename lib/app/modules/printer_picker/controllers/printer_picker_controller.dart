import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_godex_printer/flutter_godex_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:okshop_model/okshop_model.dart';

class PrinterPickerController extends GetxController with StateMixin<String> {
  final PrinterProvider printerProvider;
  final _printerRepository = PrinterRepository();
  final data = <SettingLabel>[].obs;
  final draft = <num>{}.obs;
  final _ids = <num>[].obs;
  final _godex = 0.obs;
  final _title = '搜尋印表機'.obs;
  bool get isGodex => Switcher.On.index == _godex.value;
  String get title => _title.value;

  PrinterPickerController({
    @required this.printerProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.Data)) {
      final it = jsonDecode(Get.parameters[Keys.Data]);
      _ids.assignAll(List.castFrom<dynamic, num>(it));
    }
    if (Get.parameters.containsKey('godex')) {
      _godex.value = int.tryParse(Get.parameters['godex']) ?? Switcher.On.index;
    }
    if (isGodex) {
      _title.value = '搜尋標籤機';
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {}

  Future<void> onRefresh() async {
    try {
      // final it = await _printerRepository.scan([PrinterType.net]);
      final callable = isGodex ? _scanGodex : _scanSunmi;
      final it = await callable.call();
      _onPrinterDiscovered(it);
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<Iterable<SettingLabel>> _scanSunmi() {
    return _printerRepository.scan([PrinterType.net]);
  }

  Future<Iterable<SettingLabel>> _scanGodex() async {
    final it = await GodexUtil.wifiPrinterSearch();
    if (it.isNotEmpty) {
      return it.map((e) => e.asSettingLabel());
    }
    final stream = GodexUtil.discover();
    final ls = await stream.toList();
    return ls
        .where((element) => element.exists)
        .map((element) => element.toGodexPrinter().asSettingLabel());
  }

  void _onPrinterDiscovered(Iterable<SettingLabel> it) {
    final ls = it.where((printer) {
      return _ids.every((element) => element != printer.uuid);
    });
    data.assignAll(ls);
    refreshStatus();
  }

  Iterable<SettingLabel> get checked {
    return data.where((printer) {
      return draft.any((element) => element == printer.uuid);
    });
  }

  void refreshStatus() {
    final status = data.isNotEmpty ? RxStatus.success() : RxStatus.empty();
    change('', status: status);
  }
}

extension _ExtensionGodexPrinter on GodexPrinter {
  SettingLabel asSettingLabel() {
    return SettingLabel(
      categoryIds: <num>[],
      id: 0,
      ip: '$ip:$port',
      macAddress: macAddress,
      name: name,
      printCount: 1,
      status: Switcher.On.index,
    );
  }
}
