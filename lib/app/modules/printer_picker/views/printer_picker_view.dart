import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';

import '../controllers/printer_picker_controller.dart';

class PrinterPickerView extends GetView<PrinterPickerController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      // titleText: '搜尋印表機',
      titleText: controller.title,
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: controller.obx(
          (state) {
            return BottomWidgetPage(
              bottom: BottomWrapper(
                child: YesNoButton(
                  rightButtonText: '加入',
                  leftButtonText: '手動輸入',
                  onLeftPressed: _onManualPressed,
                  onRightPressed: _submit,
                ),
              ),
              // buttonText: '加入',
              // onPressed: _submit,
              child: _main(),
            );
          },
          onEmpty: ListWidget.blank(
            buttonText: '手動輸入',
            onPressed: _onManualPressed,
          ),
          onError: ListWidget.message,
        ),
      ),
    );
  }

  Future<void> _onManualPressed() async {
    final editing = TextEditingController();
    final ret = await Get.defaultDialog<String>(
      title: 'IP 位置',
      content: SettingsWidget.input(
        // hintText: '192.168.1...',
        controller: editing,
        // onChanged: (value) {},
      ),
      onCancel: () {},
      onConfirm: () {
        Get.back(result: editing.text);
      },
    );
    if (ret != null && ret.isNotEmpty) {
      final printer = SettingLabel(
        categoryIds: <num>[],
        id: 0,
        ip: '$ret:9100',
        macAddress: controller.isGodex ? '' : 'esc@$ret:9100',
        name: ret,
        printCount: 1,
        status: Switcher.On.index,
      );
      final jsonString = jsonEncode([printer]);
      Get.back(result: jsonString);
    }
  }

  void _submit() {
    final jsonString = jsonEncode(controller.checked.toList());
    Get.back(result: jsonString);
  }

  Widget _main() {
    final list = controller.data;
    return ListView.separated(
      padding: EdgeInsets.only(
        top: kPadding,
        bottom: kBottomButtonPadding,
      ),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: list.length,
      itemBuilder: (context, index) {
        final element = list.elementAt(index);
        return Obx(() {
          return CheckboxListTile(
            value: controller.draft.contains(element.uuid),
            tileColor: Colors.white,
            title: Text(element.name ?? ''),
            subtitle: Text(element.ip ?? ''),
            // subtitle: Text(element.subtitle ?? ''),
            onChanged: (value) {
              if (value) {
                controller.draft.add(element.uuid);
              } else {
                controller.draft.remove(element.uuid);
              }
            },
          );
        });
      },
      separatorBuilder: (context, index) => Divider(),
    );
  }
}
