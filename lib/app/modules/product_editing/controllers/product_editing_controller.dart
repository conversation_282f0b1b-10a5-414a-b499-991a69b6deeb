import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:flutter/widgets.dart' show TextEditingController;
import 'package:get/get.dart';
import 'package:muyipork/app/models/image_model.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/models/brands_info.dart';
import 'package:muyipork/app/models/products_post_req.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';

class ProductEditingArgs {
  ProductEditingArgs({
    this.defaultCategoryId,
    this.productId,
    @required this.kind,
  });
  // 新產品的預設分類
  final num defaultCategoryId;
  // 繼續編輯已有的產品
  final num productId;
  // 設定 API 使用類型
  // 0: 店內餐飲
  // 1: 線上餐飲
  // 2: 零售商品
  final num kind;
}

extension ExtProductEditingArgs on ProductEditingArgs {
  ProductKind get productKind => (kind ?? ProductKind.Max.index).productKind;
}

extension ExtProductKind on ProductKind {
  String get title {
    switch (this) {
      case ProductKind.DinnerApp:
        return '店內餐點';
      case ProductKind.DinnerLine:
        return '線上餐點';
      case ProductKind.Retail:
        return '商品';
      default:
        return '';
    }
  }

  num get summaryLengthLimit {
    switch (this) {
      case ProductKind.DinnerApp:
      case ProductKind.DinnerLine:
        return 1000;
      case ProductKind.Retail:
        return 1000;
      default:
        return 1000;
    }
  }

  String get summaryTitle {
    switch (this) {
      case ProductKind.DinnerApp:
      case ProductKind.DinnerLine:
        return '主菜簡介';
      case ProductKind.Retail:
        return '商品簡介';
      default:
        return '';
    }
  }

  String get summaryHint {
    switch (this) {
      case ProductKind.DinnerApp:
      case ProductKind.DinnerLine:
        return '請輸入1000字內簡介';
      case ProductKind.Retail:
        return '請輸入1000字內簡介';
      default:
        return '';
    }
  }
}

class ProductEditingController extends GetxController with StateMixin<String> {
  static const MAX_TILE = 10;
  final _disposable = Completer();
  final ProductProvider productProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BrandsInfo get brandsInfo => prefProvider.brandsInfo;
  // 主要編輯 Model: 編輯跟送出都是這個東西
  final _draft = Rx<ProductsPostReq>(null);
  ProductsPostReq get draft => _draft.value;
  ProductEditingArgs args;
  final _id = RxNum(0);
  num get id => _id.value;
  final imageEditing = false.obs;
  final tiles = <ImageModel>[].obs;
  final imagesToRemove = <ImageModel>[].obs;
  final regexp = RegExp(r'^https?://');
  // 庫存量編輯控制器
  final stockEditingController = TextEditingController();
  // 名稱
  final titleEditingController = TextEditingController();
  // 價格
  final priceEditingController = TextEditingController();
  // VIP價格
  final vipPriceEditingController = TextEditingController();

  Iterable<Category> get categories {
    return productProvider.getCategoriesFromStorage(args.productKind);
  }

  Iterable<Category> get additionCategories {
    return productProvider.getAdditionCategoriesFromStorage(args.productKind);
  }

  String get _shopUrl {
    final uri = Uri.parse(brandsInfo.okshopShopUrl);
    return '${uri.origin}/shop/product';
  }

  String get _dinnerUrl {
    final uri = Uri.parse(brandsInfo.okshopShopUrl);
    return '${uri.origin}/product';
  }

  bool get urlAvailable {
    if (id > 0) {
      final uri = Uri.parse(productionUrl);
      return uri.scheme.contains(RegExp('^https?'));
    }
    return false;
  }

  // 商品網址
  String get productionUrl => '$_baseUrl/$id';

  String get _baseUrl {
    switch (args.productKind) {
      case ProductKind.DinnerApp:
      case ProductKind.DinnerLine:
        return _dinnerUrl;
      case ProductKind.Retail:
        return _shopUrl;
      default:
        return '';
    }
  }

  ProductEditingController({
    @required this.productProvider,
  });

  void _initObservable() {
    _id.stream.takeUntil(_disposable.future).listen((event) => onRefresh());
    tiles.stream
        .where((event) => event.isEmpty)
        .takeUntil(_disposable.future)
        .listen((event) {
      imageEditing.value = false;
    });
  }

  @override
  void onInit() {
    super.onInit();
    _initObservable();
    args = Get.arguments;
    _id.value = args?.productId ?? 0;
  }

  @override
  void onReady() {
    super.onReady();
    titleEditingController.addListener(() {
      draft.title = titleEditingController.text;
    });
    priceEditingController.addListener(() {
      final input = num.tryParse(priceEditingController.text ?? '') ?? 0;
      draft.price = input.round();
    });
    vipPriceEditingController.addListener(() {
      final input = num.tryParse(vipPriceEditingController.text ?? '') ?? 0;
      draft.vipPrice = input.round();
    });
    stockEditingController.addListener(() {
      final input = num.tryParse(stockEditingController.text ?? '') ?? 0;
      draft.stock = input.round();
    });
  }

  @override
  void onClose() {
    stockEditingController.dispose();
    titleEditingController.dispose();
    priceEditingController.dispose();
    vipPriceEditingController.dispose();
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      if (id > 0) {
        // 先跟 Server 取 ProductsProductIdGetRes 來產生一個可編輯的 Modal
        _draft.value = await _getProduct(id);
        stockEditingController.text = '${draft?.stock}';
        titleEditingController.text = draft.title;
        priceEditingController.text = draft.initialPrice;
        vipPriceEditingController.text = draft.initialVipPrice;
      } else {
        // 直接做一個空的新 Modal 讓使用者編輯.
        _draft.value = ProductsPostReq(
          categories: [args.defaultCategoryId],
          additionCategories: <AdditionCategorySetting>[],
          kind: args.kind,
        );
      }
      //這邊嘗試初始化界面
      stockEditingController.text = '${draft?.stock}';
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  String get title {
    final action = id == 0 ? '新增' : '編輯';
    return '$action${args.productKind.title}';
  }

  // 取得資料
  Future<ProductsPostReq> _getProduct(int productId) async {
    final data = await productProvider.getProductDetail(productId);
    // 取得圖片
    data.productImages ??= <ProductsImage>[];
    data.productImages.sort((x, y) => (y.sort ?? 0).compareTo(x.sort ?? 0));
    tiles.assignAll(data.productImages.map((e) {
      return ImageModel(
        id: e.imageId,
        url: e.imageUrl,
      );
    }));
    return data.generateAReqModal(args.kind);
  }

  Future<bool> delete() async {
    final ret = await productProvider.deleteProduct(id);
    return ret is num && ret > 0;
  }

  // 刪除不需要的圖片
  void _removeImages() {
    final it = this.imagesToRemove.where((e) => regexp.hasMatch(e.url));
    this.imagesToRemove.clear();
    Future.forEach<ImageModel>(
        it, (element) => apiProvider.deleteImage(element.id));
  }

  Future<Iterable<ProductsImage>> _applyImages() async {
    // 移除圖片
    _removeImages();
    // 儲存圖片(不是 http 開頭)
    final files = this.tiles.where((e) => !regexp.hasMatch(e.url));
    final images =
        await apiProvider.postImages(List.from(files.map((e) => e.url)));
    // 上傳完成後賦予 id
    for (var i = 0; i < files.length; i++) {
      files.elementAt(i).id = images.elementAt(i).id;
    }

    return List.generate(this.tiles.length, (index) {
      final element = this.tiles.elementAt(index);
      return ProductsImage(
        imageId: element.id,
        sort: this.tiles.length - index,
      );
    });
  }

  ///
  /// 儲存商品
  ///
  Future<bool> submit() async {
    // 圖片
    _draft.value.productImages = List.from(await _applyImages());
    //移除資料中已經不存在的 categories
    draft.cleanUpNonExistCategories(categories.map((e) => e.id));
    //移除資料中已經不存在的 addition categories
    draft.cleanUpNonExistAdditionCategories(additionCategories);
    // 更新或新增
    final upsertProduct = id > 0 ? _updateProduct : _insertProduct;
    return await upsertProduct();
  }

  ///
  /// 更新
  ///
  Future<bool> _updateProduct() async {
    final ret = await productProvider.putProduct(id, draft);
    return ret is num && ret > 0;
  }

  ///
  /// 新增
  ///
  Future<bool> _insertProduct() async {
    final ret = await productProvider.postProduct(draft);
    return ret is num && ret > 0;
  }

  void refreshDraft() {
    _draft.refresh();
  }
}
