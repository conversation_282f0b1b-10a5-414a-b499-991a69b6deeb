import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:image/image.dart' as Img;
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/dialog_actions.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/image_item.dart';
import 'package:muyipork/app/components/product_addition_category_editing_view.dart';
import 'package:muyipork/app/components/checkbox_expandable_item.dart';
import 'package:muyipork/app/components/checkbox_item.dart';
import 'package:muyipork/app/components/radio_button.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/underline_divider.dart';
import 'package:muyipork/app/models/image_model.dart';
import 'package:muyipork/app/modules/category_setup/controllers/category_setup_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:reorderables/reorderables.dart';

import '../controllers/product_editing_controller.dart';

class ProductEditingView extends GetView<ProductEditingController> {
  static const _THUMBNAIL_SIZE = 60.0;
  static const _IMAGE_SIZE = 600.0;

  ///
  /// 刪除產品
  ///
  Future<void> _delete() async {
    final button = Completer<Button>();
    DialogGeneral.quest(
      '即將刪除產品',
      mainButtonText: '刪除',
      onMainButtonPressed: () {
        button.complete(Button.Positive);
      },
      secondaryButtonText: '取消',
      onSecondaryButtonPress: () {
        button.complete(Button.Negative);
      },
    ).dialog();
    final res = await button.future;
    if (res == Button.Positive) {
      final ret = await FutureProgress<bool>(
        future: controller.delete(),
      ).dialog();
      if (true == ret) {
        Get.back(result: true);
      }
    }
  }

  Iterable<Widget> _actions() sync* {
    if (controller.id > 0) {
      yield Align(
        alignment: Alignment.center,
        child: SizedBox(
          height: 36,
          child: OutlinedButton(
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.zero,
              textStyle: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              // visualDensity: VisualDensity.compact,
              shape: StadiumBorder(),
              side: BorderSide(
                width: 1.0,
                color: Colors.white,
              ),
            ),
            onPressed: _delete,
            child: Text(
              '刪除',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ),
      );
      yield SizedBox(width: 12);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.focusScope.unfocus(),
      child: StandardPage(
        actions: _actions().toList(growable: false),
        titleText: controller.title,
        child: controller.obx((state) {
          return BottomWidgetPage.save(
            child: Obx(() => _body()),
            onPressed: _submit,
          );
        }),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 30);
    // product title TextField
    yield SettingsWidget.input(
      controller: controller.titleEditingController,
      // initialValue: controller.draft.title,
      labelText: '${controller.args.productKind.title}名稱',
      hintText: '可輸入20個字',
      // onChanged: (value) {
      //   controller.draft.title = value;
      //   controller.titleEditingController.text = value;
      // },
    );
    // Price TextField
    yield SettingsWidget.input(
      controller: controller.priceEditingController,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly
      ],
      keyboardType: TextInputType.number,
      // initialValue: controller.draft.initialPrice,
      labelText: '價格',
      hintText: '價格',
      // onChanged: (value) {
      //   final input = num.tryParse(value ?? '') ?? 0;
      //   controller.draft.price = input.round();
      //   controller.priceEditingController.text = input.toString();
      // },
    );
    yield SettingsWidget.input(
      controller: controller.vipPriceEditingController,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly
      ],
      keyboardType: TextInputType.number,
      // initialValue: controller.draft.initialVipPrice,
      labelText: 'VIP價格',
      hintText: 'VIP價格',
      // onChanged: (value) {
      //   final input = num.tryParse(value ?? '') ?? 0;
      //   controller.draft.vipPrice = input.round();
      //   controller.vipPriceEditingController.text = input.toString();
      // },
    );
    yield Obx(() => _permission());
    // 只有零售 (kind: 2) 才顯示庫存編輯欄位
    if (controller.args.productKind.isRetail) {
      yield Obx(() => _editStock());
    }
    // 只有零售 (kind: 2) 才顯示運送方式編輯欄位
    if (controller.args.productKind.isRetail) {
      yield Obx(() => _editShipping());
    }
    if (controller.prefProvider.brandsInvoice.isMixTaxType) {
      yield Obx(() => _editingTaxType());
    }
    yield const SizedBox(height: kPadding);
    //Summary Editing
    yield _editSummary();
    yield const SizedBox(height: kPadding);
    //Categories
    yield Obx(() => _categoryEditor());
    yield const SizedBox(height: kPadding);
    //Addition Categories
    yield Obx(() => _additionEditor());
    yield const SizedBox(height: kPadding);
    yield* _imageEditor();
    yield const SizedBox(height: kPadding);
    // 商品網址
    if (controller.urlAvailable) {
      yield SettingsWidget.link(
        titleText: '商品網址',
        subtitleText: controller.productionUrl,
        tileColor: Colors.transparent,
      );
    }
    // 此商品是否隱藏
    if ([ProductKind.DinnerLine, ProductKind.Retail]
        .contains(controller.args.productKind)) {
      yield Obx(() {
        controller.draft.isHidden ??= Switcher.Off.index;
        return SettingsWidget.switcher(
          titleText: '此商品是否隱藏',
          value: controller.draft.isHidden.switcher.isOn,
          tileColor: Colors.transparent,
          onChanged: (value) {
            controller.draft.isHidden = value.switcher.index;
            controller.refreshDraft();
          },
        );
      });
    }
    yield const SizedBox(height: kPadding);
  }

  Widget _body() {
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      physics: const AlwaysScrollableScrollPhysics(),
      children: _children().toList(growable: false),
    );
  }

  Future<void> _submit() async {
    try {
      if (controller.draft.validate()) {
        final ret = await FutureProgress(
          future: controller.submit(),
        ).dialog();
        if (true == ret) {
          Get.back(result: true);
        }
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  void _onReorder(int oldIndex, int newIndex) {
    logger.d('_onReorder oldIndex($oldIndex), newIndex($newIndex)');
    final tempImage = controller.tiles[oldIndex];
    controller.tiles[oldIndex] = controller.tiles[newIndex];
    controller.tiles[newIndex] = tempImage;
  }

  Future<File> _showImagePicker() {
    return DialogActions(
      titleText: '取得照片',
      actions: [
        '相機',
        '相簿',
      ],
    )
        .dialog<num>(
      barrierDismissible: true,
    )
        .then(
      (value) {
        final ImagePicker _picker = ImagePicker();
        switch (value) {
          case 0:
            return _picker.pickImage(
              source: ImageSource.camera,
              maxWidth: _IMAGE_SIZE,
              maxHeight: _IMAGE_SIZE,
            );
          case 1:
            return _picker.pickImage(
              source: ImageSource.gallery,
              maxWidth: _IMAGE_SIZE,
              maxHeight: _IMAGE_SIZE,
            );
          default:
        }
      },
    ).then(
      (value) {
        return _cropImage(value.path);
      },
    );
  }

  Future<File> _cropImage(String path) {
    // create square image with white background
    final data = File(path).readAsBytesSync();
    Img.Image image = Img.decodeImage(data);
    final size = max(image.width, image.height);
    final blank = Img.Image.rgb(size, size);
    blank.fill(Colors.white.value);
    // bland
    final dstX = (size - image.width) ~/ 2;
    final dstY = (size - image.height) ~/ 2;
    final thumbnail = Img.copyInto(
      blank,
      image,
      dstX: dstX,
      dstY: dstY,
      blend: false,
    );
    // Resize the image to a 120x? thumbnail (maintaining the aspect ratio).
    // Img.Image thumbnail = Img.copyResizeCropSquare(image, 300);
    // Img.Image thumbnail2 = Img.copyResize(
    //   image,
    //   width: 300,
    //   height: 300,
    // );
    // Save the thumbnail as a PNG.
    // final newfile = '${path.path}/${path.filename}';
    File(path).writeAsBytesSync(Img.encodePng(thumbnail));

    return ImageCropper.cropImage(
      sourcePath: path,
      maxWidth: _IMAGE_SIZE.round(),
      maxHeight: _IMAGE_SIZE.round(),
      aspectRatio: CropAspectRatio(
        ratioX: 1.0,
        ratioY: 1.0,
      ),
      aspectRatioPresets: [
        CropAspectRatioPreset.square,
        // CropAspectRatioPreset.ratio3x2,
        // CropAspectRatioPreset.original,
        // CropAspectRatioPreset.ratio4x3,
        // CropAspectRatioPreset.ratio16x9
      ],
      androidUiSettings: AndroidUiSettings(
        toolbarTitle: 'Cropper',
        toolbarColor: Colors.deepOrange,
        toolbarWidgetColor: Colors.white,
        initAspectRatio: CropAspectRatioPreset.original,
        lockAspectRatio: true,
        // backgroundColor: Colors.white,
      ),
      iosUiSettings: IOSUiSettings(
          // minimumAspectRatio: 1.0,
          doneButtonTitle: '完成',
          cancelButtonTitle: '取消',
          aspectRatioLockDimensionSwapEnabled: true,
          aspectRatioLockEnabled: true),
    );
  }

  Widget get _plusIcon {
    return ImageItem(
      child: Icon(
        Icons.add,
        size: 60.0,
        color: Colors.white,
      ),
      onPressed: () async {
        final value = await _showImagePicker();
        controller.tiles.add(ImageModel(
          url: value.uri.toFilePath(),
        ));
      },
    );
  }

  Widget _getLocalImage(String path) {
    return SizedBox.fromSize(
      size: Size.square(_THUMBNAIL_SIZE),
      child: Image.file(
        File(path),
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _getNetworkImage(String imageUrl) {
    return SizedBox.fromSize(
      size: Size.square(_THUMBNAIL_SIZE),
      child: CachedNetworkImage(
        fit: BoxFit.contain,
        imageUrl: imageUrl ?? '',
        placeholder: (context, url) {
          return CircularProgressIndicator();
        },
        errorWidget: (context, url, error) {
          return Icon(
            Icons.image,
            size: _THUMBNAIL_SIZE,
            color: Colors.white,
          );
        },
      ),
    );
  }

  Iterable<Widget> _imageEditor() {
    final children = <Widget>[];
    children.addIf(
      true,
      Row(
        children: [
          Expanded(
            child: Text(
              '商品照片：',
              style: const TextStyle(
                fontSize: 14,
                color: const Color(0xff222222),
              ),
              textAlign: TextAlign.left,
            ),
          ),
          Obx(() {
            final editing = controller.imageEditing.value;
            return Visibility(
              visible: controller.tiles.isNotEmpty,
              child: TextButton.icon(
                onPressed: controller.imageEditing.toggle,
                icon: Icon(
                  editing ? Icons.check : Icons.edit,
                  color: kColorPrimary,
                ),
                label: Text(
                  editing ? '完成' : '編輯',
                  style: const TextStyle(
                    fontSize: 14,
                    color: kColorPrimary,
                    height: 2,
                  ),
                  textHeightBehavior:
                      TextHeightBehavior(applyHeightToFirstAscent: false),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          }),
        ],
      ).paddingSymmetric(
        horizontal: 16.0,
      ),
    );
    children.addIf(
      true,
      Obx(() {
        return Visibility(
          visible: controller.imageEditing.value,
          child: Text(
            '您可刪除或更改商品照片排序。',
            style: const TextStyle(
              fontSize: 14.0,
              color: const Color(0xffe00707),
            ),
            textAlign: TextAlign.left,
          ).paddingSymmetric(
            horizontal: 16.0,
          ),
        );
      }),
    );
    children.addIf(true, SizedBox(height: kPadding));
    children.addIf(
      true,
      Obx(() {
        final editing = controller.imageEditing.value;
        final ls = controller.tiles
            .map((e) {
              final child = controller.regexp.hasMatch(e.url)
                  ? _getNetworkImage(e.url)
                  : _getLocalImage(e.url);
              return ImageItem(
                child: child,
                onDeletePressed: editing
                    ? () {
                        if (controller.tiles.remove(e)) {
                          controller.imagesToRemove.add(e);
                        }
                      }
                    : null,
              );
            })
            .cast<Widget>()
            .toList();
        if (editing) {
          return ReorderableWrap(
            needsLongPressDraggable: false,
            spacing: 40.0,
            runSpacing: 28.0,
            children: ls,
            onReorder: _onReorder,
            onNoReorder: (int index) {
              //this callback is optional
              logger.d(
                  '${DateTime.now().toString().substring(5, 22)} reorder cancelled. index:$index');
            },
            onReorderStarted: (int index) {
              //this callback is optional
              logger.d(
                  '${DateTime.now().toString().substring(5, 22)} reorder started: index:$index');
            },
          );
        } else {
          // 加入圖片張數限制
          if (ls.length < ProductEditingController.MAX_TILE) {
            ls.add(this._plusIcon);
          }
          return Wrap(
            spacing: 40.0,
            runSpacing: 28.0,
            children: ls,
          );
        }
      }).paddingSymmetric(
        horizontal: 12.0,
      ),
    );
    return children;
  }

  Widget _permission() {
    final children = <Widget>[];
    children.addIf(
      true,
      Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          children: [
            TextSpan(
              text: '觀看權限',
            ),
            TextSpan(
              text: '*',
              style: TextStyle(
                color: OKColor.Error,
              ),
            ),
          ],
        ),
        textAlign: TextAlign.left,
      ),
    );
    children.addIf(true, Spacer());
    children.addIf(
      true,
      RadioButton<num>(
        titleText: '全部會員',
        groupValue: controller.draft.isVip,
        value: Switcher.Off.index,
        onChanged: (value) {
          controller.draft.isVip = value;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      true,
      RadioButton<num>(
        titleText: '僅VIP會員',
        groupValue: controller.draft.isVip,
        value: Switcher.On.index,
        onChanged: (value) {
          controller.draft.isVip = value;
          controller.refreshDraft();
        },
      ),
    );
    return UnderlineDivider(
      insets: kContentPadding,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ).paddingSymmetric(horizontal: kPadding),
    );
  }

  List<Widget> _stockContent(bool largeWidth) {
    final children = <Widget>[];
    children.addIf(
      true,
      Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          children: [
            TextSpan(
              text: '網路庫存量',
            ),
            TextSpan(
              text: '*',
              style: TextStyle(
                color: OKColor.Must,
              ),
            ),
          ],
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.left,
      ),
    );
    children.addIf(largeWidth, Spacer());
    children.addIf(
      true,
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Spacer(),
          // 0
          RadioButton<int>(
            titleText: '無限制',
            value: 1,
            groupValue: controller.draft.stockUnlimited,
            onChanged: (value) {
              controller.draft.stockUnlimited = value;
              controller.refreshDraft();
            },
          ),
          // 1
          RadioButton<int>(
            child: Row(
              children: [
                //stock
                SizedBox(
                  width: 60,
                  child: CustomEditor.number(
                    controller: controller.stockEditingController,
                    textAlign: TextAlign.center,
                    decoration: InputDecoration(
                      fillColor: Colors.white,
                      contentPadding: EdgeInsets.symmetric(horizontal: 8),
                      isDense: true,
                    ),
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.singleLineFormatter,
                      FilteringTextInputFormatter.digitsOnly
                    ],
                    // onChanged: (value) {
                    //   controller.draft.stock = num.tryParse(value ?? '') ?? 0;
                    // },
                  ),
                ),
                Text('件'),
              ],
            ),
            value: 0,
            groupValue: controller.draft.stockUnlimited,
            onChanged: (value) {
              controller.draft.stockUnlimited = value;
              controller.refreshDraft();
            },
          ),
        ],
      ),
    );
    return children;
  }

  Widget _editStock() {
    controller.draft.stockUnlimited ??= 1;
    controller.draft.stock ??= 1;
    return UnderlineDivider(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: kPadding),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final largeWidth = constraints.maxWidth > 320;
            if (largeWidth) {
              return Row(
                children: _stockContent(largeWidth),
              );
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: _stockContent(largeWidth),
            );
          },
        ),
      ),
    );
  }

  Widget _editingTaxType() {
    final children = <Widget>[];
    children.addIf(
      true,
      Text.rich(
        TextSpan(
          style: const TextStyle(
            fontSize: 16.0,
            color: Colors.black,
          ),
          children: [
            TextSpan(
              text: '混合稅率',
            ),
            TextSpan(
              text: '*',
              style: TextStyle(
                color: const Color(0xffe00707),
              ),
            ),
          ],
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.left,
      ),
    );
    children.addIf(true, Spacer());
    children.addIf(
      true,
      RadioButton<TaxType>(
        titleText: '應稅',
        value: TaxType.TX,
        groupValue: controller.draft.taxType.taxType,
        onChanged: (value) {
          controller.draft.taxType = value.index;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      true,
      RadioButton<TaxType>(
        titleText: '免稅',
        value: TaxType.Free,
        groupValue: controller.draft.taxType.taxType,
        onChanged: (value) {
          controller.draft.taxType = value.index;
          controller.refreshDraft();
        },
      ),
    );
    return UnderlineDivider(
      child: Padding(
        padding: kContentPadding,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: children,
        ),
      ),
    );
  }

  Widget _editShipping() {
    //A dirty quick fix for server's null value...
    controller.draft.shippingType ??= ShippingType.Normal.index;
    final children = <Widget>[];
    children.addIf(
      true,
      Text(
        '運送方式',
        style: Get.textTheme.subtitle1,
      ),
    );
    children.addIf(
      true,
      Text(
        '*',
        style: Get.textTheme.bodyText1.copyWith(color: Colors.red),
      ),
    );
    children.addIf(true, Spacer());
    // 0: 常溫
    children.addIf(
      true,
      RadioButton<ShippingType>(
        titleText: '常溫',
        value: ShippingType.Normal,
        groupValue: controller.draft.shippingType.shippingType,
        onChanged: (value) {
          controller.draft.shippingType = value.index;
          controller.refreshDraft();
        },
      ),
    );
    // 1: 低溫
    children.addIf(
      true,
      RadioButton<ShippingType>(
        titleText: '低溫',
        value: ShippingType.Cold,
        groupValue: controller.draft.shippingType.shippingType,
        onChanged: (value) {
          controller.draft.shippingType = value.index;
          controller.refreshDraft();
        },
      ),
    );
    return UnderlineDivider(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: kPadding),
        child: Row(children: children),
      ),
    );
  }

  Widget _editSummary() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.comment(
        contentPadding: EdgeInsets.symmetric(horizontal: kPadding),
        titleText: controller.args.productKind.summaryTitle,
        initialValue: controller.draft.summary ?? '',
        hintText: controller.args.productKind.summaryHint,
        onChanged: (value) {
          final limit = controller.args.productKind.summaryLengthLimit;
          final length = min(value.length, limit);
          controller.draft.summary = value.substring(0, length);
        },
      ),
    );
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  Future<void> _onAddCategoryPressed() async {
    //開啟編輯頁面
    await Get.toNamed(
      Routes.CATEGORY_SETUP,
      parameters: {
        Keys.Tag: '${CategorySetupTab.Categories.index}',
      },
      arguments: CategorySetupArgs(
        initialTab: CategorySetupTab.Categories,
        kind: controller.args.kind,
        title: '分類設定',
      ),
    );
    controller.refreshDraft();
  }

  Widget _categoryEditor() {
    Iterable<Widget> children() sync* {
      yield _plusButton('分類', _onAddCategoryPressed).paddingSymmetric(
        horizontal: Constants.paddingHorizontal,
      );
      yield* _categoryList();
    }

    return children().column();
  }

  Future<void> _onAddCategoryAdditionPressed() async {
    //開啟編輯頁面
    await Get.toNamed(
      Routes.CATEGORY_SETUP,
      parameters: {
        Keys.Tag: '${CategorySetupTab.AdditionCategories.index}',
      },
      arguments: CategorySetupArgs(
        initialTab: CategorySetupTab.AdditionCategories,
        kind: controller.args.kind,
        title: '規格設定',
      ),
    );
    controller.refreshDraft();
  }

  Widget _additionEditor() {
    Iterable<Widget> children() sync* {
      yield _plusButton('規格', _onAddCategoryAdditionPressed).paddingSymmetric(
        horizontal: Constants.paddingHorizontal,
      );
      yield* _additionList();
    }

    return children().column();
  }

  Widget _plusButton(String title, VoidCallback onButtonPress) {
    Iterable<Widget> children() sync* {
      yield Text(
        title ?? '',
        style: Get.textTheme.subtitle1,
      ).expanded();
      yield IconButton(
        icon: const Icon(
          Icons.add_circle_outline,
          color: OKColor.Primary,
        ),
        onPressed: onButtonPress,
      );
    }

    return children().row();
  }

  // 編輯列表
  Iterable<Widget> _categoryList() sync* {
    final draft = controller.draft;
    var needSeparator = false;
    for (final category in controller.categories) {
      if (needSeparator == true) {
        yield const Divider(height: 1);
      }
      yield CheckboxListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 8),
        tileColor: Colors.white,
        controlAffinity: ListTileControlAffinity.leading,
        title: Transform.translate(
          offset: Offset(-12, 0),
          child: Text(category.name),
        ),
        value: draft.categories.contains(category.id),
        onChanged: (b) {
          if (b) {
            // 選取
            if (!draft.categories.contains(category.id)) {
              draft.categories.add(category.id);
              controller.refreshDraft();
            }
          } else {
            // 取消
            if (draft.categories.remove(category.id)) {
              controller.refreshDraft();
            }
          }
        },
      );
      needSeparator = true;
    }
  }

  Iterable<Widget> _additionList() sync* {
    final draft = controller.draft;
    var needSeparator = false;
    for (final addition in controller.additionCategories) {
      if (needSeparator == true) {
        // yield const Divider();
      }
      yield CheckboxExpandableItem(
        checkboxTitle: addition.name,
        checkboxValue: draft.containsAdditionCategory(addition.id),
        onCheckboxChanged: (checked) {
          if (checked) {
            final cate = controller.productProvider
                .getAdditionCategoryWithId(addition.id);
            draft.addNewAdditionCategory(addition.id, cate?.name ?? '');
          } else {
            draft.removeAdditionCategory(addition.id);
          }
          controller.refreshDraft();
        },
        displaySwitch: draft.containsAdditionCategory(addition.id),
        switchTitle: '必選',
        switchValue: draft.isAdditionCategoryRequired(addition.id),
        onSwitchChanged: (b) {
          // 必選 Switch 被改變
          draft.setAdditionCategoryRequired(addition.id, b);
          //特殊邏輯: 如果必選被設為 true, 要避免 optionMin 為 0
          //如果必選被設為 false, 要確認 optionMin 為 0
          final acs = draft.tryGetAdditionCategorySetting(addition.id);
          if (b) {
            if (acs.optionMin == 0) {
              acs.optionMin = 1;
            }
          } else {
            if (acs.optionMin != 0) {
              acs.optionMin = 0;
            }
          }
          controller.refreshDraft();
        },
        expandedWidget: ProductAdditionCategoryEditingView(
          additionCategorySetting:
              draft.tryGetAdditionCategorySetting(addition.id),
          onOptionMinChanged: (newOptionMin) {
            final acs = draft.tryGetAdditionCategorySetting(addition.id);
            if (newOptionMin != 0) {
              //特殊邏輯: newOptionMin 不為 0, 把必選打開
              if (acs.required != 1) {
                acs.required = 1;
                controller.refreshDraft();
              }
            } else {
              //特殊邏輯: newOptionMin 為 0, 把必選關掉
              if (acs.required != 0) {
                acs.required = 0;
                controller.refreshDraft();
              }
            }
          },
        ),
      );
      needSeparator = true;
    }
  }
}
