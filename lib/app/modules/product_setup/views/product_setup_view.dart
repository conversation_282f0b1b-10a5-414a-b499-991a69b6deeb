import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_tab.dart';
import 'package:muyipork/app/components/custom_tab_bar.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/product_info_item.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/modules/product_editing/controllers/product_editing_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:reorderables/reorderables.dart';

import '../controllers/product_setup_controller.dart';

class ProductSetupView extends GetView<ProductSetupController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: controller.filter?.kind?.productKind?.titleText ?? '',
      onBackPress: () => Get.back(),
      child: BottomWidgetPage.save(
        buttonText: '新增',
        onPressed: _submit,
        child: controller.obx(
          (state) => _body(),
          onError: (message) {
            return ListWidget.message(
              message,
              buttonText: '重試',
              onPressed: controller.onRefresh,
            );
          },
          onEmpty: ListWidget.blank(),
        ),
      ),
    );
  }

  Widget _body() {
    return RefreshIndicator(
      onRefresh: controller.onRefresh,
      child: Obx(() => _main()),
    );
  }

  Iterable<Widget> _tabs() {
    final list = controller.categories;
    return list.map((e) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: CustomTab(
          titleText: e.name,
          selected: false,
        ),
      );
    });
  }

  Widget _header() {
    return Container(
      decoration: BoxDecoration(
        color: OKColor.Tab,
        boxShadow: [
          BoxShadow(
            color: const Color(0x29000000),
            offset: Offset(0, 0),
            blurRadius: 6,
          ),
        ],
      ),
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 4),
      child: CustomTabBar(
        themeColor: controller.prefProvider.themeColor,
        isScrollable: true,
        tabs: _tabs().toList(growable: false),
        onTap: (value) => controller.selectedIndex = value,
      ),
    );
  }

  Widget _main() {
    return DefaultTabController(
      length: controller.categories.length,
      child: Column(
        children: [
          _header(),
          Expanded(child: _list()),
        ],
      ),
    );
  }

  ///
  /// 排序事件
  ///
  void _onReorder(int oldIndex, int newIndex) {
    logger.d('_onReorder oldIndex($oldIndex), newIndex($newIndex)');
    final ls = List<ProductInfo>.from(controller.productWithCurrentCate());
    ls.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    final element = ls.removeAt(oldIndex);
    ls.insert(newIndex, element);
    for (var i = 0; i < ls.length; i++) {
      final data = ls.elementAt(i);
      data.sort = 1 + i;
    }
    controller.products.refresh();
    Future.forEach<ProductInfo>(ls.reversed, (element) {
      final put = element.asProductCategoriesPut();
      return controller.productProvider.sort(put);
    });
  }

  Widget _list() {
    final list = controller.productWithCurrentCate().toList(growable: false);
    list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    if (list.isEmpty && controller.more <= 0) {
      return ListWidget.blank();
    }
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: controller.scroll,
      slivers: <Widget>[
        ReorderableSliverList(
          delegate: ReorderableSliverChildBuilderDelegate(
            (context, index) {
              if (index < list.length) {
                //final data = controller.draft.elementAt(index);
                final data = list.elementAt(index);
                return ReorderableWidget(
                  key: ValueKey(data.uniqueId),
                  reorderable: true,
                  // child: _item(data),
                  child: ProductInfoItem(
                    dragable: true,
                    vipPrice: data.vipPrice,
                    vipOnly: data.isVip?.switcher,
                    title: data.title,
                    summary: data.summary,
                    price: data.price,
                    onTap: () async {
                      //Open the product editing page with EDIT mode.
                      // 編輯
                      final shouldUpdateProducts = await Get.toNamed(
                        Routes.PRODUCT_EDITING,
                        arguments: ProductEditingArgs(
                          productId: data.productId,
                          kind: controller.args.kind,
                        ),
                      );
                      if (shouldUpdateProducts != null &&
                          shouldUpdateProducts) {
                        // FIXME:
                        controller.onRefresh();
                        // await controller.reFetchProducts();
                      }
                    },
                    displaySwitch: true,
                    switchTitle: '售完',
                    switchValue: data.isSoldOut,
                    onSwitchChanged: (value) {
                      final callable = value == true ? _soldOut : _sold;
                      callable(data.productId);
                    },
                  ),
                );
              }
              controller.onEndScroll();
              return ListWidget.bottomProgressing();
            },
            childCount: list.length + controller.more,
          ),
          onReorder: _onReorder,
        ),
        SizedBox(
          height: kBottomButtonPadding,
        ).sliverBox,
      ],
    );
  }

  void _sold(num productId) {
    controller.sold(productId).catchError((error) {
      DialogGeneral.alert('$error').dialog();
    });
  }

  void _soldOut(num productId) {
    controller.soldOut(productId).catchError((error) {
      DialogGeneral.alert('$error').dialog();
    });
  }

  ///
  /// 新增產品，如外層使用 await，需回傳 Future<void>
  ///
  Future<void> _submit() async {
    try {
      final index = controller.selectedIndex;
      if (controller.categories.isEmpty) {
        // TODO: 引導使用者去新增分類的 Dialog
        Get.showAlert('目前沒有分類，請先新增分類');
      } else {
        final category = controller.categories.elementAt(index);
        // 新增
        final shouldUpdateProducts = await Get.toNamed(
          Routes.PRODUCT_EDITING,
          arguments: ProductEditingArgs(
            productId: 0,
            defaultCategoryId: category?.id ?? 0,
            kind: controller.filter.kind,
          ),
        );
        if (true == shouldUpdateProducts) {
          // FIXME:
          controller.onRefresh();
          // await controller.reFetchProducts();
        }
      }
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }
}

extension _ProductKind on ProductKind {
  String get titleText {
    switch (this) {
      case ProductKind.DinnerApp:
        return '店內菜單編輯';
      case ProductKind.DinnerLine:
        return '線上菜單編輯';
      case ProductKind.Retail:
        return '零售商品編輯';
      default:
        return '';
    }
  }
}
