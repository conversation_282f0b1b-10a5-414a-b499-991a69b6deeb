import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/extension.dart';

class PromotionPickerController extends GetxController with StateMixin<String> {
  final CouponProvider couponProvider;
  final singleSelected = RxNum(0);
  final multiSelected = RxMap<num, num>();
  final OrdersPostReq draft;

  PromotionPickerController({
    @required this.couponProvider,
    this.draft,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {}

  num get deltaPrice {
    var ret = 0.0;
    if (singleSelected.value > 0) {
      final coupon = couponProvider.couponsLikeCached[singleSelected.value];
      // ret += (100 - coupon.absDiscount) * (-0.01) * (draft?.normalItemsPrice ?? 0);
      ret += (100 - coupon.absDiscount) * (-0.01) * (draft?.total ?? 0);
    }
    for (var item in multiSelected.entries) {
      if (item.value > 0) {
        final coupon = couponProvider.couponsLikeCached[item.key];
        ret += coupon.promotionValue * item.value;
      }
    }
    return ret.round();
  }
}
