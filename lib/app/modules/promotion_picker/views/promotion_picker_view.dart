import 'dart:math';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/amount_edit.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';
import 'package:muyipork/app/components/radio_button.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';

import '../controllers/promotion_picker_controller.dart';

class PromotionPickerView extends GetView<PromotionPickerController> {
  final OrdersPostReq draft;
  final Iterable<PromotionType> promotionTypes;
  PromotionType get promotionType => promotionTypes?.first ?? PromotionType.Max;

  PromotionPickerView({
    @required this.draft,
    @required this.promotionTypes,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PromotionPickerController>(
      init: PromotionPickerController(
        couponProvider: Get.find(),
        draft: draft,
      ),
      builder: (controller) {
        return DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          builder: (context, scrollController) {
            return Obx(() => _bottomWidget(scrollController));
          },
        );
      },
    );
  }

  Widget _bottomWidget(final ScrollController scrollController) {
    final normalPrice = draft.total;
    final deltaPrice = controller.deltaPrice;
    final children = <Widget>[];
    children.addIf(true, SizedBox(height: kPadding));
    children.addIf(true, _label('金額', normalPrice));
    children.addIf(true, _label(promotionType?.title, deltaPrice));
    children.addIf(true, _label('小計', normalPrice + deltaPrice));
    children.addIf(true, SizedBox(height: kPadding));
    children.addIf(
      true,
      YesNoButton(
        leftButtonText: '清除',
        onLeftPressed: _onLeftPressed,
        rightButtonText: '確認',
        onRightPressed: _onRightPressed,
      ),
    );
    return BottomWidgetPage(
      child: _main(scrollController),
      bottom: BottomWrapper(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: children,
        ),
      ),
    );
  }

  // 清除
  void _onLeftPressed() {
    controller.singleSelected.value = 0;
    controller.multiSelected.clear();
  }

  // 傳回金額
  void _onRightPressed() {
    Get.back(result: controller.deltaPrice);
  }

  Widget _main(final ScrollController scrollController) {
    final children = <Widget>[];
    children.addIf(true, SizedBox(height: kPadding));
    children.addIf(true, _close());
    children.addIf(
      true,
      Expanded(
        child: Obx(() => _list(scrollController)),
      ),
    );
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _close() {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.close,
            color: Color(0xFF707070),
          ).paddingAll(4),
        ),
        onPressed: () => Get.back(),
      ).paddingSymmetric(
        vertical: 8,
        horizontal: 16,
      ),
    );
  }

  Widget _list(final ScrollController scrollController) {
    final children = <Widget>[];
    children.add(SizedBox(width: double.infinity));
    children.addIf(
      true,
      Text(
        promotionType?.title ?? '',
        style: TextStyle(
          fontSize: 18,
          color: const Color(0xff333333),
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
        softWrap: false,
      ),
    );
    children.addAll(_promotions());
    return ClipRRect(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(30.0),
      ),
      child: ColoredBox(
        color: Colors.white,
        child: ListView(
          // shrinkWrap: true,
          controller: scrollController,
          children: children,
        ),
      ),
    );
  }

  Iterable<Widget> _promotions() {
    return controller.couponProvider.couponsLikeCached.values.where(
      (element) {
        return promotionTypes.contains(element.promotionType.promotionType);
      },
    ).map((e) {
      switch (e.promotionType.promotionType) {
        case PromotionType.Off:
          return _offWidget(e);
        case PromotionType.Discount:
          return _discountWidget(e);
        case PromotionType.Upgrade:
          return _upgradeWidget(e);
        default:
          return SizedBox();
      }
    });
  }

  Widget _offWidget(Coupon data) {
    final children = <Widget>[];
    children.addIf(true, SizedBox(width: kPadding));
    children.addIf(
      true,
      RadioButton<num>(
        value: data.id,
        groupValue: controller.singleSelected.value,
        onChanged: controller.singleSelected,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(width: 8),
            Text(
              // '母親節活動(9折)',
              data.displayTitle ?? '',
              style: TextStyle(
                fontSize: 18,
                color: const Color(0xff333333),
              ),
              softWrap: false,
            )
          ],
        ),
      ),
    );
    children.addIf(true, SizedBox(width: kPadding));
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    ).paddingSymmetric(
      vertical: 8,
    );
  }

  Widget _discountWidget(Coupon data) {
    final children = <Widget>[];
    children.addIf(true, SizedBox(width: kPadding));
    children.add(Expanded(
      child: Text(
        // '母親節活動(9折)',
        data.displayTitle ?? '',
        style: TextStyle(
          fontSize: 18,
          color: const Color(0xff333333),
        ),
        softWrap: false,
      ),
    ));
    children.addIf(
      true,
      AmountEdit(
        editingValue: controller.multiSelected[data.id] ?? 0,
        onChanged: (value) {
          controller.multiSelected[data.id] = max(0, value);
        },
      ),
    );
    children.addIf(true, SizedBox(width: kPadding));
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    ).paddingSymmetric(
      vertical: 8,
    );
  }

  Widget _upgradeWidget(Coupon data) => _discountWidget(data);

  Widget _label(String key, num value) {
    final children = <InlineSpan>[];
    children.addIf(
      true,
      TextSpan(
        text: '$key ',
        style: TextStyle(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
    children.addIf(
      true,
      TextSpan(
        text: '${(value ?? 0).round().decimalStyle}',
        style: TextStyle(
          color: const Color(0xfffa821b),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
    children.addIf(
      true,
      TextSpan(
        text: '元',
        style: TextStyle(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
    return Align(
      alignment: Alignment.centerRight,
      child: Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 22,
            color: const Color(0xff222222),
          ),
          children: children,
        ),
        textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
        textAlign: TextAlign.right,
        softWrap: false,
      ),
    );
  }
}
