import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/save_page.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';

import '../controllers/push_settings_controller.dart';

class PushSettingsView extends GetView<PushSettingsController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      child: SavePage(
        child: _main(),
      ),
    );
  }

  Widget _main() {
    return Column(
      children: [
        SettingsWidget.title(
          titleText: '震動',
        ),
        SettingsWidget.switcher(
          titleText: '開啟震動',
          value: true,
          onChanged: (value) {
            //
          },
        ),
        SettingsWidget.title(
          titleText: '請選擇靜音或聲音',
        ),
        RadioListTile(
          title: Text(
            '靜音',
            style: TextStyle(
              fontSize: 16,
              color: const Color(0xff000000),
            ),
            textAlign: TextAlign.left,
          ),
          value: 0,
          groupValue: 1,
          onChanged: (value) {
            //
          },
        ),
        RadioListTile<int>(
          title: Text(
            '聲音1',
            style: TextStyle(
              fontSize: 16,
              color: const Color(0xff000000),
            ),
            textAlign: TextAlign.left,
          ),
          value: 1,
          groupValue: 1,
          onChanged: (value) {
            //
          },
        ),
        RadioListTile(
          title: Text(
            '聲音2',
            style: TextStyle(
              fontSize: 16,
              color: const Color(0xff000000),
            ),
            textAlign: TextAlign.left,
          ),
          value: 2,
          groupValue: 1,
          onChanged: (value) {
            //
          },
        ),
        RadioListTile(
          title: Text(
            '聲音3',
            style: TextStyle(
              fontSize: 16,
              color: const Color(0xff000000),
            ),
            textAlign: TextAlign.left,
          ),
          value: 3,
          groupValue: 1,
          onChanged: (value) {
            //
          },
        ),
      ],
    );
  }
}
