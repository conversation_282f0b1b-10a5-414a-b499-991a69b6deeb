import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:path_provider/path_provider.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/app/models/orders_orderid_print_put_req.dart';
import 'package:muyipork/app/models/order_root.dart';
import 'package:muyipork/app/models/orders_orderid_put_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/static_methods.dart';
import 'package:screenshot/screenshot.dart';
import 'package:muyipork/extension.dart';

// 運作模式
enum PrintingMode {
  DoNothing, // 0: 不印，會顯示補印按鈕
  PrintAllAndStay, // 1: 自動印後停在此頁: 會顯示補印按鈕
  PrintAllAndLeave, // 2: 自動印並離開，出錯才會停下並顯示補印按鈕
}

// 頁面顯示模式
enum PrintingPageSetting {
  ReceiptAndStickers, // 全部顯示
  ReceiptOnly, // 只顯示明細
  StickerOnly, // 只顯示工作單
  Max,
}

extension _PrintingModeX on PrintingMode {
  bool get needPrint {
    return [
      PrintingMode.PrintAllAndStay,
      PrintingMode.PrintAllAndLeave,
    ].contains(this);
  }

  bool get needBack {
    return [
      PrintingMode.PrintAllAndLeave,
    ].contains(this);
  }
}

extension _PrintingPageSettingX on PrintingPageSetting {
  bool get containsReceipt {
    return [
      PrintingPageSetting.ReceiptAndStickers,
      PrintingPageSetting.ReceiptOnly,
    ].contains(this);
  }

  bool get containsStickers {
    return [
      PrintingPageSetting.ReceiptAndStickers,
      PrintingPageSetting.StickerOnly,
    ].contains(this);
  }
}

// 頁面控制參數
class PrintingArgs {
  const PrintingArgs({
    this.printingMode = PrintingMode.DoNothing,
    this.printingPageSetting = PrintingPageSetting.ReceiptAndStickers,
    this.orderId,
  });

  // 指定列印模式
  final PrintingMode printingMode;
  // 頁面顯示設定
  final PrintingPageSetting printingPageSetting;
  // 使用 orderId 自行取得 modal.
  final int orderId;
}

class ReceiptStickerPrintingController extends GetxController
    with StateMixin<String> {
  // disposable 物件
  final _disposable = Completer();
  final PrinterProvider printerProvider;
  final OrderProvider orderProvider;
  ApiProvider get apiProvider => orderProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  // order instance
  final _data = Rx<OrderRoot>(null);
  OrderRoot get data => _data.value;

  final _arguments = Rx<PrintingArgs>(null);
  PrintingArgs get arguments => _arguments.value;
  // 頁面排版設定
  PrintingPageSetting get printingPageSetting => arguments.printingPageSetting;
  final stickerScreenshotController = ScreenshotController();

  // Are we printing anything? This will display a progress bar at the bottom.
  final _isPrinting = false.obs;
  bool get isPrinting => _isPrinting.value;

  // sticker model
  final _sticker = Rx<Sticker>(null);
  Sticker get sticker => _sticker.value;
  // sticker widget 更新器
  final _widgetUpdater = Completer().obs;
  Completer get widgetUpdater => _widgetUpdater.value;
  // order id
  final _id = RxNum(0);
  num get id => _id.value;
  set id(num value) => _id.value = value;

  ReceiptStickerPrintingController({
    this.printerProvider,
    this.orderProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 取得傳遞過來的參數
    if (Get.arguments is PrintingArgs) {
      _arguments.value = Get.arguments;
    }
    // 監聽要列印的訂單 id
    _id.stream
        .distinct()
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    // 由參數取得 order id
    _id.value = arguments.orderId;
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
      _data.value = await orderProvider.getOrderDetail(id, cacheFirst: true);
      final needAutoPrint = arguments?.printingMode?.needPrint ?? false;
      if (needAutoPrint) {
        await _autoPrint();
      }
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    } finally {
      // 跳出頁面
      if (arguments?.printingMode?.needBack ?? false) {
        Get.back(result: true);
      }
    }
  }

  ///
  /// Receipt 用商店名稱
  ///
  String get storeName {
    final info = prefProvider.brandsInfo;
    return info?.name ?? '';
  }

  ///
  /// Receipt 用帳號名稱
  ///
  String get accountName {
    final jwt = prefProvider.jwt;
    return jwt?.name ?? '';
  }

  Future<void> printReceipt() async {
    try {
      _isPrinting.value = true;
      await _printReceipt();
    } finally {
      _isPrinting.value = false;
    }
  }

  // Try print the Receipt.
  Future<void> _printReceipt() async {
    final receipt = data.asReceipt(
      storeName: storeName,
      userName: accountName,
    );
    // TODO: move to printer provider
    await SunmiUtil.printReceipt(receipt);
    await _putOrderIsPrinted();
  }

  // 一次列印明細 + 所有工作單
  Future<void> _autoPrint() async {
    // 列印明細
    if (printingPageSetting.containsReceipt) {
      try {
        await printReceipt();
      } catch (e) {
        logger.e(e);
      }
    }
    // 列印工作單
    if (printingPageSetting.containsStickers) {
      for (var item in data.normalItems) {
        try {
          await printTagStickers(item);
        } catch (e) {
          logger.e(e);
        }
      }
    }
  }

  Future<void> printTagStickers(OrderItem orderItem) async {
    try {
      _isPrinting.value = true;
      await _printTagStickers(orderItem);
    } finally {
      _isPrinting.value = false;
    }
  }

  // 列印 order item，數量及過濾由內部判斷
  Future<void> _printTagStickers(OrderItem orderItem) async {
    // 產生新的 completer
    _widgetUpdater.value = Completer();
    // 設定目前的列印資料
    _sticker.value = data.data.asSticker(orderItem);
    // 等待內容變更完成
    await widgetUpdater.future;
    final ctrl = stickerScreenshotController;
    // final ctrl = stickerScreenshotControllers[orderItem.id];
    logger.d('[Sticker] Capture screenshot');
    final image = await ctrl.capture(pixelRatio: 1.0, delay: 200.milliseconds);
    // final directory = await getApplicationDocumentsDirectory();
    // logger.d('[Sticker] Save image to ${directory.path}');
    // await saveUint8ListToFile(image, '${directory.path}/sticker.png');
    if (image != null) {
      await _printOrderItemTagSticker(
        orderItem,
        image,
      );
    }
  }

  // Future<void> saveUint8ListToFile(Uint8List uint8List, String filePath) async {
  //   try {
  //     final file = File(filePath);
  //     await file.writeAsBytes(uint8List);
  //     print('Uint8List saved to $filePath');
  //   } catch (e) {
  //     print('Error saving Uint8List to file: $e');
  //   }
  // }

  // 列印傳入數量的圖片
  Future<void> _printOrderItemTagSticker(
      OrderItem orderItem, Uint8List image) async {
    final categoryIds = orderItem.productCategoryIds;
    final printers = printerProvider.getPrinterWithCategories(categoryIds);
    for (var printer in printers) {
      final printCount = printer.getMaxPrintCount(categoryIds);
      final itemCount = orderItem.nnQuantity;
      final count = printCount * itemCount;
      for (var i = 0; i < count; i++) {
        try {
          await _printImage(printer, image);
        } catch (e) {
          // 不可拋出例外，會中斷迴圈
          logger.e(e);
        }
      }
    }
  }

  // 因需區分 godex 及 sunmi，故多了此 function 傳入印表機及圖片列印
  Future<void> _printImage(SettingLabel printer, Uint8List image) async {
    if (PrinterType.all.contains(printer.type)) {
      // print with sunmi printer
      if (printer.categoryIds.contains(PrintType.sticker.value) &&
          printer.type == PrinterType.net.value) {
        // 使用文字方式列印
        final bytes = await printer.stickerTask(sticker);
        // 使用圖片方式列印
        // 不可直接把 ui.Image 轉化為 bytes 的資料直接丟入商米印表機
        // 需轉換成 esc post 專用的 bytes
        // final bytes = await printer.imageTask(image);
        printer.pushTask(bytes);
      }
    } else {
      // print with godex printer
      printer.pushTask(image);
    }
  }

  Future<void> _putOrderIsPrinted() async {
    try {
      final res = await apiProvider.putOrdersOrderIdPrint(
        id,
        OrdersOrderIdPrintPutReq(isPrint: Switcher.On.index),
      );
      logger.i(res);
    } catch (e) {
      logger.e(e);
    }
  }
}
