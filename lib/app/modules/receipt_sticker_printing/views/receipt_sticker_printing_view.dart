import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/background.dart';
import 'package:muyipork/app/components/bottom_button.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/order_sticker.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/sticker_widget.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:screenshot/screenshot.dart';

import '../controllers/receipt_sticker_printing_controller.dart';

class ReceiptStickerPrintingView
    extends GetView<ReceiptStickerPrintingController> {
  const ReceiptStickerPrintingView({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '列印',
      child: controller.obx(
        (state) {
          return BottomWidgetPage(
            child: Stack(
              alignment: Alignment.topCenter,
              children: _children().toList(growable: false),
            ),
            bottom: Obx(() => _bottom()),
          );
        },
      ),
    );
  }

  Widget _bottom() {
    if (controller.isPrinting) {
      return IntrinsicHeight(
        child: Background(
          background: LinearProgressIndicator(
            minHeight: kMinInteractiveDimension,
          ),
          child: Align(
            alignment: Alignment.center,
            child: Text(
              '列印中',
              style: Get.textTheme.subtitle1
                  .copyWith(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ),
      );
    }
    return BottomButton(
      buttonText: '全部列印',
      onPressed: _printAllStickers,
    );
  }

  Future<void> _printAllStickers() async {
    try {
      Get.showLoading();
      for (var item in controller.data.normalItems) {
        await controller.printTagStickers(item);
      }
      Get.back();
      Get.showAlert('已為你送印');
    } catch (e) {
      Get.back();
      Get.showAlert('列印失敗，請檢查設定以及Wifi是否正常');
    }
  }

  Iterable<Widget> _children() sync* {
    // 列印截圖 (單張)
    yield Obx(() {
      final ret = Screenshot(
        controller: controller.stickerScreenshotController,
        child: StickerWidget(data: controller.sticker),
      );
      logger.d('[Sticker] Content update');
      controller.widgetUpdater.complete();
      return ret;
    });
    // 國防布
    yield ColoredBox(
      color: kColorBackground,
      child: SizedBox.expand(),
    );
    // 使用者實際看到的預覽介面
    yield _visiblePreview();
  }

  // 使用者實際看到可以操作的介面
  Widget _visiblePreview() {
    final list = controller.data.normalItems;
    return ListView.separated(
      padding: EdgeInsets.only(
        top: kPadding,
        bottom: kBottomButtonPadding,
      ),
      itemCount: list.length,
      separatorBuilder: (context, index) {
        return SizedBox(
          height: kPadding,
        );
      },
      itemBuilder: (context, index) {
        final element = list.elementAt(index);
        return Center(
          child: _sticker(element),
        );
      },
    );
  }

  // 工作單預覽介面單張 (截圖及操作共用)
  Widget _sticker(OrderItem orderItem) {
    Iterable<Widget> children(Sticker sticker) sync* {
      yield OrderSticker(sticker);
      yield TextButton(
        onPressed: () async {
          try {
            Get.showLoading();
            await controller.printTagStickers(orderItem);
            Get.back();
            Get.showAlert('已為你送印');
          } catch (e) {
            Get.back();
            Get.showAlert('列印失敗，請檢查設定以及Wifi是否正常');
          }
        },
        child: Text(
          '共 ${orderItem.nnQuantity} 份\n列印',
          style: const TextStyle(
            fontSize: 22,
            color: Colors.white,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    final sticker = controller.data.data.asSticker(orderItem);
    return DecoratedBox(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(25.0),
          bottom: Radius.circular(15.0),
        ),
        color: Color(0xff929ca8),
        boxShadow: [
          BoxShadow(
            color: Color(0x29000000),
            offset: Offset(0, 3),
            blurRadius: 6,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children(sticker).toList(growable: false),
      ),
    );
  }
}
