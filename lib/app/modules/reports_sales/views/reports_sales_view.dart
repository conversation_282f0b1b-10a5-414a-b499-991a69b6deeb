import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/custom_divider.dart';
import 'package:muyipork/app/components/date_banner.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/label_value_text.dart';
import 'package:muyipork/app/components/left_center_right.dart';
import 'package:muyipork/app/components/print_header.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';

import '../controllers/reports_sales_controller.dart';

class ReportsSalesView extends GetView<ReportsSalesController> {
  Future<void> _onPrintPressed() async {
    await FutureProgress(future: controller.print()).dialog();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return StandardPage(
        backgroundColor: controller.storeType.backgroundColor,
        actions: _actions().toList(growable: false),
        title: _title(),
        child: _main(),
      );
    });
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: _onPrintPressed,
      child: Text(
        '列印',
        style: const TextStyle(
          fontSize: 16,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _title() {
    return TextButton(
      onPressed: controller.toggleStoreType,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            controller.storeType.title ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          Visibility(
            visible: controller.hasMultipleStoreType,
            child: Icon(
              Icons.expand_more,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _main() {
    return Column(
      children: [
        DateBanner(
          initialDate: controller.dateTime,
          dateChanged: (value) {
            controller.dateTime = value;
          },
        ),
        Expanded(
          child: controller.obx((state) {
            return RefreshIndicator(
              onRefresh: controller.onRefresh,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                  ),
                  constraints: BoxConstraints(
                    maxWidth: 370,
                  ),
                  child: _list(),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _list() {
    final list = <Widget>[];
    list.add(SizedBox(
      height: kPadding,
    ));
    list.add(Text(
      // '餐飲2021/08/12',
      '${controller.storeType.name}${controller.dateTime.yMd}',
      style: TextStyle(
        fontSize: 16,
        color: Colors.black,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    ));
    list.add(SizedBox(
      height: 4,
    ));
    list.add(Text(
      '當日商品銷售統計',
      style: TextStyle(
        fontSize: 24,
        color: Colors.black,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    ));
    list.add(SizedBox(
      height: 8,
    ));
    list.add(Row(
      children: [
        Text(
          '列印時間：',
          style: TextStyle(
            fontSize: 18,
            color: Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
        Expanded(
          child: Text(
            // '2020/12/10 00:00:00',
            controller.now.yMdHms,
            style: TextStyle(
              fontSize: 18,
              color: Colors.black,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    ));
    list.add(CustomDivider.print(height: 20));
    list.add(Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '商店名稱',
          style: TextStyle(
            fontSize: 19,
            color: Colors.black,
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.left,
        ),
        Expanded(
          child: Text(
            // '好事成雙編織小舖好事成雙編織小舖好事成雙編織小舖小舖好事成雙編織小舖小舖好事成最多三十六個字',
            controller.prefProvider.brandsInfo.name,
            style: TextStyle(
              fontSize: 19,
              color: Colors.black,
              fontWeight: FontWeight.w700,
            ),
            maxLines: 3,
            softWrap: false,
            overflow: TextOverflow.ellipsis,
            textHeightBehavior:
                TextHeightBehavior(applyHeightToFirstAscent: false),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    ));
    list.add(SizedBox(
      height: 4,
    ));
    list.add(Row(
      children: [
        Text(
          '人員',
          style: TextStyle(
            fontSize: 19,
            color: Colors.black,
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.left,
        ),
        Expanded(
          child: Text(
            // '彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子',
            controller.prefProvider.jwt.name,
            style: TextStyle(
              fontSize: 19,
              color: Colors.black,
              fontWeight: FontWeight.w700,
            ),
            textHeightBehavior:
                TextHeightBehavior(applyHeightToFirstAscent: false),
            textAlign: TextAlign.right,
            maxLines: 1,
            softWrap: false,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    ));
    list.add(SizedBox(height: 4));

    list.addAll(_group(controller.data.asAppSales()));

    list.addAll(_group(controller.data.asOnlineSales()));

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: list,
    );
  }

  Iterable<Widget> item(App item) {
    final list = <Widget>[];
    // list.add(_labelValue5(left: AA.left('巧克力蛋糕'), right: AA.right('')));
    // list.add(
    //     _labelValue5(left: AA.left('210 X 10'), right: AA.right('\$2,100')));
    list.add(LabelValueText(left: TextArgs.left(item.productTitle)));
    final price = item.price ?? 0;
    final quantity = (item.quantity ?? 0).toInt();
    // final quantity = (item.quantity ?? 0) > 0 ? '${item.quantity}' : '';
    final total = item.total ?? 0;
    list.add(LabelValueText(
        left: TextArgs.left('\$${price.decimalStyle ?? 0} X $quantity'),
        right: TextArgs.right('\$${total.decimalStyle}')));
    list.add(SizedBox(height: 8));
    return list;
  }

  Iterable<Widget> _group(GroupArgs sales) {
    final list = <Widget>[];
    list.add(PrintHeader(sales.title));
    list.add(SizedBox(height: 8));
    sales.items.fold<List<Widget>>(list, (previousValue, element) {
      previousValue.addAll(item(element));
      return previousValue;
    });
    list.add(CustomDivider.print());
    list.add(SizedBox(height: 20));
    list.add(
        LabelValueText(right: TextArgs.right('\$${sales.total.decimalStyle}')));
    list.add(LabelValueText(right: TextArgs.right('一般銷售小計')));
    list.add(CustomDivider.print());
    list.add(SizedBox(height: 20));
    list.add(LabelValueText(
        left: TextArgs.right('${sales.count}'),
        right: TextArgs.right('${sales.quantity.round()}')));
    list.add(LabelValueText(
        left: TextArgs.right('合計品項'), right: TextArgs.right('合計銷售數量')));
    list.add(LeftCenterRight(
      left: CustomDivider.print(height: 12),
      right: CustomDivider.print(height: 12),
    ));
    // list.add(LabelValueText(right: TextArgs.right('41')));
    // list.add(LabelValueText(right: TextArgs.right('一般銷售訂單數量')));
    // list.add(CustomDivider.print(height: 20));
    return list;
  }
}

extension ExtensionStoreType on StoreType {
  String get title {
    switch (this) {
      case StoreType.Dinner:
        return '餐飲銷貨明細';
      case StoreType.Retail:
        return '零售銷貨明細';
      default:
        return '';
    }
  }

  Color get backgroundColor {
    switch (this) {
      case StoreType.Dinner:
        return kColorPrimary;
      case StoreType.Retail:
        return kColorRetailMode;
      default:
        return kColorPrimary;
    }
  }
}
