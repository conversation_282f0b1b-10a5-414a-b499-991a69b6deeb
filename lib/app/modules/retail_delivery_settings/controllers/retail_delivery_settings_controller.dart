import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/models/shipping_delivery.dart';
import 'package:muyipork/app/models/shipping_delivery_put.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';

class RetailDeliverySettingsController extends GetxController
    with StateMixin<String> {
  final SettingProvider settingProvider;
  ApiProvider get apiProvider => settingProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  final _draft = ShippingDeliveryPut().obs;
  ShippingDeliveryPut get draft => _draft.value;

  RetailDeliverySettingsController({
    @required this.settingProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  void refreshDraft() {
    _draft.refresh();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    try {
      final ret = await settingProvider.getShippingDelivery();
      prefProvider.shippingDelivery = ret;
      _draft.value = ret.asPut();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit() async {
    final ret = await settingProvider.putShippingDelivery(draft);
    if (ret is num && ret > 0) {
      // 儲存成功，更新本地設定
      prefProvider.shippingDelivery =
          await settingProvider.getShippingDelivery();
      return true;
    }
    return false;
  }
}

extension _ExtensionShippingDelivery on ShippingDelivery {
  ShippingDeliveryPut asPut() {
    return ShippingDeliveryPut(
      ambStatus: amb?.status ?? 0,
      ambShippingFee: amb?.shippingFee?.round() ?? 0,
      ambShippingFree: amb?.shippingFree?.round() ?? 0,
      coldStatus: cold?.status ?? 0,
      coldShippingFee: cold?.shippingFee?.round() ?? 0,
      coldShippingFree: cold?.shippingFree?.round() ?? 0,
      ambDescription: amb?.description ?? '',
      coldDescription: cold?.description ?? '',
      ambPreOrderAfterDays: amb?.preOrderAfterDays?.round() ?? 0,
      ambPreOrderWithinDays: amb?.preOrderWithinDays?.round() ?? 0,
      coldPreOrderAfterDays: cold?.preOrderAfterDays?.round() ?? 0,
      coldPreOrderWithinDays: cold?.preOrderWithinDays?.round() ?? 0,
    );
  }
}

extension ExtensionShippingDeliveryPut on ShippingDeliveryPut {
  String get displayAmbPreOrderAfterDays {
    ambPreOrderAfterDays ??= 0;
    if (ambPreOrderAfterDays is num && ambPreOrderAfterDays > 0) {
      return ambPreOrderAfterDays.toString();
    }
    return '';
  }

  String get displayAmbPreOrderWithinDays {
    ambPreOrderWithinDays ??= 0;
    if (ambPreOrderWithinDays is num && ambPreOrderWithinDays > 0) {
      return ambPreOrderWithinDays.toString();
    }
    return '';
  }

  String get displayColdPreOrderAfterDays {
    coldPreOrderAfterDays ??= 0;
    if (coldPreOrderAfterDays is num && coldPreOrderAfterDays > 0) {
      return coldPreOrderAfterDays.toString();
    }
    return '';
  }

  String get displayColdPreOrderWithinDays {
    coldPreOrderWithinDays ??= 0;
    if (coldPreOrderWithinDays is num && coldPreOrderWithinDays > 0) {
      return coldPreOrderWithinDays.toString();
    }
    return '';
  }
}
