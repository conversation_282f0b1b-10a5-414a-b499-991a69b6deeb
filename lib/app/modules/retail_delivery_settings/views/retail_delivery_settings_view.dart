import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/retail_delivery_settings_controller.dart';

class RetailDeliverySettingsView
    extends GetView<RetailDeliverySettingsController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.focusScope.unfocus();
      },
      child: StandardPage(
        titleText: '零售商店宅配寄送設定',
        child: controller.obx((state) {
          return BottomWidgetPage.save(
            onPressed: _submit,
            child: _main(),
          );
        }),
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, SizedBox(height: 12));
    children.addIf(
      true,
      Text(
        '宅配寄送',
        style: const TextStyle(
          fontSize: 16.0,
          color: kColorPrimary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
    );
    children.addIf(true, SizedBox(height: 12));
    children.addIf(
      true,
      Expanded(
        child: Obx(() => _list()),
      ),
    );
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _list() {
    final children = <Widget>[];
    children.addAllIf(true, _page1());
    children.addAllIf(true, _page2());
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: children,
    );
  }

  Future<void> _submit() async {
    final ret = await FutureProgress<bool>(
      future: controller.submit(),
    ).dialog<bool>();
    if (true == ret) {
      Get.back();
    }
  }

  Iterable<Widget> _page1() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.title(
        titleText: '常溫',
      ),
    );
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，常溫宅配',
        value: controller.draft.ambStatus.switcher.isOn,
        onChanged: (value) {
          controller.draft.ambStatus = value.switcher.index;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      controller.draft.ambStatus.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        labelText: '能下幾天內的訂單',
        hintText: '請輸入數字',
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        keyboardType: TextInputType.number,
        initialValue: controller.draft.displayAmbPreOrderWithinDays,
        onChanged: (value) {
          controller.draft.ambPreOrderWithinDays =
              num.tryParse(value ?? '') ?? 0;
        },
      ),
    );
    children.addIf(
      controller.draft.ambStatus.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        labelText: '取貨日需提前幾天',
        hintText: '請輸入數字',
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        keyboardType: TextInputType.number,
        initialValue: controller.draft.displayAmbPreOrderAfterDays,
        onChanged: (value) {
          controller.draft.ambPreOrderAfterDays =
              num.tryParse(value ?? '') ?? 0;
        },
      ),
    );
    children.addIf(
      controller.draft.ambStatus.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        initialValue: '${controller.draft.ambShippingFee.round()}',
        labelText: '常溫配送運費',
        hintText: '請輸入數字',
        keyboardType: TextInputType.number,
        onChanged: (value) {
          controller.draft.ambShippingFee = int.tryParse(value) ?? 0;
        },
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
        validator: (value) {
          if (value.isNum) {
            return null;
          }
          return '輸入數字';
        },
      ),
    );
    children.addIf(
      controller.draft.ambStatus.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        initialValue: '${controller.draft.ambShippingFree.round()}',
        labelText: '常溫免運費門檻金額',
        hintText: '請輸入數字',
        keyboardType: TextInputType.number,
        onChanged: (value) {
          controller.draft.ambShippingFree = int.tryParse(value) ?? 0;
        },
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
        validator: (value) {
          if (value.isNum) {
            return null;
          }
          return '輸入數字';
        },
      ),
    );
    children.addIf(
      controller.draft.ambStatus.switcher.isOn,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
          initialValue: controller.draft.ambDescription,
          titleText: '店家備註',
          hintText: '請輸入200字內文字',
          onChanged: (value) {
            controller.draft.ambDescription = value;
          },
        ),
      ),
    );
    return children;
  }

  Iterable<Widget> _page2() {
    final children = <Widget>[];
    children.addIf(true, SettingsWidget.title(titleText: '低溫'));
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，低溫宅配',
        value: controller.draft.coldStatus.switcher.isOn,
        onChanged: (value) {
          controller.draft.coldStatus = value.switcher.index;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      controller.draft.coldStatus.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        labelText: '能下幾天內的訂單',
        hintText: '請輸入數字',
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        keyboardType: TextInputType.number,
        initialValue: controller.draft.displayColdPreOrderWithinDays,
        onChanged: (value) {
          controller.draft.coldPreOrderWithinDays =
              num.tryParse(value ?? '') ?? 0;
        },
      ),
    );
    children.addIf(
      controller.draft.coldStatus.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        labelText: '取貨日需提前幾天',
        hintText: '請輸入數字',
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        keyboardType: TextInputType.number,
        initialValue: controller.draft.displayColdPreOrderAfterDays,
        onChanged: (value) {
          controller.draft.coldPreOrderAfterDays =
              num.tryParse(value ?? '') ?? 0;
        },
      ),
    );
    children.addIf(
      controller.draft.coldStatus.switcher.isOn,
      ListTile(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        tileColor: Colors.white,
        title: const Text(
          '當消費者常溫與低溫商品混合下單時，以低溫宅配方式計算',
          style: const TextStyle(
            fontSize: 14,
            color: const Color(0xffe00707),
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
    children.addIf(
      controller.draft.coldStatus.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        initialValue: '${controller.draft.coldShippingFee.round()}',
        labelText: '低溫配送運費',
        hintText: '請輸入數字',
        keyboardType: TextInputType.number,
        onChanged: (value) {
          controller.draft.coldShippingFee = int.tryParse(value) ?? 0;
        },
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
        validator: (value) {
          if (value.isNum) {
            return null;
          }
          return '輸入數字';
        },
      ),
    );
    children.addIf(
      controller.draft.coldStatus.switcher.isOn,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        initialValue: '${controller.draft.coldShippingFree.round()}',
        labelText: '低溫免運費門檻金額',
        hintText: '請輸入數字',
        keyboardType: TextInputType.number,
        onChanged: (value) {
          controller.draft.coldShippingFree = int.tryParse(value) ?? 0;
        },
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
        validator: (value) {
          if (value.isNum) {
            return null;
          }
          return '輸入數字';
        },
      ),
    );
    children.addIf(
      controller.draft.coldStatus.switcher.isOn,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
          initialValue: controller.draft.coldDescription,
          titleText: '店家備註',
          hintText: '請輸入200字內文字',
          onChanged: (value) {
            controller.draft.coldDescription = value;
          },
        ),
      ),
    );
    return children;
  }
}
