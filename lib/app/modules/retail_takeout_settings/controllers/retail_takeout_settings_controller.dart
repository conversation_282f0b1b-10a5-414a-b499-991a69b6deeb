import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/models/shipping_instore.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';

class RetailTakeoutSettingsController extends GetxController
    with StateMixin<String> {
  final SettingProvider settingProvider;
  ApiProvider get apiProvider => settingProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  final _draft = Rx<ShippingInstore>(null);
  ShippingInstore get draft => _draft.value;

  RetailTakeoutSettingsController({
    @required this.settingProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {}

  Future<void> onRefresh() async {
    try {
      _draft.value = await settingProvider.getShippingInstore();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  void refreshData() {
    _draft.refresh();
  }

  Future<bool> submit() async {
    final ret = await settingProvider.putShippingInstore(_draft.value);
    if (ret is num && ret > 0) {
      // 儲存成功，更新本地設定
      // 這裡沒有本地設定
      return true;
    }
    return false;
  }
}

extension ExtensionShippingInstore on ShippingInstore {
  String get displayPreOrderAfterDays {
    preOrderAfterDays ??= 0;
    if (preOrderAfterDays is num && preOrderAfterDays > 0) {
      return preOrderAfterDays.toString();
    }
    return '';
  }

  String get displayPreOrderWithinDays {
    preOrderWithinDays ??= 0;
    if (preOrderWithinDays is num && preOrderWithinDays > 0) {
      return preOrderWithinDays.toString();
    }
    return '';
  }
}
