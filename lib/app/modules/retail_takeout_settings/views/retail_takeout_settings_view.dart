import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/retail_takeout_settings_controller.dart';

class RetailTakeoutSettingsView
    extends GetView<RetailTakeoutSettingsController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.focusScope.unfocus();
      },
      child: StandardPage(
        titleText: '零售商店到店自取設定',
        child: controller.obx((state) {
          return BottomWidgetPage.save(
            child: Obx(() => _main()),
            onPressed: _submit,
          );
        }),
      ),
    );
  }

  Future<void> _submit() async {
    final ret = await FutureProgress<bool>(
      future: controller.submit(),
    ).dialog();
    if (true == ret) {
      Get.back();
    }
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      const Text(
        '到店自取',
        style: const TextStyle(
          fontSize: 16,
          color: kColorPrimary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ).paddingSymmetric(
        vertical: 12.0,
      ),
    );
    children.addIf(
      true,
      Expanded(child: _list()),
    );
    return Column(children: children);
  }

  Widget _list() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，到店自取',
        value: Switcher.On.index == controller.draft.status,
        onChanged: (value) {
          controller.draft.status = value.switcher.index;
          controller.refreshData();
        },
      ),
    );
    children.addIf(
      Switcher.On.index == controller.draft.status,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        labelText: '能下幾天內的訂單',
        hintText: '請輸入數字',
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        keyboardType: TextInputType.number,
        initialValue: controller.draft.displayPreOrderWithinDays,
        onChanged: (value) {
          controller.draft.preOrderWithinDays = num.tryParse(value ?? '') ?? 0;
        },
      ),
    );
    children.addIf(
      Switcher.On.index == controller.draft.status,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        labelText: '取貨日需提前幾天',
        hintText: '請輸入數字',
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        keyboardType: TextInputType.number,
        initialValue: controller.draft.displayPreOrderAfterDays,
        onChanged: (value) {
          controller.draft.preOrderAfterDays = num.tryParse(value ?? '') ?? 0;
        },
      ),
    );
    children.addIf(
      Switcher.On.index == controller.draft.status,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
          titleText: '店家備註',
          hintText: '請輸入200字內文字',
          initialValue: controller.draft.description,
          onChanged: (value) {
            controller.draft.description = value;
          },
        ).paddingOnly(top: 12),
      ),
    );
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: children,
    );
  }
}
