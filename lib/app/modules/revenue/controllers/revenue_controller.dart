import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/revenue_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

class RevenueController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final RevenueProvider revenueProvider;
  final _disposable = Completer();
  final _filter = RevenueReq().obs;
  final _dateTime = DateTime.now().obs;
  final _page = RevenuePage().obs;

  Iterable<Revenue> get data => _page.value.data ?? [];

  DateTime get dateTime => _dateTime.value;
  set dateTime(DateTime value) {
    _dateTime.value = value;
    _filter.value.date = value.yMd;
    _filter.value.page = 1;
    logger.d('[RevenueController] set dateTime(${_filter.value.date})');
    _filter.refresh();
  }

  String get total => (_page.value?.total ?? 0).decimalStyle;

  num get needToLoadMore {
    final ret = _page.value?.pagination?.needToLoadMore ?? false;
    return ret ? 1 : 0;
  }

  RevenueController({
    @required this.revenueProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _filter.stream
        .asBroadcastStream()
        .tap((event) {
          logger.d('[RevenueController] tap(${event.date})');
          change('', status: RxStatus.loading());
        })
        .debounce(500.milliseconds)
        .where((event) {
          // cache 存在則直接使用
          final key = event.date.hashCode;
          if (1 == event.page && revenueProvider.cached.containsKey(key)) {
            logger.d('[RevenueController] use cache(${event.date})');
            _page.value = revenueProvider.cached[key];
            _refreshPage();
            return false;
          }
          return true;
        })
        .asyncMap((event) {
          logger.d('[RevenueController] asyncMap(${event.date})');
          return onRefresh();
        })
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    this.dateTime = DateTime.now();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  void next() {
    dateTime = dateTime.add(1.days);
  }

  void pre() {
    dateTime = dateTime.subtract(1.days);
  }

  ///
  /// 這裡呼叫的增加
  ///
  @override
  Future<void> onEndScroll() async {
    logger.d('[RevenueController] onEndScroll');
    if (needToLoadMore > 0) {
      try {
        _filter.value.page = _page.value.pagination.nextPage;
        _page.value = await revenueProvider.getRevenues(_filter.value);
      } catch (e) {
        // on end scroll 不顯示錯誤
        // change('', status: RxStatus.error('$error'));
        // 錯誤，標示為最後一頁
        _page.value.pagination.setLastPage();
      } finally {
        _refreshPage();
      }
    }
  }

  void _refreshPage() {
    change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
  }

  @override
  Future<void> onTopScroll() async {
    logger.d('[RevenueController] onTopScroll');
  }

  ///
  /// 這裡呼叫的先清空再增加
  ///
  Future<void> onRefresh() async {
    logger.d('[RevenueController] onRefresh(${_filter.value.date})');
    try {
      // 重新整理: 移除 cache，避免不斷傳回 cache
      final key = _filter.value.date.hashCode;
      revenueProvider.cached.remove(key);
      // 重新整理: 一定是第一頁
      _filter.value.page = 1;
      _page.value = await revenueProvider.getRevenues(_filter.value);
      _refreshPage();
    } catch (e) {
      // 首讀可顯示錯誤頁面
      change('', status: RxStatus.error('$e'));
    }
  }
}
