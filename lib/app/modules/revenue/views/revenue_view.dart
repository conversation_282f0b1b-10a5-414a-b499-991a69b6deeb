import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/date_banner.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/underline_divider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

import '../controllers/revenue_controller.dart';

class RevenueView extends GetView<RevenueController> {
  const RevenueView({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '每日營收',
      child: _main(),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      UnderlineDivider(
        insets: EdgeInsets.zero,
        backgroundColor: Colors.transparent,
        child: DateBanner(
          initialDate: controller.dateTime,
          dateChanged: (value) {
            controller.dateTime = value;
          },
        ),
      ),
    );
    children.addIf(
      true,
      Expanded(
        child: controller.obx(
          (state) => _list(),
          onEmpty: Column(
            children: [
              _banner(),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: controller.onRefresh,
                  child: ListWidget.blank(),
                ),
              ),
            ],
          ),
          onError: (message) {
            return RefreshIndicator(
              onRefresh: controller.onRefresh,
              child: ListWidget.message(message),
            );
          },
        ),
      ),
    );
    return Column(children: children);
  }

  Widget _list() {
    final children = <Widget>[];
    children.addIf(true, _banner());
    children.addIf(true, _header());
    children.addIf(true, Expanded(child: _page()));
    return Column(children: children);
  }

  Widget _page() {
    return ColoredBox(
      color: Colors.white,
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: Obx(() {
          // 移除重複
          final entries = controller.data.map((e) => MapEntry(e.id, e));
          final list = Map.fromEntries(entries).values.toList(growable: false);
          // desc sort
          list.sort((x, y) =>
              (y.orderSerialNumber ?? 0).compareTo(x.orderSerialNumber ?? 0));
          return ListView.separated(
            physics: const AlwaysScrollableScrollPhysics(),
            controller: controller.scroll,
            itemCount: list.length + controller.needToLoadMore,
            itemBuilder: (context, index) {
              if (index < list.length) {
                final data = list.elementAt(index);
                return _Item(
                  data: data,
                  onPressed: () {
                    Get.toNamed(
                      Routes.ORDER_DETAIL,
                      parameters: <String, String>{
                        Keys.Tag: '${data.id}',
                        Keys.Id: '${data.id}',
                      },
                    );
                  },
                );
              }
              controller.onEndScroll();
              return ListWidget.bottomProgressing();
            },
            separatorBuilder: (context, index) {
              return Divider(
                height: 1.0,
                indent: kPadding,
                endIndent: kPadding,
              );
            },
          );
        }),
      ),
    );
  }

  Widget _banner() {
    return ListTile(
      contentPadding: kContentPadding,
      leading: SvgPicture.asset('assets/images/icon_currency.svg'),
      title: Text(
        '當日營業總額',
        style: TextStyle(
          fontSize: 16,
          color: const Color(0xff333333),
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.left,
      ),
      trailing: Obx(() {
        return Text(
          // '\$88888',
          '\$${controller.total}',
          style: TextStyle(
            fontSize: 30,
            color: kColorPrimary,
          ),
          textAlign: TextAlign.left,
        );
      }),
    );
  }

  Widget _header() {
    final children = <Widget>[];
    children.addIf(true, SizedBox(width: kPadding));
    children.addIf(
      true,
      Expanded(
        child: const Text(
          '訂單編號',
          style: const TextStyle(
            fontSize: 14,
            color: const Color(0xff6d7278),
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
    children.addIf(
      true,
      Text(
        '金額',
        style: const TextStyle(
          fontSize: 14,
          color: const Color(0xff6d7278),
        ),
      ),
    );
    children.addIf(true, SizedBox(width: kPadding));
    children.addIf(true, SizedBox(width: kPadding));

    return UnderlineDivider(
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: children,
      ).paddingOnly(
        top: 8,
        bottom: 4,
      ),
    );
  }
}

class _Item extends StatelessWidget {
  final Revenue data;
  final Function onPressed;

  const _Item({
    Key key,
    @required this.data,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: this.onPressed,
      contentPadding: kContentPadding,
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            // 'ts20210985 ',
            this.data.orderNumber ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
            textAlign: TextAlign.left,
          ),
          const SizedBox(
            width: 4.0,
          ),
          DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(const Radius.circular(25.0)),
              color: this.data.displayColor,
            ),
            child: Text(
              // '內用',
              this.data.displayType ?? '',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ).paddingSymmetric(
              horizontal: 8.0,
              vertical: 4.0,
            ),
          ),
          const SizedBox(
            width: 4,
          ),
          // FIXME: layout overflow
          Visibility(
            visible: this.data.source.orderSource.isLine,
            child: Text(
              'LINE下單',
              style: TextStyle(
                fontSize: 13,
                color: OrderSource.Line.color,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            // '\$820',
            '\$${(this.data.total ?? 0).decimalStyle}',
            style: const TextStyle(
              fontSize: 18,
              color: kColorPrimary,
            ),
            textAlign: TextAlign.right,
          ),
          const Icon(
            Icons.keyboard_arrow_right,
          ),
        ],
      ),
    );
  }
}
