import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/local_settings.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/extension.dart';

class SettingAutoPrintController extends GetxController {
  final PrefProvider prefProvider;

  final _draft = Rx<AutoPrintSettings>(null);
  AutoPrintSettings get draft => _draft.value;

  final _storeType = StoreType.Dinner.obs;
  StoreType get storeType => _storeType.value;

  String get switcherName {
    if (storeType.isRetail) {
      return '開啟列印${storeType.name}的訂單';
    }
    return '開啟列印「線上點餐」完的訂單';
  }

  SettingAutoPrintController({
    @required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey('type')) {
      final type = int.tryParse(Get.parameters['type']);
      _storeType.value = type.storeType;
    }
    final json = prefProvider.localSettings.toJson();
    _draft.value = AutoPrintSettings(
      type: storeType,
      localSettings: LocalSettings.fromJson(json),
    );
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    //
  }

  Future<bool> submit() async {
    prefProvider.localSettings = draft.localSettings;
    return true;
  }

  void refreshDraft() {
    _draft.refresh();
  }
}

class AutoPrintSettings {
  final StoreType type;
  final LocalSettings localSettings;

  AutoPrintSettings({
    @required this.type,
    @required this.localSettings,
  });

  AutoPrint get _autoPrint => localSettings.getAutoPrint(type);

  bool get autoPrintEnabled {
    if (type == StoreType.Dinner) {
      _autoPrint.enabled = localSettings.autoPrintReceiptEnabled;
    }
    return _autoPrint.enabled?.switcher?.isOn ?? false;
  }

  set autoPrintEnabled(bool value) {
    if (type == StoreType.Dinner) {
      localSettings.autoPrintReceiptEnabled = value.switcher.index;
    }
    _autoPrint.enabled = value.switcher.index;
  }

  bool get autoPrintReceipt {
    if (type == StoreType.Dinner) {
      _autoPrint.receipt = localSettings.autoPrintReceipt;
    }
    return _autoPrint.receipt?.switcher?.isOn ?? false;
  }

  set autoPrintReceipt(bool value) {
    if (type == StoreType.Dinner) {
      localSettings.autoPrintReceipt = value.switcher.index;
    }
    _autoPrint.receipt = value.switcher.index;
  }

  bool get autoPrintSticker {
    if (type == StoreType.Dinner) {
      _autoPrint.sticker = localSettings.autoPrintSticker;
    }
    return _autoPrint.sticker?.switcher?.isOn ?? false;
  }

  set autoPrintSticker(bool value) {
    if (type == StoreType.Dinner) {
      localSettings.autoPrintSticker = value.switcher.index;
    }
    _autoPrint.sticker = value.switcher.index;
  }
}
