import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/setting_auto_print_controller.dart';

class SettingAutoPrintView extends GetView<SettingAutoPrintController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '${controller.storeType.name}自動列印設定',
      child: BottomWidgetPage.save(
        child: Obx(
          () => Column(
            children: _children().toList(growable: false),
          ),
        ),
        onPressed: _submit,
      ),
    );
  }

  void _submit() {
    FutureProgress(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (true == value) {
          Get.back();
        }
      },
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(height: 36);
    // 總開關
    yield SettingsWidget.switcher(
      titleText: controller.switcherName,
      value: controller.draft.autoPrintEnabled,
      onChanged: (value) {
        controller.draft.autoPrintEnabled = value;
        controller.refreshDraft();
      },
    );
    // 貼紙
    if (controller.draft.autoPrintEnabled == true) {
      yield SettingsWidget.switcher(
        titleText: '列印熱感紙「${controller.storeType.receipt}明細」',
        value: controller.draft.autoPrintReceipt,
        onChanged: (value) {
          controller.draft.autoPrintReceipt = value;
          controller.refreshDraft();
        },
        contentPadding: EdgeInsets.only(
          left: kPadding * 2.0,
          right: kPadding,
        ),
      );
    }
    // 明細
    if (controller.draft.autoPrintEnabled == true) {
      yield SettingsWidget.switcher(
        titleText: '列印標籤機「工作單」',
        value: controller.draft.autoPrintSticker,
        onChanged: (value) {
          controller.draft.autoPrintSticker = value;
          controller.refreshDraft();
        },
        contentPadding: EdgeInsets.only(
          left: kPadding * 2.0,
          right: kPadding,
        ),
      );
    }
    // space
    yield SettingsWidget.space();
  }
}
