import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_common/okshop_common.dart';

import '../controllers/setting_dinner_online_here_controller.dart';

class SettingDinnerOnlineHereView
    extends GetView<SettingDinnerOnlineHereController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '餐飲線上預約內用設定',
      child: controller.obx(
        (state) {
          return BottomWidgetPage.save(
            child: Obx(() => _main()),
            onPressed: _submit,
          );
        },
      ),
    );
  }

  Future<void> _submit() async {
    try {
      final ret = await FutureProgress<bool>(
        future: controller.submit(),
      ).dialog();
      if (true == ret) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
        true, SettingsWidget.title(titleText: Get.parameters['name']));
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用網路商店下單，' + Get.parameters['name'],
        value: Switcher.On.index == controller.draft.status,
        onChanged: (value) {
          controller.draft.status = value.switcher.index;
          controller.refreshDraft();
        },
      ),
    );
    children.addIf(
      // Switcher.On.index == controller.draft.status,
      false,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        labelText: '能下幾天內的訂單',
        hintText: '請輸入數字',
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        keyboardType: TextInputType.number,
        initialValue: '${controller.draft.preOrderWithinDays ?? 0}',
        onChanged: (value) {
          controller.draft.preOrderWithinDays = int.tryParse(value ?? '') ?? 0;
        },
      ),
    );
    children.addIf(
      Switcher.On.index == controller.draft.status,
      SettingsWidget.input(
        contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
        labelText: '用餐時間需提前幾分鐘下單',
        hintText: '請輸入數字',
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly
        ],
        keyboardType: TextInputType.number,
        initialValue: '${controller.draft.preOrderAfterMinute ?? 0}',
        onChanged: (value) {
          controller.draft.preOrderAfterMinute = int.tryParse(value ?? '') ?? 0;
        },
      ),
    );
    children.addIf(
      Switcher.On.index == controller.draft.status,
      ColoredBox(
        color: Colors.white,
        child: SettingsWidget.comment(
          contentPadding: EdgeInsets.only(left: kPadding * 2, right: kPadding),
          titleText: '店家備註',
          hintText: '請輸入200字內文字',
          initialValue: controller.draft.description,
          onChanged: (value) {
            controller.draft.description = value;
          },
        ),
      ),
    );
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: children,
    );
  }
}
