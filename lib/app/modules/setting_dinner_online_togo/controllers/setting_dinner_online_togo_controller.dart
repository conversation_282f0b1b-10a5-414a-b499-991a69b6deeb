import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/models/setting_line_order.dart';
import 'package:muyipork/app/models/setting_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';

class SettingDinnerOnlineTogoController extends GetxController
    with StateMixin<String> {
  final SettingProvider settingProvider;
  ApiProvider get apiProvider => settingProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  final _data = Rx<SettingGetRes>(null);
  SettingGetRes get data => _data.value;
  final _draft = Rx<DinerOnline>(null);
  DinerOnline get draft => _draft.value;

  SettingDinnerOnlineTogoController({
    @required this.settingProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {}

  Future<void> onRefresh() async {
    try {
      _data.value = await apiProvider.getSetting(updateCache: true);
      _draft.value = data.nnData.nnOther.nnLineOrder.nnDinerOnlineTogo;
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  void refreshDraft() {
    _draft.refresh();
  }

  Future<bool> submit() async {
    final ret = await apiProvider.putSetting(data.data);
    return true == ret.isUpdated;
  }
}
