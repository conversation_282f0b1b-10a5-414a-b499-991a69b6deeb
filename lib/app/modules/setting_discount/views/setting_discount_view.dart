import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/composite_edit_item.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/setting_discount_controller.dart';

class SettingDiscountView extends GetView<SettingDiscountController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: controller.titleText,
      child: controller.obx((state) {
        return BottomWidgetPage.save(
          child: Obx(() => _main()),
          onPressed: _submit,
        );
      }),
    );
  }

  Future<void> _submit() async {
    try {
      final ret = await FutureProgress(future: controller.submit()).dialog();
      if (ret == true) {
        Get.back();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Widget _main() {
    final list = controller.data;
    final children = List<Widget>.generate(list.length + 1, (index) {
      if (index < list.length) {
        final element = list.elementAt(index);
        return CompositeEditItem(
          CompositeEditItemArgs(
            CompositeContentMode.TextFieldAndNumField,
            CompositeRightButtonMode.Remove,
            mainTextFieldInit: element.title,
            mainTextFieldHint: '選項名稱',
            secondaryTextFieldInit: element.displayPromotionValue,
            secondaryTextFieldHint: element.displayPromotionTypeHint,
            mainTextFieldChanged: (value) {
              element.title = value;
              controller.updating(element.id);
            },
            secondaryTextFieldChanged: (value) {
              element.promotionValue = num.tryParse(value ?? '') ?? 0;
              controller.updating(element.id);
            },
            onRightButtonPressed: () async {
              // 刪除
              try {
                return await controller.deleting(element.id);
              } catch (e) {
                return DialogGeneral.alert('$e').dialog();
              }
            },
          ),
          key: Key('$index'),
        );
      }
      return CompositeEditItem(
        CompositeEditItemArgs(
          CompositeContentMode.TextFieldAndNumField,
          CompositeRightButtonMode.Add,
          mainTextFieldHint: '選項名稱',
          secondaryTextFieldHint: controller.creating.displayPromotionTypeHint,
          mainTextFieldChanged: (value) {
            controller.creating.title = value;
          },
          secondaryTextFieldChanged: (value) {
            controller.creating.promotionValue = num.tryParse(value ?? '') ?? 0;
          },
          onRightButtonPressed: () {
            final name = controller.creating.title;
            if (name == null || name.isEmpty) {
              return DialogGeneral.alert('選項名稱是必填項目').dialog();
            }
            // return controller.applyCreating();
            return FutureProgress(
              future: controller.submit(),
            ).dialog();
          },
          showDragHandle: false,
        ),
        key: Key('$index'),
      );
    });

    return ReorderableListView(
      padding: EdgeInsets.only(
        top: kPadding,
        bottom: kBottomButtonPadding,
      ),
      children: children,
      onReorder: (int srcIndex, int destIndex) {
        // asc (0, 2)
        // desc (1, 0)
        controller.sorting(srcIndex, destIndex);
      },
    );
  }
}
