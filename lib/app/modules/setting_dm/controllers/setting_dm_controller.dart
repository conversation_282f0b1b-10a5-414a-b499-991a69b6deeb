import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/brands_dm.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

class SettingDmController extends GetxController with StateMixin<String> {
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  final _storeType = StoreType.Max.obs;
  StoreType get storeType => _storeType.value;
  final list = <BrandsDm>[].obs;
  final _data = Rx<BrandsDm>(null);
  BrandsDm get data => _data.value;

  void refreshData() => _data.refresh();

  SettingDmController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _storeType.value = Get.arguments;
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {}

  Future<void> onRefresh() async {
    try {
      // final ls = await apiProvider.getList<BrandsDm>(
      //   unencodedPath: 'brands/dm',
      //   creator: (value) => BrandsDm.fromJson(value),
      // );
      final ls = await apiProvider.getData<Iterable<BrandsDm>>(
        unencodedPath: 'brands/dm',
        creator: (json) {
          if (json is Map && json.containsKey(Keys.Data)) {
            final jsonArray = json[Keys.Data];
            return List.from(jsonArray).map((e) => BrandsDm.fromJson(e));
          }
          return Iterable<BrandsDm>.empty();
        },
      );
      list.clear();
      list.add(ls.firstWhere(
        (element) => element.type.storeType == StoreType.Dinner,
        orElse: () => BrandsDm(
          cart: Switcher.On.index,
          createOrder: Switcher.On.index,
          orderList: Switcher.On.index,
          type: StoreType.Dinner.index,
        ),
      ));
      list.add(ls.firstWhere(
        (element) => element.type.storeType == StoreType.Retail,
        orElse: () => BrandsDm(
          cart: Switcher.On.index,
          createOrder: Switcher.On.index,
          orderList: Switcher.On.index,
          type: StoreType.Retail.index,
        ),
      ));
      _data.value = list.firstWhere(
        (element) => element.type.storeType == storeType,
        orElse: () => BrandsDm(
          cart: Switcher.On.index,
          createOrder: Switcher.On.index,
          orderList: Switcher.On.index,
          type: storeType.index,
        ),
      );
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit() {
    return apiProvider.put(
      'brands/dm',
      data: <String, dynamic>{
        // 'array': jsonEncode(list),
        'array': jsonEncode({
          'data': list,
        }),
      },
      creator: (data) {
        if (data.containsKey(Keys.IsUpdated) && true == data[Keys.IsUpdated]) {
          return true;
        }
        return null;
      },
    );
  }
}
