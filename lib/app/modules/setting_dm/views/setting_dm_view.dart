import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

import '../controllers/setting_dm_controller.dart';

class SettingDmView extends GetView<SettingDmController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      backgroundColor: controller.storeType.color,
      // titleText: '餐飲DM模式',
      titleText: controller.storeType.titleText,
      child: controller.obx(
        (state) {
          return BottomWidgetPage.save(
            child: RefreshIndicator(
              onRefresh: controller.onRefresh,
              child: _main(),
            ),
            onPressed: _submit,
          );
        },
        onError: ListWidget.message,
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, const SizedBox(height: 36.0));
    children.addIf(
      true,
      Obx(() {
        return SettingsWidget.switcher(
          titleText: '隱藏LINE下單「加入購物車」按鈕',
          value: controller.data.cart.switcher.isOff,
          onChanged: (value) {
            controller.data.cart = (!value).switcher.index;
            controller.refreshData();
          },
        );
      }),
    );
    children.addIf(
      true,
      Container(
        padding: kContentPadding,
        decoration: BoxDecoration(
          color: Colors.white,
        ),
        child: Text(
          '*LINE會員可觀看線上商品，僅無法下單',
          style: TextStyle(
            fontSize: 14,
            color: OKColor.Must,
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
    children.addIf(
      true,
      Obx(() {
        return SettingsWidget.switcher(
          // titleText: '隱藏LINE會員中心「立即點餐」按鈕',
          titleText: controller.storeType.createOrderText,
          value: controller.data.createOrder.switcher.isOff,
          onChanged: (value) {
            controller.data.createOrder = (!value).switcher.index;
            controller.refreshData();
          },
        );
      }),
    );
    children.addIf(
      true,
      Obx(() {
        return SettingsWidget.switcher(
          // titleText: '隱藏LINE會員中心「消費記錄」按鈕',
          titleText: controller.storeType.orderListText,
          value: controller.data.orderList.switcher.isOff,
          onChanged: (value) {
            controller.data.orderList = (!value).switcher.index;
            controller.refreshData();
          },
        );
      }),
    );
    children.addIf(true, const SizedBox(height: kBottomButtonPadding));
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: children,
    );
  }

  ///
  /// 儲存
  ///
  void _submit() {
    FutureProgress(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (value != null) {
          Get.back();
        }
      },
    );
  }
}

extension _ExtensionStoreType on StoreType {
  String get titleText {
    switch (this) {
      case StoreType.Dinner:
        return '餐飲DM模式';
      case StoreType.Retail:
        return '零售DM模式';
      default:
        return '';
    }
  }

  String get createOrderText {
    switch (this) {
      case StoreType.Dinner:
        return '隱藏LINE會員中心「立即點餐」按鈕';
      case StoreType.Retail:
        return '隱藏LINE會員中心「零售商店」按鈕';
      default:
        return '';
    }
  }

  String get orderListText {
    switch (this) {
      case StoreType.Dinner:
        return '隱藏LINE會員中心「消費記錄」按鈕';
      case StoreType.Retail:
        return '隱藏LINE會員中心「訂單紀錄」按鈕';
      default:
        return '';
    }
  }
}
