import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/paymethod_item.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/models/setting_pay.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:reorderables/reorderables.dart';

import '../controllers/setting_pay_controller.dart';

class SettingPayView extends GetView<SettingPayController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      actions: _actions(),
      titleText: '支付方式設定',
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: controller.obx(
          (state) {
            return BottomWidgetPage.save(
              child: _main(),
              onPressed: _submit,
            );
          },
          onError: ListWidget.message,
        ),
      ),
    );
  }

  List<Widget> _actions() {
    final children = <Widget>[];
    children.add(
      TextButton(
        onPressed: () {
          Get.toNamed(Routes.SETTING_PAY_EDITOR);
        },
        child: Text(
          '編輯',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
      ),
    );
    return children;
  }

  void _submit() {
    FutureProgress<bool>(
      future: controller.submit(),
    ).dialog<bool>().then(
      (value) {
        if (true == value) {
          Get.back();
        }
      },
    );
  }

  void _onReorder(int oldIndex, int newIndex) {
    logger.d('_onReorder oldIndex($oldIndex), newIndex($newIndex)');
    final list = [...controller.draft];
    final item = list.elementAt(oldIndex);
    list.removeAt(oldIndex);
    list.insert(newIndex, item);
    for (var i = 0; i < list.length; i++) {
      list.elementAt(i).sort = i;
    }
    controller.draft.assignAll(list);
  }

  Widget _main() {
    final children = <Widget>[];
    children.add(SizedBox(height: 12));
    children.add(Center(child: _header()));
    children.add(SizedBox(height: 12));
    children.add(
      Expanded(
        child: ColoredBox(
          color: Colors.white,
          child: _list(),
        ),
      ),
    );
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _reorderList() {
    final list = [...controller.draft];
    list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    return ReorderableSliverList(
      // delegate: ReorderableSliverChildListDelegate(
      //   List.generate(
      //     controller.draft.length,
      //     (index) {
      //       final data = controller.draft.elementAt(index);
      //       return ReorderableWidget(
      //         key: ValueKey(data.id),
      //         reorderable: true,
      //         child: _item(data),
      //       );
      //     },
      //   ),
      // ),
      // or use ReorderableSliverChildBuilderDelegate if needed
      delegate: ReorderableSliverChildBuilderDelegate(
        (context, index) {
          final element = list.elementAt(index);
          return ReorderableWidget(
            key: ValueKey(element.id),
            reorderable: true,
            child: _item(element),
          );
        },
        childCount: list.length,
      ),
      onReorder: _onReorder,
    );
  }

  Widget _list() {
    final children = <Widget>[];
    children.add(Obx(() => _reorderList()));
    children.add(SizedBox(height: kBottomButtonPadding).sliverBox);
    return CustomScrollView(slivers: children);
    // return ReorderableFlex(
    //   scrollController: controller.scroll,
    //   padding: EdgeInsets.only(
    //     bottom: kBottomButtonPadding,
    //   ),
    //   direction: Axis.vertical,
    //   needsLongPressDraggable: false,
    //   onReorder: _onReorder,
    //   children: List.generate(
    //     controller.draft.length,
    //     (index) {
    //       final data = controller.draft.elementAt(index);
    //       return ReorderableWidget(
    //         key: ValueKey(data.id),
    //         reorderable: true,
    //         child: _item(data),
    //       );
    //     },
    //   ),
    // );
    // return ReorderableListView.builder(
    //   padding: EdgeInsets.only(
    //     bottom: kBottomButtonPadding,
    //   ),
    //   buildDefaultDragHandles: true,
    //   dragStartBehavior: DragStartBehavior.start,
    //   // header: Center(child: _header()),
    //   onReorder: _onReorder,
    //   itemCount: controller.editingData.length,
    //   itemBuilder: (context, index) {
    //     final data = controller.editingData.elementAt(index);
    //     return _item(data);
    //   },
    // );
  }

  Widget _item(SettingPay data) {
    return ListTile(
      // key: ValueKey(data.id),
      leading: Icon(Icons.drag_handle),
      title: Align(
        alignment: Alignment.centerLeft,
        child: PaymethodItem(
          key: ValueKey(data.id),
          title: data.name,
          image: data.payMethodId.appPayMethod.icon,
          showTitle: data.isCustom ?? false,
        ),
      ),
    );
  }

  Widget _header() {
    final children = <Widget>[];
    children.add(
      Text(
        '長按',
        style: TextStyle(
          fontSize: 16,
          color: OKColor.Primary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
    );
    children.add(Icon(Icons.drag_handle));
    children.add(
      Text(
        '可以調整排序',
        style: TextStyle(
          fontSize: 16,
          color: OKColor.Primary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
