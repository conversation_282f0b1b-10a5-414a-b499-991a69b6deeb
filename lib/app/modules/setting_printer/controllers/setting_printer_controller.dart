import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:okshop_model/okshop_model.dart';

class SettingPrinterController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final PrinterProvider printerProvider;
  final data = <SettingLabel>[].obs;
  final _containsCloud = false.obs;
  bool get containsCloud => _containsCloud.value;

  SettingPrinterController({
    @required this.printerProvider,
  });

  @override
  void onInit() {
    super.onInit();
    final switcher =
        num.tryParse(Get.parameters['cloud'] ?? '') ?? Switcher.Off.index;
    _containsCloud.value = switcher.switcher.isOn;
    data.stream
        .debounce(200.milliseconds)
        .takeUntil(_disposable.future)
        .listen((value) {
      final status = data.isNotEmpty ? RxStatus.success() : RxStatus.empty();
      change('', status: status);
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      final asyncValueGetter = containsCloud
          ? printerProvider.getSettingPrinters
          : printerProvider.getSettingLabels;
      final ls = await asyncValueGetter();
      final it = ls.where((element) =>
          // 雲印表機不需要過濾
          containsCloud ? true : PrinterType.all.contains(element.type));
      final printers = it.distinct((element) => element.uuid);
      data.assignAll(printers);
      addAll(it);
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  void delete(SettingLabel printer) {
    final index = data.indexWhere((element) => printer.uuid == element.uuid);
    if (index >= 0) {
      data.removeAt(index);
    }
  }

  void addAll(Iterable<SettingLabel> printers) {
    printers ??= <SettingLabel>[];
    final ls = [...data, ...printers];
    final it = ls.distinct((element) => element.uuid);
    data.assignAll(it);
  }

  void reload(SettingLabel printer) {
    final index = data.indexWhere((element) => printer.uuid == element.uuid);
    if (index >= 0) {
      final valueGetter = containsCloud
          ? printerProvider.getCloudPrinter
          : printerProvider.getPrinter;
      data[index] = valueGetter(printer.uuid);
    }
  }

  void loadDataFromStorage() {
    final valueGetter = containsCloud
        ? printerProvider.getSettingPrintersFromStorage
        : printerProvider.getSettingLabelsFromStorage;
    final it = valueGetter().where((element) =>
        // 雲印表機不需要過濾
        containsCloud ? true : PrinterType.all.contains(element.type));
    final printers = it.distinct((element) => element.uuid);
    data.assignAll(printers);
  }

  Future<bool> submit() async {
    final it = data.where((element) => element.id == null || element.id <= 0);
    // 使用這個會有錯誤
    // return printerProvider.postSettingLabelsBatch(it);
    final futures = it.map((e) {
      final asyncFunction = containsCloud
          ? printerProvider.postSettingPrinters
          : printerProvider.postSettingLabels;
      return asyncFunction(e);
    });
    final values = await Future.wait(futures);
    return values.every((element) => element);
  }
}
