import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';

import '../controllers/setting_printer_controller.dart';

class SettingPrinterView extends GetView<SettingPrinterController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      actions: _actions().toList(growable: false),
      titleText: controller.containsCloud ? '雲印表機設定' : '印表機設定',
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: controller.obx(
          (state) {
            return BottomWidgetPage.save(
              onPressed: _submit,
              child: Obx(() => _body()),
            );
          },
          onError: (message) {
            return ListWidget.message(
              message,
              buttonText: '重新整理',
              onPressed: controller.onRefresh,
            );
          },
          onEmpty: ListWidget.blank(
            buttonText: '新增',
            onPressed: _onAddPressed,
          ),
        ),
      ),
    );
  }

  Future<void> _submit() async {
    try {
      final value = await FutureProgress(
        future: controller.submit(),
      ).dialog();
      if (true == value) {
        Get.back();
      }
    } catch (e) {
      Get.showAlert('$e');
    }
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: _onAddPressed,
      child: Text(
        '新增',
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
        ),
      ),
    );
  }

  Future<void> _onAddPressed() async {
    final future =
        controller.containsCloud ? _onAddEmptyPressed : _onSearchPressed;
    await future();
  }

  Future<void> _onAddEmptyPressed() async {
    try {
      final printer = SettingLabel();
      await Get.toNamed(
        Routes.PRINTER_DETAIL,
        parameters: <String, String>{
          Keys.Data: printer.toRawJson(),
          'cloud': '${controller.containsCloud.switcher.index}',
        },
      ).then(
        (value) {
          switch (value) {
            case DataAction.Delete:
              controller.delete(printer);
              break;
            case DataAction.Update:
              controller.reload(printer);
              break;
            case DataAction.Create:
              controller.addAll([printer]);
              break;
            default:
          }
        },
      );
    } catch (e) {
      Get.showAlert('$e');
    }
  }

  Future<void> _onSearchPressed() async {
    try {
      final ids = controller.data.map((element) => element.uuid);
      final value = await Get.toNamed(
        Routes.PRINTER_PICKER,
        parameters: {
          Keys.Data: jsonEncode([...ids]),
        },
      );
      if (value is String) {
        final it = List.from(jsonDecode(value))
            .map((json) => SettingLabel.fromJson(json));
        controller.addAll(it);
      }
    } catch (e) {
      Get.showAlert('$e');
    }
  }

  Widget _body() {
    final it = controller.data;
    return ListView.separated(
      padding: EdgeInsets.only(
        top: kPadding,
        bottom: kBottomButtonPadding,
      ),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: it.length,
      itemBuilder: (context, index) {
        final printer = it.elementAt(index);
        return ListTile(
          tileColor: Colors.white,
          title: Text(printer.name ?? ''),
          subtitle: Text(printer.ip ?? ''),
          trailing: const Icon(Icons.navigate_next),
          onTap: () => _toDetail(printer),
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 8);
      },
    );
  }

  Future<void> _toDetail(SettingLabel printer) async {
    try {
      final value = await Get.toNamed(
        Routes.PRINTER_DETAIL,
        parameters: <String, String>{
          Keys.Data: printer.toRawJson(),
          'cloud': '${controller.containsCloud.switcher.index}',
        },
      );
      switch (value) {
        case DataAction.Delete:
          controller.delete(printer);
          break;
        case DataAction.Update:
          controller.reload(printer);
          break;
        case DataAction.Create:
          controller.loadDataFromStorage();
          break;
        default:
      }
    } catch (e) {
      Get.showAlert('$e');
    }
  }
}
