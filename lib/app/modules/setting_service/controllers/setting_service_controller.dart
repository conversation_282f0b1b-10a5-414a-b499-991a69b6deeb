import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/setting_order_fee.dart';
import 'package:muyipork/app/models/setting_get_res.dart';
import 'package:muyipork/app/models/setting_put_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

class SettingServiceController extends GetxController with StateMixin<String> {
  final ApiProvider apiProvider;
  final editing = TextEditingController();
  final _setting = Rx<SettingGetRes>(null);
  final _draft = Rx<SettingOrderFee>(null);

  SettingOrderFee get draft => _draft.value;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  SettingServiceController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    logger.d('[SettingServiceController] onRefresh');
    try {
      _setting.value = await apiProvider.getSetting(updateCache: true);
      _setting.value.data.other.fee ??= SettingOrderFee();
      _draft.value = _setting.value.data.other.fee;
      _draft.value.percent ??= 0;
      _draft.value.type ??= ServiceFeeType.None.index;
      editing.text = '${_draft.value.percent}';
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<SettingPutRes> submit() async {
    logger.d('[SettingServiceController] submit');
    try {
      final ret = await apiProvider.putSetting(_setting.value.data);
      if (ret != null && true == ret.isUpdated) {
        // update local setting
        prefProvider.setting = await apiProvider.getSetting(updateCache: true);
      }
      return ret;
    } catch (e) {
      rethrow;
    }
  }

  void refreshData() {
    _draft.refresh();
  }

  bool validate() {
    try {
      _percentValidate();
      return true;
    } catch (e) {
      rethrow;
    }
  }

  void _percentValidate() {
    try {
      draft.percent ??= 0;
      final value = draft.percent;
      if (!(value >= 0 && value <= 100)) {
        throw '請輸入 0-100';
      }
    } catch (e) {
      rethrow;
    }
  }
}
