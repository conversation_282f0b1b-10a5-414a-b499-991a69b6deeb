import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/modules/settings/controllers/settings_controller.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:okshop_common/okshop_common.dart';

import 'dart:convert';

class _CellModel {
  _CellModel({
    this.name,
    this.visibility,
    this.page,
  });

  String name;
  List<num> visibility;
  String page;

  _CellModel copyWith({
    String name,
    List<num> visibility,
    String page,
  }) =>
      _CellModel(
        name: name ?? this.name,
        visibility: visibility ?? this.visibility,
        page: page ?? this.page,
      );

  factory _CellModel.fromRawJson(String str) =>
      _CellModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory _CellModel.fromJson(Map<String, dynamic> json) => _CellModel(
        name: json["name"] == null ? null : json["name"],
        visibility: json["visibility"] == null
            ? null
            : List<int>.from(json["visibility"].map((x) => x)),
        page: json["page"] == null ? null : json["page"],
      );

  Map<String, dynamic> toJson() => {
        "name": name == null ? null : name,
        "visibility": visibility == null
            ? null
            : List<dynamic>.from(visibility.map((x) => x)),
        "page": page == null ? null : page,
      };
}

class SettingsPayment extends GetView<SettingsController> {
  static final _ITEM_LIST = [
    _CellModel(
      name: '現場付款',
      visibility: [StoreType.Dinner.index, StoreType.Retail.index],
      page: Routes.PAYMENT_INSTORE,
    ),
    _CellModel(
      name: '轉帳匯款',
      visibility: [StoreType.Retail.index],
      page: Routes.PAYMENT_BANK,
    ),
    _CellModel(
      name: '貨到付款',
      visibility: [StoreType.Retail.index],
      page: Routes.PAYMENT_COD,
    ),
    _CellModel(
      name: '信用卡一次付清',
      visibility: [],
      page: '',
    ),
    _CellModel(
      name: '網路ATM',
      visibility: [],
      page: '',
    ),
    _CellModel(
      name: '超商代碼',
      visibility: [],
      page: '',
    ),
    _CellModel(
      name: '超商條碼',
      visibility: [],
      page: '',
    ),
    _CellModel(
      name: 'LINE Pay 付款',
      visibility: [StoreType.Dinner.index, StoreType.Retail.index],
      page: Routes.PAYMENT_LINE_PAY,
    ),
    _CellModel(
      name: '綠界金流設定',
      visibility: [StoreType.Retail.index],
      page: Routes.ECPAY,
    ),
  ];

  final PrefProvider prefProvider;

  const SettingsPayment({
    Key key,
    @required this.prefProvider,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _children(),
    );
  }

  List<Widget> _children() {
    final children = <Widget>[];
    children.addIf(true, SettingsWidget.title(titleText: '付款方式設定'));
    children.addAll(_cells());
    children.addIf(true, SettingsWidget.space());
    return children;
  }

  Iterable<Widget> _cells() {
    return _ITEM_LIST.where((element) {
      if (prefProvider.brandsType.containsDinner &&
          element.visibility.contains(StoreType.Dinner.index)) {
        return true;
      }
      if (prefProvider.brandsType.containsRetail &&
          element.visibility.contains(StoreType.Retail.index)) {
        return true;
      }
      return false;
    }).map((element) {
      return SettingsWidget.item(
        titleText: element.name,
        onPressed: () {
          Get.toNamed(
            element.page,
            parameters: {
              'name': element.name,
            },
          );
        },
      );
    });
  }
}
