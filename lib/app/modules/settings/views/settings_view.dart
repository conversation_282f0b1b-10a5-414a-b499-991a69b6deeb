import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart' hide Svg;

import 'package:get/get.dart';
import 'package:muyipork/app/components/bg_default.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/underline_divider.dart';
import 'package:muyipork/app/modules/category_setup/controllers/category_setup_controller.dart';
import 'package:muyipork/app/modules/product_setup/controllers/product_setup_controller.dart';
import 'package:muyipork/app/modules/settings/views/settings_payment.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:url_launcher/url_launcher.dart';

import '../controllers/settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingsController>(
      init: SettingsController(
        apiProvider: Get.find(),
        packageInfo: Get.find(),
      ),
      builder: (controller) {
        return BgDefault(
          child: SafeArea(
            top: true,
            child: CustomScrollView(
              slivers: _body().toList(growable: false),
            ),
          ),
        );
      },
    );
  }

  Iterable<Widget> _body() sync* {
    yield Column(
      mainAxisSize: MainAxisSize.min,
      children: _banner().toList(growable: false),
    ).sliverBox;
    yield SliverFillRemaining(
      hasScrollBody: false,
      fillOverscroll: true,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(
            top: kRadiusCircular,
          ),
          color: kColorBackground,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  ///
  /// 餐飲線上訂單設定
  ///
  Iterable<Widget> _dinnerOnlineSettings() sync* {
    yield SettingsWidget.title(titleText: '餐飲線上訂單設定');
    yield SettingsWidget.item(
      titleText: '餐飲線上現場點餐設定',
      onPressed: () {
        return Get.toNamed(
          Routes.SETTING_DINNER_ONLINE_ORDER,
          parameters: {
            'name': '現場點餐',
          },
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '餐飲線上預約內用設定',
      onPressed: () {
        return Get.toNamed(
          Routes.SETTING_DINNER_ONLINE_HERE,
          parameters: {
            'name': '預約內用',
          },
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '餐飲線上到店自取設定',
      onPressed: () {
        return Get.toNamed(
          Routes.SETTING_DINNER_ONLINE_TOGO,
          parameters: {
            'name': '到店自取',
          },
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '餐飲線上店家外送設定',
      onPressed: () {
        return Get.toNamed(
          Routes.SETTING_DINNER_ONLINE_DELIVERY,
          parameters: {
            'name': '店家外送',
          },
        );
      },
    );
    yield SettingsWidget.space();
  }

  Iterable<Widget> _children() sync* {
    // avatar
    yield _avatar();
    // 營收統計
    if (controller.storeRole.isBoss) {
      yield* _reportSettings();
    }
    // 餐飲線上訂單設定
    if (controller.storeRole.isBoss &&
        controller.prefProvider.brandsType.containsDinner) {
      yield* _dinnerOnlineSettings();
    }
    // 餐飲菜單設定
    if (controller.storeRole.isBoss &&
        controller.prefProvider.brandsType.containsDinner) {
      yield* _dinnerSettings();
    }
    // 零售相關設定
    if (controller.storeRole.isBoss &&
        controller.prefProvider.brandsType.containsRetail) {
      yield* _retailSettings();
    }
    // 店家資訊設定
    if (controller.storeRole.isBoss) {
      yield* _storeSettings();
    }
    // 行銷推廣
    if (controller.storeRole.isBoss) {
      yield* _promotionSettings();
    }
    // FIXME: 跨界聯盟
    // 付款方式設定
    if (controller.storeRole.isBoss) {
      yield SettingsPayment(prefProvider: controller.prefProvider);
    }
    // 其他設定(權限在細項設定)
    yield* _otherSettings();
    // 開發者設定
    if (controller.storeRole.isBoss) {
      yield Obx(() {
        return Visibility(
          visible: controller.developerOptionsVisibility.value,
          child: _developSettings(),
        );
      });
    }
    yield const SizedBox(height: 12.0);
    yield GestureDetector(
      onTap: () => controller.count.value++,
      child: Text(
        // 'APP版本 ${controller.version}',
        controller.packageInfo?.displayVersion ?? '',
        style: const TextStyle(
          fontSize: 16,
          color: OKColor.GrayB9,
        ),
        textAlign: TextAlign.center,
      ).align(alignment: Alignment.center),
    );
    yield Obx(() {
      return Text(
        // 'omos-後結版',
        controller.displayInfo ?? '',
        style: const TextStyle(
          fontSize: 16,
          color: OKColor.GrayB9,
        ),
        textAlign: TextAlign.center,
      ).align(alignment: Alignment.center);
    });
    yield const SizedBox(height: kBottomPadding);
  }

  ///
  /// 營收統計
  ///
  Iterable<Widget> _reportSettings() sync* {
    yield SettingsWidget.item(
      titleText: '當日營收訂單',
      onPressed: () {
        Get.toNamed(Routes.REVENUE);
      },
    );
    yield SettingsWidget.item(
      titleText: '歷史營收統計',
      onPressed: () {
        Get.toNamed(Routes.REPORTS_STATEMENTS);
      },
    );
    yield SettingsWidget.item(
      titleText: '當日商品銷售統計',
      onPressed: () {
        Get.toNamed(Routes.REPORTS_SALES);
      },
    );
    // 當日交接班報表
    yield SettingsWidget.item(
      titleText: '當日交接班報表',
      onPressed: () {
        Get.toNamed(Routes.REPORTS_REALTIME_STATEMENTS);
      },
    );
    // FIXME: 尚未完成
    // yield SettingsWidget.item(
    //   titleText: '庫存表單',
    //   onPressed: () {
    //     Get.toNamed(Routes.REVENUE);
    //   },
    // );
    // space
    yield SettingsWidget.space();
  }

  Iterable<Widget> _banner() sync* {
    yield Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: _logout,
        child: const Text(
          '登出',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
    yield SvgPicture.asset('assets/images/icon_store.svg');
    yield const SizedBox(height: 16);
    yield Obx(() {
      return Text(
        // '賣東西零售店 忠孝東路門市',
        controller.storeName ?? '',
        style: const TextStyle(
          fontSize: 16,
          height: 1.0,
          color: Colors.white,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      );
    });
    yield const SizedBox(height: 25);
  }

  void _logout() {
    DialogGeneral(
      DialogArgs(
        header: Text(
          "即將登出",
          style: TextStyle(
            fontSize: 20,
            color: OKColor.Gray33,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ),
        contentIcon: DialogContentIcon.Alert,
        mainButtonText: "登出",
        onMainButtonPress: () {
          FutureProgress(
            future: controller.logout(),
          ).dialog();
        },
        secondaryButtonText: "取消",
      ),
    ).dialog();
  }

  ///
  /// 餐飲相關設定
  ///
  Iterable<Widget> _dinnerSettings() sync* {
    yield SettingsWidget.title(titleText: '餐飲相關設定');
    // yield SettingsWidget.item(
    //   titleText: '店內菜單分類規格設定',
    //   onPressed: () async {
    //     await Get.toNamed(
    //       Routes.CATEGORY_SETUP,
    //       parameters: {
    //         Keys.Tag: '${CategorySetupTab.Categories.index}',
    //       },
    //       // TODO: use parameters instead.
    //       arguments: CategorySetupArgs(
    //         initialTab: CategorySetupTab.Categories,
    //         kind: ProductKind.DinnerApp.index,
    //         title: '店內菜單分類規格設定',
    //       ),
    //     );
    //   },
    // );
    yield SettingsWidget.item(
      titleText: '店內菜單分類設定',
      onPressed: () {
        Get.toNamed(
          Routes.CATEGORY_SETUP,
          parameters: {
            Keys.Tag: '${CategorySetupTab.Categories.index}',
          },
          // TODO: use parameters instead.
          arguments: CategorySetupArgs(
            initialTab: CategorySetupTab.Categories,
            title: '店內菜單分類設定',
            kind: ProductKind.DinnerApp.index,
          ),
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '店內菜單規格設定',
      onPressed: () {
        Get.toNamed(
          Routes.CATEGORY_SETUP,
          parameters: {
            Keys.Tag: '${CategorySetupTab.AdditionCategories.index}',
          },
          // TODO: use parameters instead.
          arguments: CategorySetupArgs(
            initialTab: CategorySetupTab.AdditionCategories,
            title: '店內菜單規格設定',
            kind: ProductKind.DinnerApp.index,
          ),
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '店內菜單編輯',
      onPressed: () {
        Get.toNamed(
          Routes.PRODUCT_SETUP,
          arguments: ProductSetupArgs(
            kind: ProductKind.DinnerApp.index,
          ),
        );
      },
    );
    // yield SettingsWidget.item(
    //   titleText: '線上菜單分類規格設定',
    //   onPressed: () async {
    //     await Get.toNamed(
    //       Routes.CATEGORY_SETUP,
    //       parameters: {
    //         Keys.Tag: '${CategorySetupTab.Categories.index}',
    //       },
    //       // TODO: use parameters instead.
    //       arguments: CategorySetupArgs(
    //         initialTab: CategorySetupTab.Categories,
    //         title: '線上菜單分類規格設定',
    //         kind: ProductKind.DinnerLine.index,
    //       ),
    //     );
    //   },
    // );
    yield SettingsWidget.item(
      titleText: '線上菜單分類設定',
      onPressed: () {
        Get.toNamed(
          Routes.CATEGORY_SETUP,
          parameters: {
            Keys.Tag: '${CategorySetupTab.Categories.index}',
          },
          // TODO: use parameters instead.
          arguments: CategorySetupArgs(
            initialTab: CategorySetupTab.Categories,
            title: '線上菜單分類設定',
            kind: ProductKind.DinnerLine.index,
          ),
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '線上菜單規格設定',
      onPressed: () {
        Get.toNamed(
          Routes.CATEGORY_SETUP,
          parameters: {
            Keys.Tag: '${CategorySetupTab.AdditionCategories.index}',
          },
          // TODO: use parameters instead.
          arguments: CategorySetupArgs(
            initialTab: CategorySetupTab.AdditionCategories,
            title: '線上菜單規格設定',
            kind: ProductKind.DinnerLine.index,
          ),
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '線上菜單編輯',
      onPressed: () {
        Get.toNamed(
          Routes.PRODUCT_SETUP,
          arguments: ProductSetupArgs(
            kind: ProductKind.DinnerLine.index,
          ),
        );
      },
    );
    // TODO: 內容有再調整!
    yield SettingsWidget.item(
      titleText: '餐飲桌號設定',
      onPressed: () {
        Get.toNamed(Routes.PARTITION_SETUP);
      },
    );
    // FIXME:
    // yield SettingsWidget.item(
    //   titleText: '餐飲控位設定',
    //   onPressed: () {
    //     Get.toNamed(Routes.PARTITION_SETUP);
    //   },
    // );
    yield SettingsWidget.item(
      titleText: '餐飲自動列印設定',
      onPressed: () {
        Get.toNamed(
          Routes.SETTING_AUTO_PRINT,
          parameters: {
            'type': '${StoreType.Dinner.index}',
          },
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '餐飲DM模式',
      onPressed: () {
        Get.toNamed(
          Routes.SETTING_DM,
          arguments: StoreType.Dinner,
        );
      },
    );
    yield SettingsWidget.space();
  }

  ///
  /// 零售相關設定
  ///
  Iterable<Widget> _retailSettings() sync* {
    yield SettingsWidget.title(titleText: '零售相關設定');
    yield SettingsWidget.item(
      titleText: '零售商店到店自取設定',
      onPressed: () {
        Get.toNamed(Routes.RETAIL_TAKEOUT_SETTINGS);
      },
    );
    yield SettingsWidget.item(
      titleText: '零售商店宅配寄送設定',
      onPressed: () {
        Get.toNamed(Routes.RETAIL_DELIVERY_SETTINGS);
      },
    );
    // FIXME:
    // if (controller.storeRole.isBoss == true) {
    //   yield SettingsWidget.item(
    //     titleText: '零售商店超商取貨設定',
    //     onPressed: () {},
    //   );
    // }
    // 已拆分成分類&規格
    // yield SettingsWidget.item(
    //   titleText: '零售商品分類規格設定',
    //   onPressed: () async {
    //     await Get.toNamed(
    //       Routes.CATEGORY_SETUP,
    //       parameters: {
    //         Keys.Tag: '${CategorySetupTab.Categories.index}',
    //       },
    //       // TODO: use parameters instead.
    //       arguments: CategorySetupArgs(
    //         initialTab: CategorySetupTab.Categories,
    //         title: '零售商品分類規格設定',
    //         kind: ProductKind.Retail.index,
    //       ),
    //     );
    //   },
    // );
    yield SettingsWidget.item(
      titleText: '零售商品分類設定',
      onPressed: () async {
        await Get.toNamed(
          Routes.CATEGORY_SETUP,
          parameters: {
            Keys.Tag: '${CategorySetupTab.Categories.index}',
          },
          // TODO: use parameters instead.
          arguments: CategorySetupArgs(
            initialTab: CategorySetupTab.Categories,
            title: '零售商品分類設定',
            kind: ProductKind.Retail.index,
          ),
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '零售商品規格設定',
      onPressed: () async {
        await Get.toNamed(
          Routes.CATEGORY_SETUP,
          parameters: {
            Keys.Tag: '${CategorySetupTab.AdditionCategories.index}',
          },
          arguments: CategorySetupArgs(
            initialTab: CategorySetupTab.AdditionCategories,
            title: '零售商品規格設定',
            kind: ProductKind.Retail.index,
          ),
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '零售商品編輯',
      onPressed: () {
        Get.toNamed(
          Routes.PRODUCT_SETUP,
          arguments: ProductSetupArgs(
            kind: ProductKind.Retail.index,
          ),
        );
      },
    );
    // FIXME:
    // yield SettingsWidget.item(
    //   titleText: '零售金流設定',
    //   onPressed: () {
    //     //
    //   },
    // );
    // FIXME:
    // yield SettingsWidget.item(
    //   titleText: '零售物流設定',
    //   onPressed: () {
    //     //
    //   },
    // );
    yield SettingsWidget.item(
      titleText: '零售自動列印設定',
      onPressed: () {
        Get.toNamed(
          Routes.SETTING_AUTO_PRINT,
          parameters: {
            'type': '${StoreType.Retail.index}',
          },
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '零售DM模式',
      onPressed: () {
        Get.toNamed(
          Routes.SETTING_DM,
          arguments: StoreType.Retail,
        );
      },
    );
    yield SettingsWidget.space();
  }

  ///
  /// 店家資訊設定
  ///
  Iterable<Widget> _storeSettings() sync* {
    yield SettingsWidget.title(titleText: '店家資訊設定');
    yield SettingsWidget.item(
      titleText: '輪播圖片',
      onPressed: () {
        Get.toNamed(Routes.BRANDS_BANNERS);
      },
    );
    yield SettingsWidget.item(
      titleText: '最新消息',
      onPressed: () {
        Get.toNamed(Routes.BRANDS_NEWS);
      },
    );
    yield SettingsWidget.item(
      titleText: '營業時間設定',
      onPressed: () {
        Get.toNamed(Routes.BUSINESS_HOURS_SETUP);
      },
    );
    yield SettingsWidget.item(
      titleText: '基本資料',
      onPressed: () {
        Get.toNamed(Routes.BRANDS_BASIC);
      },
    );
    // const _Divider(),
    // StreamBuilder<BrandsType>(
    //   stream: controller.checkoutTypeStream,
    //   builder: (context, snapshot) {
    //     bool visible = controller.prefProvider.checkoutType.containsDinner;
    //     return Visibility(
    //       visible: visible,
    //       child: _Item(
    //         titleText: '餐飲桌號設定',
    //         onPressed: () {
    //           controller.tableSettingPressed();
    //         },
    //       ),
    //     );
    //   },
    // ),
    yield SettingsWidget.space();
  }

  ///
  /// 行銷推廣
  ///
  Iterable<Widget> _promotionSettings() sync* {
    yield SettingsWidget.title(titleText: '行銷推廣');
    yield SettingsWidget.item(
      titleText: '會員積點',
      onPressed: () {
        Get.toNamed(Routes.SETTING_POINTS);
      },
    );
    // TODO: completed me
    // yield SettingsWidget.item(
    //   titleText: '會員活動',
    // );
    // TODO: completed me
    // yield SettingsWidget.item(
    //   titleText: '問卷設定',
    // );
    // yield SettingsWidget.item(
    //   titleText: '商品折扣',
    //   onPressed: () {
    //     Get.toNamed(
    //       Routes.SETTING_DISCOUNT,
    //       parameters: <String, String>{'type': '${PromotionType.Off.index}'},
    //     );
    //   },
    // );
    yield SettingsWidget.item(
      // titleText: '現場減價',
      titleText: PromotionType.Discount.title,
      onPressed: () {
        Get.toNamed(
          Routes.SETTING_DISCOUNT,
          parameters: <String, String>{
            'type': '${PromotionType.Discount.index}'
          },
        );
      },
    );
    yield SettingsWidget.item(
      // titleText: '額外費用',
      titleText: PromotionType.Upgrade.title,
      onPressed: () {
        Get.toNamed(
          Routes.SETTING_DISCOUNT,
          parameters: <String, String>{
            'type': '${PromotionType.Upgrade.index}'
          },
        );
      },
    );
    yield SettingsWidget.space();
  }

  ///
  /// 其他設定
  ///
  Iterable<Widget> _otherSettings() sync* {
    yield SettingsWidget.title(titleText: '其他設定');
    // FIXME:
    if (controller.storeRole.isBoss && false) {
      yield SettingsWidget.item(
        titleText: '推播通知設定',
        onPressed: () {
          Get.toNamed(Routes.PUSH_SETTINGS);
        },
      );
    }
    // FIXME:
    if (controller.storeRole.isBoss && false) {
      yield SettingsWidget.item(
        titleText: '商品圖片流量',
        onPressed: () {},
      );
    }
    if (controller.storeRole.isBoss &&
        controller.prefProvider.brandsType.containsDinner) {
      yield SettingsWidget.item(
        titleText: '服務費設定',
        onPressed: () {
          Get.toNamed(Routes.SETTING_SERVICE);
        },
      );
    }
    if (controller.storeRole.isBoss) {
      yield SettingsWidget.item(
        titleText: '電子發票設定',
        onPressed: () {
          Get.toNamed(Routes.INVOICE_SETTINGS);
        },
      );
    }
    if (controller.storeRole.isBoss) {
      yield SettingsWidget.item(
        titleText: '支付方式設定',
        onPressed: () {
          Get.toNamed(Routes.SETTING_PAY);
        },
      );
    }
    if (controller.storeRole.isBoss) {
      yield SettingsWidget.item(
        titleText: '標籤機設定',
        onPressed: () {
          Get.toNamed(Routes.GODEX_PRINTER_SETUP);
        },
      );
    }
    if (controller.storeRole.isBoss) {
      yield SettingsWidget.item(
        titleText: '印表機設定',
        onPressed: () {
          Get.toNamed(Routes.SETTING_PRINTER);
        },
      );
    }
    if (controller.storeRole.isBoss) {
      yield SettingsWidget.item(
        titleText: '雲印表機設定',
        onPressed: () {
          Get.toNamed(Routes.SETTING_PRINTER, parameters: {
            'cloud': '${Switcher.On.index}',
          });
        },
      );
    }
    // if (controller.storeRole.isBoss) {
    //   yield Obx(() {
    //     final other = controller.draft.data.other;
    //     return SettingsWidget.switcher(
    //       titleText: '列印工作單(線上訂單確認後)',
    //       value: (other.printDetail ??= 0).switcher.isOn,
    //       onChanged: (value) {
    //         other.printDetail = value.switcher.index;
    //         // 更新本地顯示
    //         controller.refreshData();
    //         // 上傳到伺服器
    //         try {
    //           controller.submit();
    //         } catch (e) {
    //           _showDialog('$e');
    //         }
    //       },
    //     );
    //   });
    // }
    if (controller.storeRole.isBoss) {
      yield SettingsWidget.item(
        titleText: '操作員帳號設定',
        onPressed: () {
          Get.toNamed(Routes.ACCOUNTS);
        },
      );
    }
    yield SettingsWidget.item(
      titleText: '修改密碼',
      onPressed: () {
        Get.toNamed(Routes.PASSWORD);
      },
    );
    if (controller.storeRole.isBoss) {
      yield _lineAt();
    }
    yield _contactUs();
    yield SettingsWidget.space();
  }

  Widget _lineAt() {
    return SettingsWidget.item(
      onPressed: () {
        Get.toNamed(Routes.LINE_AT_SETTINGS);
      },
      title: const Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 16.0,
            color: OKColor.Line,
          ),
          children: [
            TextSpan(
              text: 'LINE@',
              style: TextStyle(
                fontWeight: FontWeight.w700,
              ),
            ),
            TextSpan(
              text: ' ',
            ),
            TextSpan(
              text: '設定',
              style: TextStyle(
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _contactUs() {
    return SettingsWidget.item(
      onPressed: () {
        // 聯絡 OKSHOP 客服
        canLaunch(kOkShopService).then(
          (value) {
            if (value != null && value) {
              launch(kOkShopService);
            }
          },
        );
      },
      title: const Text.rich(
        TextSpan(
          style: TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          children: [
            TextSpan(text: '聯絡'),
            TextSpan(
              text: 'OKSHOP',
              style: TextStyle(
                color: OKColor.Primary,
                fontWeight: FontWeight.w700,
              ),
            ),
            TextSpan(text: '客服'),
          ],
        ),
      ),
    );
  }

  ///
  /// 開發者設定
  ///
  Widget _developSettings() {
    Iterable<Widget> children() sync* {
      yield SettingsWidget.title(titleText: '開發者設定');
      yield SettingsWidget.item(
        leading: Text(
          'FCM token',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
        ),
        titleText: controller.prefProvider.userDefault.get(
          kFcmToken,
          defaultValue: '-',
        ),
      );
      // yield Obx(() {
      //   final other = controller.draft.data.other;
      //   return ColoredBox(
      //     color: Colors.white,
      //     child: Padding(
      //       padding: kContentPadding,
      //       child: Row(
      //         children: [
      //           Expanded(
      //             child: Text(
      //               '流程模式',
      //               style: const TextStyle(
      //                 fontSize: 16,
      //                 color: Colors.black,
      //               ),
      //             ),
      //           ),
      //           DropdownButton(
      //             //目前有意義的 checkoutType 就只這 5 個
      //             items: [0, 1, 2, 3, 4]
      //                 .map((index) => DropdownMenuItem<int>(
      //                       value: index,
      //                       child: Text(
      //                           SettingOther.getBrandsTypeDisplayStr(index)),
      //                     ))
      //                 .toList(),
      //             underline: SizedBox.shrink(),
      //             value: other.checkoutType,
      //             onChanged: (value) {
      //               other.checkoutType = value;
      //               controller.refreshData();
      //             },
      //           )
      //         ],
      //       ),
      //     ),
      //   );
      // });
      if (controller.storeRole.isBoss && Platform.isAndroid) {
        yield UnderlineDivider(
          child: ListTile(
            dense: true,
            contentPadding: kContentPadding,
            onTap: controller.checkUpdate,
            title: Text(
              '檢查新版本',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
              textAlign: TextAlign.left,
            ),
            trailing: Obx(() {
              return Visibility(
                visible: controller.progressingVisible,
                child: SizedBox(
                  width: 20.0,
                  height: 20.0,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.0,
                    value: controller.progressingValue > 0.0
                        ? controller.progressingValue
                        : null,
                  ),
                ),
              );
            }),
          ),
        );
      }
      yield SettingsWidget.space();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _avatar() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(
        width: 60.0,
        height: 60.0,
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Color(0x4dc68329),
                offset: Offset(3, 3),
                blurRadius: 12,
              ),
            ],
          ),
          child: const Icon(
            Icons.person,
            color: OKColor.Primary,
            size: 48.0,
          ),
        ),
      );
      yield const SizedBox(width: 12);
      yield Expanded(
        child: Text(
          controller?.prefProvider?.jwt?.name ?? '',
          style: const TextStyle(
            fontSize: 22,
            color: Colors.black,
            fontWeight: FontWeight.w700,
          ),
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.left,
          maxLines: 2,
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.only(
        left: 16.0,
        right: 16.0,
        top: 20.0,
        bottom: 24.0,
      ),
      child: children().row(),
    );
  }
}
