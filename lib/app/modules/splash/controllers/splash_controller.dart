import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';

class SplashController extends GetxController with StateMixin<String> {
  final ProductProvider productProvider;
  final SettingProvider settingProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Logger get logger => boxProvider.logger;

  SplashController({
    @required this.productProvider,
    @required this.settingProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
      if (prefProvider.isLogin) {
        await _init();
      }
      Get.offAllNamed(Routes.HOME);
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<void> _init() async {
    await _initBoxes();
    await _initSettings();
    await _initCategories();
    await _initAdditionCategories();
    await _initAdditionProducts();
    await _initProducts();
    await _initShippingSettings();
    await _initPointSettings();
  }

  Future<void> _initBoxes() async {
    try {
      boxProvider.token = prefProvider.token;
      await boxProvider.init();
      for (var box in boxes) {
        await boxProvider.initGsBox(box);
      }
    } catch (e) {
      logger.e(e);
    }
  }

  /// 初始化應用程式設定
  Future<void> _initSettings() async {
    try {
      await apiProvider.getSetting(updateCache: true);
    } catch (e) {
      logger.e(e);
    }
  }

  /// 初始化產品分類
  Future<void> _initCategories() async {
    try {
      await productProvider.fetchAllCategories();
    } catch (e) {
      logger.e(e);
    }
  }

  /// 初始化規格分類
  Future<void> _initAdditionCategories() async {
    try {
      await productProvider.fetchAllAdditionCategories();
    } catch (e) {
      logger.e(e);
    }
  }

  /// 初始化規格產品
  Future<void> _initAdditionProducts() async {
    try {
      await productProvider.fetchAllAdditionProducts();
    } catch (e) {
      logger.e(e);
    }
  }

  /// 初始化產品資料
  Future<void> _initProducts() async {
    try {
      await productProvider.fetchAllProducts();
    } catch (e) {
      logger.e(e);
    }
  }

  /// 初始化運費設定
  Future<void> _initShippingSettings() async {
    try {
      prefProvider.shippingDelivery =
          await settingProvider.getShippingDelivery();
    } catch (e) {
      logger.e(e);
    }
  }

  /// 初始化積點設定
  Future<void> _initPointSettings() async {
    try {
      prefProvider.settingPoint = await apiProvider.getSettingPoints();
    } catch (e) {
      logger.e(e);
    }
  }
}
