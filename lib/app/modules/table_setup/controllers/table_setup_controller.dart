import 'dart:async';

import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/table_provider.dart';
import 'package:muyipork/app/models/id_sort.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';

class TableSetupController extends GetxController with StateMixin<String> {
  final TableProvider tableProvider;
  ApiProvider get apiProvider => tableProvider.apiProvider;
  final _draft = Rx<Table>(null);
  final _req = TableReq().obs;
  final _disposable = Completer();
  final _deleting = <num>[].obs;
  final _updating = <num>[].obs;
  final _needReload = false.obs;

  Table get draft => _draft.value;
  TableReq get req => _req.value;

  TableSetupController({
    this.tableProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 監聽 table cache
    // tableProvider.cached.stream
    //     .debounce(100.milliseconds)
    //     .takeUntil(_disposable.future)
    //     .listen(
    //   (event) {
    //     if (event.containsKey(draft.id)) {
    //       _draft.value = event[draft.id];
    //     }
    //   },
    // );
    // 設定 req 的 parent id
    _draft.stream.takeUntil(_disposable.future).listen(
      (event) {
        req.parentId = event?.id ?? null;
        req.sort = event?.child?.length ?? 0;
      },
    );
    if (Get.parameters.containsKey(Keys.Data)) {
      final jsonString = Get.parameters[Keys.Data];
      _draft.value = Table.fromRawJson(jsonString);
    }
  }

  @override
  void onReady() {
    super.onReady();
    if (draft != null) {
      change('', status: RxStatus.success());
    } else {
      change('',
          status: RxStatus.error('Get.parameters did NOT contains data'));
    }
  }

  @override
  void onClose() {
    _disposable.complete();
    if (true == _needReload.value) {
      tableProvider.getTables();
    }
  }

  // 嘗試新增一格。
  Future<bool> addNewTable() async {
    if (req.name == null || req.name.isEmpty) {
      return false;
    }
    try {
      final ret = await tableProvider.post(req);
      if (ret != null && ret != 0) {
        draft.child ??= <Table>[];
        draft.child.add(req.asTable(id: ret));
        await _sort();
        _req.value = TableReq();
        _draft.refresh();
        // 重新抓
        _needReload.value = true;
        return true;
      }
      return false;
    } catch (e) {
      rethrow;
    }
  }

  void sort(int srcIndex, int destIndex) {
    final list = draft.child ?? <Table>[];
    if (srcIndex < destIndex) {
      // 排序不超過新增
      if (destIndex <= list.length) {
        final element = list.elementAt(srcIndex);
        list.insert(destIndex, element);
        list.removeAt(srcIndex);
      }
    } else {
      final element = list.removeAt(srcIndex);
      list.insert(destIndex, element);
    }
    for (var i = 0; i < list.length; i++) {
      final element = list.elementAt(i);
      element.sort = i;
    }
    _draft.refresh();
  }

  Future<bool> _sort() {
    final list = draft.child ?? <Table>[];
    final it = Iterable.generate(list.length, (index) {
      final element = list.elementAt(index);
      return IDSort(id: element.id, sort: index);
    });
    return tableProvider.sort(it);
  }

  void updating(num id) {
    final contains = _updating.contains(id);
    _updating.addIf(!contains, id);
  }

  void deleting(num id) {
    final index = draft.child.indexWhere((element) => element.id == id);
    if (index >= 0) {
      final table = draft.child.removeAt(index);
      _draft.refresh();
      _deleting.add(table.id);
      for (var child in table.child) {
        _deleting.add(child.id);
      }
    }
  }

  Future<bool> _delete() async {
    if (_deleting.isEmpty) {
      return true;
    }
    final ids = _deleting.toSet();
    _deleting.clear();
    for (var id in ids) {
      try {
        await tableProvider.delete(id);
      } catch (e) {
        rethrow;
      }
    }
    return true;
  }

  Table _getTable(num id) {
    if (draft.id == id) {
      return draft;
    }
    final index = draft.child.indexWhere((element) => element.id == id);
    if (index >= 0) {
      return draft.child.elementAt(index);
    }
    return null;
  }

  Future<bool> _update() async {
    if (_updating.isEmpty) {
      return true;
    }
    final ids = _updating.toSet();
    _updating.clear();
    for (var id in ids) {
      final element = _getTable(id);
      try {
        await tableProvider.put(id, element.asTableReq());
      } catch (e) {
        rethrow;
      }
    }
    return true;
  }

  Future<bool> submit() async {
    try {
      // 刪除
      await _delete();
      // 更新
      await _update();
      // 新增 (包含排序)
      if (false == await addNewTable()) {
        // 新增失敗，手動排序
        await _sort();
      }
      _needReload.value = true;
      return true;
    } catch (e) {
      rethrow;
    }
  }
}
