import 'package:flutter/material.dart' hide Table;

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/composite_edit_item.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/table_setup_controller.dart';

// 編輯桌號
class TableSetupView extends GetView<TableSetupController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: controller.draft.name,
      child: controller.obx(
        (state) {
          return BottomWidgetPage.save(
            child: _main(),
            onPressed: _submit,
          );
        },
      ),
    );
  }

  void _submit() {
    FutureProgress<bool>(
      future: controller.submit(),
    ).dialog().then(
      (value) {
        if (true == value) {
          Get.back();
        }
      },
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(true, SizedBox(height: 30));
    children.addIf(
      true,
      SettingsWidget.input(
        initialValue: controller.draft.name,
        labelText: '區域名稱',
        hintText: '請輸入區域名稱',
        onChanged: (value) {
          controller.draft.name = value;
          // 加入要更新的 id
          controller.updating(controller.draft.id);
        },
      ),
    );
    children.addIf(
      true,
      Expanded(child: Obx(() => _list())),
    );
    return Column(children: children);
  }

  Widget _list() {
    controller.draft.child ??= <Table>[];
    final list = controller.draft.child;
    list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
    final children = List.generate(list.length + 1, (index) {
      if (index < list.length) {
        final element = list.elementAt(index);
        return CompositeEditItem(
          CompositeEditItemArgs(
            CompositeContentMode.OneTextField,
            CompositeRightButtonMode.Remove,
            mainTextFieldInit: element.name,
            mainTextFieldHint: '桌號',
            mainTextFieldChanged: (value) {
              element.name = value;
              // 加入要更新的 id
              controller.updating(element.id);
            },
            onRightButtonPressed: () async {
              // 刪除桌號
              controller.deleting(element.id);
              return true;
            },
          ),
          key: Key('$index'),
        );
      }
      // Add new partition.
      return CompositeEditItem(
        CompositeEditItemArgs(
          CompositeContentMode.OneTextField,
          CompositeRightButtonMode.Add,
          mainTextFieldHint: '桌號',
          mainTextFieldChanged: (value) {
            controller.req.name = value;
          },
          onRightButtonPressed: () {
            //Try to add this new table.
            final name = controller.req.name;
            if (name == null || name.isEmpty) {
              return DialogGeneral.alert('請先輸入桌號').dialog();
            }
            //呼叫新增 Table API.
            return controller.addNewTable();
          },
          showDragHandle: false,
        ),
        key: Key('$index'),
      );
    });

    return ReorderableListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: children,
      onReorder: (srcIndex, destIndex) {
        // asc (0, 2)
        // desc (1, 0)
        controller.sort(srcIndex, destIndex);
      },
    );
  }
}
