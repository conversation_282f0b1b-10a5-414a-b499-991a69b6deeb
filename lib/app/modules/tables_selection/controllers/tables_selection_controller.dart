import 'package:flutter/material.dart' hide Table;
import 'package:get/get.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/table_provider.dart';
import 'package:okshop_model/okshop_model.dart';

class TablesSelectionArgs {
  TablesSelectionArgs({
    this.table1Id,
    this.table2Id,
    this.withNumQueryField = false,
  });
  final int table1Id;
  final int table2Id;
  final bool withNumQueryField;
}

class TablesSelectionController extends GetxController with StateMixin<String> {
  final TableProvider tableProvider;

  final _args = Rx<TablesSelectionArgs>(null);
  TablesSelectionArgs get args => _args.value;

  //The is the actual user select ids. (Maybe we could initialize this at start?)
  final _selectedPartitionId = 0.obs;
  num get selectedPartitionId => _selectedPartitionId.value;
  set selectedPartitionId(num value) => _selectedPartitionId.value = value;

  final _selectedTableId = 0.obs;
  num get selectedTableId => _selectedTableId.value;
  set selectedTableId(num value) => _selectedTableId.value = value;

  final inputNumber = ''.obs;

  final data = <Table>[].obs;

  ApiProvider get apiProvider => tableProvider.apiProvider;

  TablesSelectionController({
    @required this.tableProvider,
  });

  @override
  void onInit() async {
    super.onInit();
    _args.value = Get.arguments;
  }

  @override
  void onReady() {
    super.onReady();
    data.assignAll(tableProvider.cached.values);
    //Try read the selected partition / table.
    _trySelect();
    change('', status: RxStatus.success());
  }

  void _trySelect() {
    if (args != null) {
      // print('Got arguments [' + partitionTable.item1.toString() + '][' + partitionTable.item2.toString() + ']');
      if (!_trySelectPartition(args.table1Id)) {
        //Maybe not
        // trySelectTheFirstPartition();
      }

      if (!_trySelectTable(args.table2Id)) {
        //Maybe not
        // trySelectTheFirstTable();
      }
    }
  }

  @override
  void onClose() {
    //
  }

  //嘗試直接選擇第一個 Partition
  bool trySelectTheFirstPartition() {
    final ret = data.isNotEmpty;
    selectedPartitionId = ret ? data.first.id : 0;
    return ret;
  }

  //嘗試直接選擇第一個 Table
  bool trySelectTheFirstTable() {
    selectedTableId = 0;
    final activePartition = selectedPartition;
    if (activePartition != null && activePartition.child.isNotEmpty) {
      selectedTableId = activePartition.child[0].id;
      return true;
    }
    return false;
  }

  //嘗試直接選擇一個 Partition
  bool _trySelectPartition(num partitionId) {
    final index = data.indexWhere((element) => element.id == partitionId);
    if (index >= 0) {
      selectedPartitionId = partitionId;
      return true;
    }
    return false;
  }

  //嘗試直接選擇一個 Table
  bool _trySelectTable(num tableId) {
    final activePartition = selectedPartition;
    if (activePartition != null && activePartition.child.isNotEmpty) {
      if (activePartition.child.any((element) => element.id == tableId)) {
        selectedTableId = tableId;
        return true;
      }
    }
    return false;
  }

  // 嘗試取得目前選擇的 Partition 有可能回傳 null
  Table get selectedPartition {
    return data.firstWhere(
      (element) => element.id == selectedPartitionId,
      orElse: () => Table(child: <Table>[]),
    );
  }

  Future<void> onRefresh() async {
    try {
      final it = await tableProvider.getTables();
      data.assignAll(it);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }
}
