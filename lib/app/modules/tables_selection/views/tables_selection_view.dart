import 'package:flutter/material.dart' hide Table;
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/square_toggle.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/constants.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:tuple/tuple.dart';

import '../controllers/tables_selection_controller.dart';

class TablesSelectionView extends GetView<TablesSelectionController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.focusScope.unfocus();
      },
      child: StandardPage(
        titleText: controller.args.withNumQueryField ? '搜尋' : '選擇座位',
        child: controller.obx(
          (state) {
            return BottomWidgetPage(
              child: Obx(() => _main()),
              bottom: _bottom(),
            );
          },
          onEmpty: ListWidget.blank(),
          onError: ListWidget.message,
        ),
      ),
    );
  }

  Widget _bottom() {
    return BottomWrapper(
      child: YesNoButton(
        leftButtonText: '取消',
        rightButtonText: '確認',
        onLeftPressed: () {
          Get.back(result: null);
        },
        onRightPressed: () async {
          //回傳 <PartitionID, TableId, queryNum>
          if (controller.selectedPartitionId > 0) {
            Get.back(
              result: Tuple3<int, int, String>(
                controller.selectedPartitionId,
                controller.selectedTableId,
                controller.inputNumber.value,
              ),
            );
          }
        },
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    //For query number field.
    children.addIf(
      controller.args.withNumQueryField,
      SizedBox(height: kPadding),
    );
    children.addIf(
      controller.args.withNumQueryField,
      queryNumEditingField(),
    );
    //Partition selection list.
    children.addIf(true, partitionEditingGroup());
    children.addIf(true, Divider(height: 30));
    //Table selection list.
    children.addIf(true, tableEditingGroup());
    return ListView(
      padding: EdgeInsets.only(
        left: kPadding,
        right: kPadding,
        bottom: kBottomButtonPadding,
      ),
      children: children,
    );
  }

  Widget queryNumEditingField() {
    return SettingsWidget.input(
      contentPadding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      labelText: '訂單編號',
      hintText: '請輸入訂單編號末3碼',
      keyboardType: TextInputType.number,
      onChanged: controller.inputNumber,
      inputFormatters: [
        FilteringTextInputFormatter.singleLineFormatter,
        FilteringTextInputFormatter.digitsOnly,
      ],
    );
  }

  Widget partitionEditingGroup() {
    final it = controller.data.map((element) {
      return SquareToggle<num>(
        value: element.id,
        text: element.name,
        checked: controller.selectedPartitionId == element.id,
        onPressed: (value) {
          if (controller.selectedPartitionId != value) {
            controller.selectedPartitionId = value;
            controller.trySelectTheFirstTable();
          }
        },
      );
    });

    final children = <Widget>[];
    // title
    children.addIf(
      true,
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Text(
          '區域',
          style: Get.textTheme.headline5,
          textAlign: TextAlign.center,
        ),
      ),
    );
    // content
    children.addIf(
      true,
      Flexible(
        child: GridView.count(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          crossAxisCount: 4,
          childAspectRatio: 3.0 / 1.75,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          children: [...it],
        ),
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget tableEditingGroup() {
    final ls = controller.selectedPartition.child ?? <Table>[];
    final it = ls.map((element) {
      return SquareToggle<num>(
        value: element.id,
        text: element.name,
        checked: controller.selectedTableId == element.id,
        onPressed: (value) {
          if (controller.selectedTableId != value) {
            controller.selectedTableId = value;
          }
        },
      );
    });

    final children = <Widget>[];
    // title
    children.addIf(
      true,
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Text(
          '桌號',
          style: Get.textTheme.headline5,
          textAlign: TextAlign.center,
        ),
      ),
    );
    children.addIf(
      true,
      Flexible(
        child: GridView.count(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          crossAxisCount: 4,
          childAspectRatio: 3.0 / 1.75,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          children: [...it],
        ),
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
