import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/models/store_account.dart';
import 'package:muyipork/app/models/store_account_post.dart';
import 'package:muyipork/app/models/store_account_put.dart';
import 'package:okshop_common/okshop_common.dart';

import 'api_provider.dart';

class Filter {
  String keyword;
}

class AccountProvider {
  final ApiProvider apiProvider;
  final cached = <num, StoreAccount>{}.obs;

  AccountProvider({
    @required this.apiProvider,
  });

  Future<Iterable<StoreAccount>> getStoreAccounts([Filter filter]) async {
    filter ??= Filter();
    try {
      final req = <String, dynamic>{
        'keyword': filter?.keyword,
      };
      final ret = await apiProvider.getList(
        unencodedPath: 'store-accounts',
        filter: req,
        creator: (json) => StoreAccount.fromJson(json),
      );
      final entries = ret.map((e) => MapEntry(e.id, e));
      cached.addEntries(entries);
      return ret;
    } catch (e) {
      rethrow;
    }
  }

  Iterable<StoreAccount> where(Filter filter) {
    filter ??= Filter();
    return cached.values.where((element) {
      if (filter.keyword == null || filter.keyword.isEmpty) {
        return true;
      }
      final regExp = RegExp(
        filter.keyword,
        caseSensitive: false,
      );
      if (regExp.hasMatch(element.name)) {
        return true;
      }
      if (regExp.hasMatch(element.username)) {
        return true;
      }
      if (regExp.hasMatch('${element.id}')) {
        return true;
      }
      return false;
    });
  }

  ///
  /// 取得單一帳號資料
  ///
  Future<StoreAccount> getStoreAccount(num id) async {
    try {
      final ret = await apiProvider.getData(
        unencodedPath: 'store-accounts/$id',
        creator: (json) => StoreAccount.fromJson(json),
      );
      cached[id] = ret;
      return ret;
    } catch (e) {
      rethrow;
    }
  }

  ///
  /// 更新帳號
  ///
  Future<num> putStoreAccount(num id, StoreAccountPut data) {
    final json = data.toJson();
    if (data.password == null || data.password.isEmpty) {
      // 特殊: 沒有設定密碼時，不傳密碼
      json['password'] = null;
    }
    return apiProvider.put(
      'store-accounts/$id',
      data: json,
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('account_id')) {
            return json['account_id'];
          }
        }
        return 0;
      },
    );
  }

  ///
  /// 新增帳號
  ///
  Future<num> postStoreAccount(StoreAccountPost data) {
    return apiProvider.post(
      'store-accounts',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          if (json.containsKey('account_id')) {
            return json['account_id'];
          }
        }
        return 0;
      },
    );
  }
}
