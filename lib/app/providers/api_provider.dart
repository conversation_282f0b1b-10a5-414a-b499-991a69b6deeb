import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide FormData, Response, MultipartFile;
import 'package:logger/logger.dart';
import 'package:muyipork/app/models/brands_banners.dart';
import 'package:muyipork/app/models/brands_banners_put.dart';
import 'package:muyipork/app/models/brands_news.dart';
import 'package:muyipork/app/models/brands_news_put.dart';
import 'package:muyipork/app/models/brands_put.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/models/image_model.dart';
import 'package:muyipork/app/models/member_reports.dart';
import 'package:muyipork/app/models/pay_method.dart';
import 'package:muyipork/app/models/payment_bank.dart';
import 'package:muyipork/app/models/payment_bank_put.dart';
import 'package:muyipork/app/models/payment_cod.dart';
import 'package:muyipork/app/models/payment_instore.dart';
import 'package:muyipork/app/models/product_categories_put.dart';
import 'package:muyipork/app/models/reports_sales.dart' show ReportsSales;
import 'package:muyipork/app/models/reports_statements.dart'
    show ReportsStatements;
import 'package:muyipork/app/models/setting_all.dart' show SettingAll;
import 'package:muyipork/app/models/setting_firebase_post.dart';
import 'package:muyipork/app/models/setting_pay.dart';
import 'package:muyipork/app/models/setting_point.dart';
import 'package:muyipork/app/models/member_point_req.dart';
import 'package:muyipork/app/models/res_base.dart';
import 'package:muyipork/app/models/update_res.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/keys.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/models/brands_info.dart' show BrandsInfo;
import 'package:muyipork/app/models/city.dart';
import 'package:muyipork/app/models/district.dart';
import 'package:muyipork/app/models/payment.dart' as OkShop;
import 'package:muyipork/app/models/login_req.dart';
import 'package:muyipork/app/models/orders_orderid_print_put_req.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/app/models/login_res.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/app/models/products_productid_menu_get_res.dart';
import 'package:muyipork/app/models/setting_get_res.dart';
import 'package:muyipork/app/models/setting_put_res.dart';
import 'package:muyipork/app/providers/message_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

class ApiProvider extends GetxService {
  static const _UNAUTHORIZED = 401;
  final Dio dio;
  final PrefProvider prefProvider;
  final MessageProvider messageProvider;
  final String authority;
  final _statusCode = 0.obs;
  final _disposable = Completer();
  Logger get logger => prefProvider.boxProvider.logger;

  Dio get httpClient {
    dio.options.contentType = Headers.formUrlEncodedContentType;
    if (prefProvider.token.isNotEmpty) {
      dio.options.headers['Authorization'] = 'Bearer ${prefProvider.token}';
    }
    dio.options.headers['accept'] = 'application/json';
    return dio;
  }

  ApiProvider({
    @required this.dio,
    @required this.prefProvider,
    @required this.messageProvider,
  }) : authority = prefProvider.host;

  void _initObservable() {
    // status code 為未授權(401)，清空 token
    _statusCode.stream
        .debounceBuffer(2.seconds)
        .where((event) {
          if (event.length == 1) {
            return _UNAUTHORIZED == event.first;
          }
          if (event.length > 1) {
            return event.every((element) => _UNAUTHORIZED == element);
          }
          return false;
        })
        .takeUntil(_disposable.future)
        .listen((event) => prefProvider.token = '');
  }

  @override
  void onInit() {
    super.onInit();
    _initObservable();
    dio.options.validateStatus = _onStatusCode;
  }

  @override
  void onReady() {
    //
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  // 檢查 token 存取合法性
  bool _onStatusCode(int status) {
    _statusCode.value = status;
    return status != null && status >= 200 && status < 300;
  }

  ///
  /// 取得單一品牌資料
  ///
  Future<BrandsInfo> getBrandsInfo() {
    return getData<BrandsInfo>(
      unencodedPath: 'brands/info',
      creator: (json) => BrandsInfo.fromJson(json),
    );
  }

  ///
  /// 更新品牌資料
  ///
  Future<num> putBrands(BrandsPut data) {
    return put<num>(
      'brands',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('brand_id')) {
            return json['brand_id'];
          }
        }
        return 0;
      },
    );
  }

  ///
  /// 變更密碼
  ///
  Future<num> passwordReset(PasswordReset data) {
    return post<num>(
      'profile/password-reset',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          return json['account_id'];
        }
        return 0;
      },
    );
  }

  ///
  /// 登入
  ///
  Future<LoginRes> login(LoginReq req) async {
    // 特殊: remove authorization or get token error
    httpClient.options.headers.remove('Authorization');
    try {
      final ret = await post<LoginRes>(
        'login',
        data: req.toJson(),
        creator: (json) => LoginRes.fromJson(json),
      );
      // 特殊: Login 完成沒錯誤的話直接記下 token
      prefProvider.token = ret.token;
      return ret;
    } on DioError catch (e) {
      // 特殊: 更換錯誤訊息
      if (Keys.Code0100 == e.resError?.code) {
        throw '帳號密碼或商店代號錯誤';
      }
      throw e.responseMessage;
    }
  }

  ///
  /// 店長、店員登出
  ///
  Future<bool> getLogout() {
    return getResData<bool>(
      unencodedPath: 'logout',
      creator: (json) =>
          json.containsKey(Keys.IsLogout.value) &&
          json[Keys.IsLogout.value] == true,
    );
  }

  ///
  /// token 更新
  ///
  Future<LoginRes> getRenew() async {
    final res = await getResData<LoginRes>(
      unencodedPath: 'renew',
      creator: (json) => LoginRes.fromJson(json),
    );
    // 特殊: renew 完成的話直接記下 token
    prefProvider.token = res.token;
    return res;
  }

  ///
  /// 更新餐廳資料
  ///
  Future<SettingPutRes> putSetting(SettingGetData settingGetData) {
    return put<SettingPutRes>(
      'setting',
      data: settingGetData.toJson(),
      creator: (json) => SettingPutRes.fromJson(json),
    );
  }

  ///
  /// 取得餐廳設定資料
  /// 預設會跑 Cache 機制, updateCache 設為 true 來強制重新 request.
  ///
  Future<SettingGetRes> getSetting({bool updateCache = false}) async {
    logger.d('[ApiProvider] getSetting: updateCache($updateCache)');
    if (updateCache == true || prefProvider.setting == null) {
      logger.d('[ApiProvider] getSetting: updateCache');
      prefProvider.setting = await getResData(
        unencodedPath: 'setting',
        creator: (json) => SettingGetRes.fromJson(json),
      );
    }
    return prefProvider.setting;
  }

  ///
  /// 取得單一產品資料-點餐介面用
  ///
  Future<ProductsProductIdMenuGetRes> getProductsProductIdMenu(num productId) {
    return getData(
      unencodedPath: 'products/$productId/menu',
      creator: (json) => ProductsProductIdMenuGetRes.fromJson(json),
    );
  }

  ///
  /// 更新訂單 (PostOrdersReq modal 共用，可省略後半部參數, 用 OrdersPostReq.asPutReq())
  /// 更新訂單(不含產品)
  /// dio: 這個不會執行庫存檢查邏輯，結帳要使用這個
  ///
  Future<UpdateRes> putOrdersOrderId(
      int orderId, OrdersPostReq ordersPostReq) async {
    // server's rule, must have something
    ordersPostReq.ensureData();
    final json = ordersPostReq.toJson();
    json.remove('items');
    json.removeNull();
    return put<UpdateRes>(
      'orders/$orderId',
      data: json,
      creator: (json) => UpdateRes.fromJson(json),
    );
  }

  ///訂單是否列印
  Future<num> putOrdersOrderIdPrint(num orderId, OrdersOrderIdPrintPutReq req) {
    final data = req.toBody();
    data.removeNull();
    return put<num>(
      'orders/$orderId/print',
      data: data,
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && json[Keys.IsUpdated]) {
          if (json.containsKey('order_id')) {
            return json['order_id'] ?? 0;
          }
        }
        return 0;
      },
    );
  }

  /// 傳送訊息給單一會員
  Future<bool> putMemberMessage(num id, String message) {
    return put<bool>(
      'members/$id/push_message',
      data: {'message': message},
      creator: (json) {
        if (json.containsKey('is_send') && json['is_send']) {
          return true;
        }
        return false;
      },
    );
  }

  ///
  /// 城市列表
  ///
  Future<Iterable<City>> getCities() {
    return getList<City>(
      unencodedPath: 'cities',
      creator: (json) => City.fromJson(json),
    );
  }

  ///
  /// 鄉鎮列表
  ///
  Future<Iterable<District>> getDistricts() {
    return getList<District>(
      unencodedPath: 'cityareas',
      creator: (json) => District.fromJson(json),
    );
  }

  ///
  /// 圖片上傳(binary)(O)
  ///
  Future<Iterable<ImageModel>> postImages(List<String> files) {
    // if (files == null || files.isEmpty) {
    //   return Future.value(Iterable<ImageModel>.empty());
    // }
    final data = <String, dynamic>{};
    data['file'] = files.map((value) {
      // final file = File(value);
      final imageBytes = File(value).readAsBytesSync();
      final filename = value.split('/').last;
      // final filename = '${DateTime.now().millisecondsSinceEpoch}';
      return MultipartFile.fromBytes(imageBytes, filename: filename);
    }).toList();
    return post<Iterable<ImageModel>>(
      'images/files',
      data: data,
      creator: (json) {
        if (json.containsKey(Keys.Data)) {
          return List.from(json[Keys.Data]).map((e) => ImageModel.fromJson(e));
        }
        return null;
      },
    );
  }

  ///
  /// 取得所有圖片列表 (O)(目前沒有使用)
  ///
  Future<Iterable<ImageModel>> getImages() {
    return getList<ImageModel>(
      unencodedPath: 'images',
      creator: (value) => ImageModel.fromJson(value),
    );
  }

  ///
  /// 取得單一圖片資料 (O)(目前沒有使用)
  ///
  Future<ImageModel> getImage(num id) {
    return getData(
      unencodedPath: 'images/$id',
      creator: (value) => ImageModel.fromJson(value),
    );
  }

  ///
  /// 刪除單一圖片資料 (O)
  ///
  Future<bool> deleteImage(num id) {
    return delete<bool>(
      'images/$id',
      creator: (json) => json[Keys.IsDeleted],
    );
  }

  ///
  /// 取得零售金流設定
  ///
  Future<OkShop.Payment> getPayment() {
    return getData<OkShop.Payment>(
      unencodedPath: 'setting/payment',
      creator: (value) => OkShop.Payment.fromJson(value),
    );
  }

  /// 更新零售金流設定
  Future<bool> putPayment(PaymentBankPut data) {
    return put<bool>(
      'setting/payment',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(kKeyIsUpdated)) {
          return true;
        }
        return false;
      },
    );
  }

  /// 取得零售金流設定 - 店內付款
  Future<PaymentInstore> getPaymentInstore() {
    return getData<PaymentInstore>(
      unencodedPath: 'setting/payment/instore',
      creator: (value) => PaymentInstore.fromJson(value),
    );
  }

  /// 更新零售金流設定 - 店內付款 (2)
  Future<bool> putPaymentInstore(PaymentInstore data) {
    return put<bool>(
      'setting/payment/instore',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(kKeyIsUpdated)) {
          return true;
        }
        return false;
      },
    );
  }

  /// 取得零售金流設定 - 轉帳匯款 (O)
  Future<PaymentBank> getPaymentBank() {
    return getData<PaymentBank>(
      unencodedPath: 'setting/payment/bank',
      creator: (value) => PaymentBank.fromJson(value),
    );
  }

  /// 更新零售金流設定 - 轉帳匯款 (6)
  Future<bool> putPaymentBank(PaymentBankPut data) {
    return put<bool>(
      'setting/payment/bank',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(kKeyIsUpdated)) {
          return true;
        }
        return false;
      },
    );
  }

  ///
  /// 取得零售金流設定 - 貨到付款 (O)
  ///
  Future<PaymentCod> getPaymentCod() {
    return getData<PaymentCod>(
      unencodedPath: 'setting/payment/cod',
      creator: (value) => PaymentCod.fromJson(value),
    );
  }

  /// 更新零售金流設定 - 貨到付款 (4)
  Future<bool> putPaymentCod(PaymentCod data) {
    return put<bool>(
      'setting/payment/cod',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(kKeyIsUpdated)) {
          return true;
        }
        return false;
      },
    );
  }

  ///
  /// 取得餐廳＆零售設定
  ///
  Future<SettingAll> getSettingAll() {
    return getData<SettingAll>(
      unencodedPath: 'setting/all',
      creator: (value) => SettingAll.fromJson(value),
    );
  }

  ///
  /// 取得單一店家基本資料
  ///
  Future<BrandsBasic> getBrandsBasic() {
    return getData(
      unencodedPath: 'brands/basic',
      creator: (value) => BrandsBasic.fromJson(value),
    );
  }

  ///
  /// 更新單一店家基本資料
  ///
  Future<num> putBrandsBasic(BrandsBasic data) {
    final map = data.toJson();
    // special case
    map['custom_url'] = jsonEncode(data.customUrl);
    return put<num>(
      'brands/basic',
      data: map,
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && json[Keys.IsUpdated]) {
          if (json.containsKey('brand_id')) {
            return json['brand_id'];
          }
        }
        return 0;
      },
    );
  }

  ///
  /// 品牌圖片輪播列表
  ///
  Future<Iterable<BrandsBanners>> getBrandsBanners() {
    return getList<BrandsBanners>(
      unencodedPath: 'brands/banners',
      creator: (json) => BrandsBanners.fromJson(json),
    );
  }

  ///
  /// 更新品牌圖片輪播資料
  ///
  Future<bool> putBrandsBanners(BrandsBannersPut data) {
    final json = data.toJson();
    json.removeWhere((key, value) => key == null || value == null);
    return put<bool>(
      'brands/banners',
      data: json,
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          return true;
        }
        return false;
      },
    );
  }

  ///
  /// 品牌最新消息
  ///
  Future<BrandsNews> getBrandsNews() {
    return getData<BrandsNews>(
      unencodedPath: 'brands/news',
      creator: (value) => BrandsNews.fromJson(value),
    );
  }

  ///
  /// 更新品牌最新消息資料
  ///
  Future<bool> putBrandsNews(BrandsNewsPut data) {
    final json = data.toJson();
    json.removeWhere((key, value) => key == null || value == null);
    return put<bool>(
      'brands/news',
      data: json,
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          return true;
        }
        return false;
      },
    );
  }

  ///
  /// 設定 fcm token
  ///
  Future<bool> postSettingFirebase(SettingFirebasePost data) {
    final json = data.toJson();
    json.removeWhere((key, value) => key == null || value == null);
    return post<bool>(
      'setting/firebase',
      data: json,
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          return true;
        }
        return false;
      },
    );
  }

  ///
  /// 提供的付款方式列表
  ///
  Future<Iterable<PayMethod>> getPayMethods() {
    return getList<PayMethod>(
      unencodedPath: 'pay-methods',
      creator: (json) => PayMethod.fromJson(json),
    );
  }

  ///
  /// 取得APP付款設定
  ///
  Future<Iterable<SettingPay>> getSettingPay() {
    return getList<SettingPay>(
      unencodedPath: 'setting/pay',
      creator: (json) => SettingPay.fromJson(json),
    );
  }

  ///
  /// 批次更新APP付款設定（新增/更新/刪除）
  ///
  Future<bool> putSettingPay(final Iterable<SettingPay> list) {
    return put<bool>(
      'setting/pay',
      data: {
        'pays': jsonEncode([...list]),
      },
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated)) {
          return json[Keys.IsUpdated];
        }
        return false;
      },
    );
  }

  ///
  /// 排序APP付款方式
  ///
  Future<bool> postSettingPaySort(final Iterable<SettingPay> list) {
    final map = <String, dynamic>{};
    map[Keys.Data] = jsonEncode(list);
    return post<bool>(
      'setting/pay/sort',
      data: map,
      creator: (json) {
        if (json.containsKey(Keys.IsSorted.value) &&
            true == json[Keys.IsSorted.value]) {
          return true;
        }
        return false;
      },
    );
  }

  ///
  /// 日結單
  ///
  Future<ReportsStatements> getReportsStatements(
      final DateTime date, final StoreType storeType) async {
    try {
      final res = await getData<ReportsStatements>(
        unencodedPath: 'reports/statements/${date.yMd}',
        filter: <String, String>{
          'kind': '${storeType.index}',
        },
        creator: (value) => ReportsStatements.fromJson(value),
      );
      return res;
    } on DioError catch (e) {
      logger.e(e);
      // 特殊: 空值回傳成功
      if (Keys.Code1104 == e.resError?.code) {
        return ReportsStatements();
      }
      rethrow;
    }
  }

  ///
  /// 日結單-重新計算
  ///
  Future<bool> getReportsStatementsRefresh(final DateTime date) {
    return getResData<bool>(
      unencodedPath: 'reports/statements/${date.yMd}/refresh',
      creator: (json) =>
          json.containsKey(Keys.IsUpdated) && json[Keys.IsUpdated] == true,
    );
  }

  ///
  /// 紀錄日結單列印時間
  ///
  Future<bool> postReportsStatementsPrint(final DateTime date) {
    return post<bool>(
      'reports/statements/${date.yMd}/print',
      creator: (json) =>
          json.containsKey(Keys.IsPrint) && json[Keys.IsPrint] == true,
    );
  }

  ///
  /// 每日銷售明細
  ///
  Future<ReportsSales> getReportsSales(
      final DateTime date, final StoreType storeType) {
    return getData<ReportsSales>(
      unencodedPath: 'reports/sales/${date.yMd}',
      filter: <String, String>{
        'kind': '${storeType.index}',
      },
      creator: (value) => ReportsSales.fromJson(value),
    );
  }

  ///
  /// 每日銷售明細-重新計算
  ///
  Future<bool> getReportsSalesRefresh(final DateTime date) {
    return getResData<bool>(
      unencodedPath: 'reports/sales/${date.yMd}/refresh',
      creator: (json) =>
          json.containsKey(Keys.IsUpdated) && json[Keys.IsUpdated] == true,
    );
  }

  ///
  /// 紀錄每日銷售明細列印時間
  ///
  Future<bool> postReportsSalesPrint(final DateTime date) {
    return post<bool>(
      'reports/sales/${date.yMd}/print',
      creator: (json) =>
          json.containsKey(Keys.IsPrint) && json[Keys.IsPrint] == true,
    );
  }

  ///
  /// 新增/減少會員積點
  ///
  Future<num> postMemberPoint(num id, MemberPointReq req) {
    logger.d('[ApiProvider] postMemberPoint id($id), req(${req.toRawJson()})');
    final data = req.toJson();
    data.removeNull();
    return post<num>(
      'member-points/$id',
      data: data,
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          return json['points'];
        }
        return 0;
      },
    );
  }

  ///
  /// 取得點數設定資料
  ///
  Future<Iterable<SettingPoint>> getSettingPoints() {
    return getList<SettingPoint>(
      unencodedPath: 'setting/points',
      creator: (json) => SettingPoint.fromJson(json),
    );
  }

  ///
  /// 更新會員積點設定
  ///
  Future<bool> putSettingPoints(SettingPoint req) {
    req.cashRatio = req.cashRatio?.round() ?? 0;
    final data = req.toJson();
    data.removeNull();
    return put<bool>(
      'setting/points',
      data: data,
      creator: (json) =>
          json.containsKey(Keys.IsUpdated) && json[Keys.IsUpdated] == true,
    );
  }

  ///
  /// 產品最後異動時間(X)
  ///

  ///
  /// 會員分析
  ///
  Future<MemberReports> getMemberReports(num id) {
    return getData<MemberReports>(
      unencodedPath: 'member-reports/$id',
      creator: (value) => MemberReports.fromJson(value),
    );
  }

  ///
  /// 某產品分類下單一產品排序
  ///
  Future<bool> putProductCategories(ProductCategoriesPut req) {
    final data = req.toJson();
    data.removeNull();
    return put<bool>(
      'product-categories/sort',
      data: data,
      creator: (json) =>
          json.containsKey(Keys.IsSorted.value) &&
          json[Keys.IsSorted.value] == true,
    );
  }

  ///
  /// delete 通用模版
  ///
  Future<T> delete<T>(
    String unencodedPath, {
    Map<String, dynamic> data,
    T Function(Map<String, dynamic> json) creator,
  }) async {
    data ??= <String, dynamic>{};
    data.removeNull();
    final res = await httpClient.deleteUri<Map<String, dynamic>>(
      Uri.https(authority, unencodedPath),
      data: FormData.fromMap(data),
    );
    final ret = creator?.call(res.data);
    if (ret != null) {
      return ret;
    }
    if (res.data.containsKey(Keys.Error.value)) {
      final e = ResError.fromJson(res.data[Keys.Error.value]);
      throw e.localMessage;
    }
    throw unencodedPath;
  }

  ///
  /// post 通用模版
  ///
  Future<T> post<T>(
    String unencodedPath, {
    Map<String, dynamic> data,
    T Function(Map<String, dynamic> json) creator,
  }) async {
    data ??= <String, dynamic>{};
    data.removeNull();
    final res = await httpClient.postUri<Map<String, dynamic>>(
      Uri.https(authority, unencodedPath),
      data: FormData.fromMap(data),
    );
    if (res.data.containsKey(Keys.Error.value)) {
      final e = ResError.fromJson(res.data[Keys.Error.value]);
      throw e.localMessage;
    }
    final ret = creator?.call(res.data);
    if (ret != null) {
      return ret;
    }
    throw unencodedPath;
  }

  ///
  /// put 通用模版
  ///
  Future<T> put<T>(
    String unencodedPath, {
    Map<String, dynamic> data,
    T Function(Map<String, dynamic> json) creator,
    Map<String, dynamic> filter,
  }) async {
    data ??= <String, dynamic>{};
    data.removeNull();
    final res = await httpClient.putUri<Map<String, dynamic>>(
      Uri.https(authority, unencodedPath, filter?.toStringMap()),
      data: FormData.fromMap(data),
    );
    if (res.data.containsKey(Keys.Error.value)) {
      final e = ResError.fromJson(res.data[Keys.Error.value]);
      throw e.localMessage;
    }
    final ret = creator?.call(res.data);
    if (ret != null) {
      return ret;
    }
    throw unencodedPath;
  }

  ///
  /// 取得原始物件通用模板
  ///
  Future<T> getResData<T>({
    String unencodedPath,
    Map<String, dynamic> filter,
    T Function(dynamic json) creator,
  }) async {
    filter ??= <String, dynamic>{};
    filter.removeNull();
    final uri = Uri.https(authority, unencodedPath, filter.toStringMap());
    final res = await httpClient.getUri<Map>(uri);
    if (res.data != null && res.data.containsKey(Keys.Error.value)) {
      final e = ResError.fromJson(res.data[Keys.Error.value]);
      throw e.localMessage;
    }
    if (res.data != null) {
      return creator?.call(res.data);
    }
    throw uri.path;
  }

  ///
  /// 取得單一物件通用模板
  ///
  Future<T> getData<T>({
    String unencodedPath,
    Map<String, dynamic> filter,
    T Function(dynamic json) creator,
  }) async {
    filter ??= <String, dynamic>{};
    filter.removeNull();
    final uri = Uri.https(authority, unencodedPath, filter.toStringMap());
    final res = await httpClient.getUri<Map>(uri);
    if (res.data.containsKey(Keys.Data)) {
      final json = res.data[Keys.Data];
      return creator?.call(json);
    }
    if (res.data.containsKey(Keys.Error.value)) {
      final e = ResError.fromJson(res.data[Keys.Error.value]);
      throw e.localMessage;
    }
    throw uri.path;
  }

  ///
  /// 取得列表通用模板
  ///
  Future<Iterable<T>> getList<T>({
    String unencodedPath,
    Map<String, dynamic> filter,
    T Function(Map<String, dynamic> json) creator,
  }) async {
    filter ??= <String, dynamic>{};
    filter.removeNull();
    final uri = Uri.https(authority, unencodedPath, filter.toStringMap());
    final res = await httpClient.getUri<Map>(uri);
    if (res.data.containsKey(Keys.Error.value)) {
      final e = ResError.fromJson(res.data[Keys.Error.value]);
      if (Keys.Code1104 == e.code) {
        // 特殊: 空值回傳成功
        return <T>[];
      }
      throw e.localMessage;
    }
    if (res.data.containsKey(Keys.Data)) {
      final json = res.data[Keys.Data] ?? [];
      final list = List.from(json).map((e) => creator?.call(e));
      return <T>[...list];
    }
    throw uri.path;
  }
}
