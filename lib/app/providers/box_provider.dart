import 'package:get_storage/get_storage.dart';
import 'package:hive/hive.dart' as h;
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:logger/logger.dart';
import 'package:muyipork/app/models/jwt.dart';
import 'package:muyipork/extension.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:objectbox/objectbox.dart';
import 'package:okshop_model/okshop_model.dart';

class BoxProvider {
  final Logger logger;
  final h.Box userDefault;
  Store _store; // store instance
  Store get store => _store;

  var _domain = '';

  set token(String value) {
    if (value == null || value.isEmpty) {
      _domain = '';
    } else {
      final json = JwtDecoder.decode(value);
      final jwt = Jwt.fromJson(json);
      _domain = jwt.dir;
    }
    logger.d('[BoxProvider] set domain($_domain)');
  }

  BoxProvider({
    this.userDefault,
    this.logger,
  });

  Future<void> init() async {
    if (_store == null || _store.isClosed()) {
      _store = await _createStore();
    }
  }

  void close() {
    _store?.close();
    _store = null;
  }

  Future<Store> _createStore() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final path = p.join(docsDir.path, _domain);
    // logger.d('[BoxProvider] path: ' + path);
    if (Store.isOpen(path)) {
      // applicable when store is from other isolate
      return Store.attach(getObjectBoxModel(), path);
    }
    return openStore(directory: path);
  }

  Future<h.LazyBox> getLazyBox(String name) async {
    final fullName = '$name.$_domain';
    // logger.d('[BoxProvider] getLazyBox($fullName)');
    if (h.Hive.isBoxOpen(fullName)) {
      return h.Hive.lazyBox(fullName);
    }
    // final docsDir = await getApplicationDocumentsDirectory();
    // final path = p.join(docsDir.path, prefProvider.jwt.dir);
    return h.Hive.openLazyBox(fullName);
  }

  Future<bool> initGsBox(String name) {
    final fullName = '$name.$_domain';
    return GetStorage.init(fullName);
  }

  GetStorage getGsBox(String name) {
    final fullName = '$name.$_domain';
    // logger.d('[BoxProvider] getGsBox($fullName)');
    return GetStorage(fullName);
  }
}
