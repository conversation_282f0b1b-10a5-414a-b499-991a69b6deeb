import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/models/coupons_req.dart';
import 'package:muyipork/app/models/id_sort.dart';
import 'package:okshop_common/okshop_common.dart';

import 'api_provider.dart';

class CouponProvider {
  final ApiProvider apiProvider;
  final cached = <num, Coupon>{}.obs;
  final memberCached = <num, Map<num, Coupon>>{}.obs;
  final couponsLikeCached = <num, Coupon>{}.obs;

  CouponProvider({
    @required this.apiProvider,
  });

  Iterable<Coupon> getMemberCached(num id) {
    if (memberCached.containsKey(id)) {
      return memberCached[id].values;
    }
    return Iterable<Coupon>.empty();
  }

  ///
  /// 優惠卷詳情
  ///
  Future<Coupon> getCoupon(num id) async {
    final ret = await apiProvider.getData<Coupon>(
      unencodedPath: 'coupons/$id',
      creator: (value) => Coupon.fromJson(value),
    );
    cached[id] = ret;
    return ret;
  }

  ///
  /// 優惠卷列表
  ///
  Future<Iterable<Coupon>> getCoupons(CouponsReq filter) async {
    filter.limit = 500;
    final it = await apiProvider.getList<Coupon>(
      unencodedPath: 'coupons',
      filter: filter?.toJson(),
      creator: (value) => Coupon.fromJson(value),
    );
    final entries = it.map((e) => MapEntry(e.id, e));
    cached.addEntries(entries);
    return it;
  }

  ///
  /// 某會員優惠卷
  ///
  Future<Iterable<Coupon>> getMemberCoupons(num id,
      [num page, num limit]) async {
    // HACK:
    limit = 500;
    final it = await apiProvider.getList(
      unencodedPath: 'member-coupons/$id',
      filter: <String, dynamic>{
        'page': page ?? 1,
        'limit': limit ?? 500,
      },
      creator: (value) => Coupon.fromJson(value),
    );
    final entries = it.map((e) => MapEntry(e.id, e));
    memberCached[id] ??= <num, Coupon>{};
    memberCached[id].addEntries(entries);
    return it;
  }

  // TODO: 超過 500 會出現問題
  Future<Coupon> getMemberCoupon(num memberId, num memberCouponId) async {
    // 取得會員的優惠券
    final it = await getMemberCoupons(memberId);
    final coupon = it.firstWhere(
      (element) => element.id == memberCouponId,
      orElse: () => Coupon(),
    );
    // 由 coupon id 取得完整結構
    return getCoupon(coupon.couponId);
  }

  ///
  /// 發放會員優惠卷
  ///
  Future<bool> postMemberCoupons(num memberId, List<num> couponIds) {
    return apiProvider.post<bool>(
      'member-coupons/$memberId',
      data: <String, dynamic>{
        'coupon_id': jsonEncode(couponIds),
      },
      creator: (json) {
        if (json.containsKey(Keys.IsCreated)) {
          return json[Keys.IsCreated];
        }
        return false;
      },
    );
  }

  ///
  /// 建立折扣 C
  ///
  Future<num> postCoupon(Coupon data) async {
    data.title ??= '';
    data.discount ??= 0;
    final ret = await apiProvider.post<num>(
      'coupons_like',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          if (json.containsKey('coupon_id')) {
            return json['coupon_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      data.id = ret;
      couponsLikeCached[ret] = data;
    }
    return ret;
  }

  ///
  /// 更新折扣 U
  ///
  Future<num> putCoupon(Coupon data) async {
    data.title ??= '';
    data.discount ??= 0;
    final ret = await apiProvider.put<num>(
      'coupons_like/${data.id}',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('coupon_id')) {
            return json['coupon_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      couponsLikeCached[ret] = data;
    }
    return ret;
  }

  ///
  /// 取得折扣 R
  ///
  Future<Iterable<Coupon>> getCouponsLike(CouponsReq filter) async {
    filter.limit = 500;
    final list = await apiProvider.getList<Coupon>(
      unencodedPath: 'coupons_like',
      filter: filter.toJson(),
      creator: (json) => Coupon.fromJson(json),
    );
    // update cache
    final entries = list.map((e) => MapEntry(e.id, e));
    couponsLikeCached.addEntries(entries);
    return list;
  }

  ///
  /// 取得單一折扣 R
  ///
  Future<Coupon> getCouponsLikeById(num id) async {
    final ret = await apiProvider.getData(
      unencodedPath: 'coupons_like/$id',
      creator: (json) => Coupon.fromJson(json),
    );
    // update cache
    couponsLikeCached[ret.id] = ret;
    return ret;
  }

  ///
  /// 刪除折扣 D
  ///
  Future<bool> deleteCouponsLikeById(num id) async {
    final ret = await apiProvider.delete<bool>(
      'coupons_like/$id',
      creator: (json) {
        if (json.containsKey(Keys.IsDeleted)) {
          return json[Keys.IsDeleted];
        }
        return false;
      },
    );
    // update cache
    if (true == ret) {
      couponsLikeCached.remove(id);
    }
    return ret;
  }

  ///
  /// 排序折扣 S
  ///
  Future<bool> sortCouponLike(Iterable<IDSort> it) async {
    final ret = await apiProvider.post<bool>(
      'coupons_like/sort',
      data: {
        'data': jsonEncode(it.toList()),
      },
      creator: (json) {
        if (json.containsKey(Keys.IsSorted.value)) {
          return json[Keys.IsSorted.value];
        }
        return false;
      },
    );
    // update cache
    if (true == ret) {
      for (var item in it) {
        couponsLikeCached[item.id].sort = item.sort;
      }
      couponsLikeCached.refresh();
    }
    return ret;
  }
}
