import 'package:flutter/foundation.dart' show required;
import 'package:muyipork/app/models/line_pay_response.dart';

import 'api_provider.dart';

class LinePayProvider {
  final ApiProvider apiProvider;

  LinePayProvider({
    @required this.apiProvider,
  });

  ///
  /// line pay 條碼付款
  ///
  Future<LinePayResponse> linePayCode(num orderId, String otk) {
    return apiProvider.post(
      'linepay/$orderId/one_time_key',
      data: {
        "one_time_key": otk,
      },
      creator: (json) => LinePayResponse.fromJson(json),
    );
  }

  ///
  /// 確認付款狀態
  ///
  Future<LinePayResponse> linePayCheck(num orderId) {
    return apiProvider.getResData(
      unencodedPath: 'linepay/$orderId/check',
      filter: {},
      creator: (json) => LinePayResponse.fromJson(json),
    );
  }

  ///
  /// 退款
  ///
  Future<LinePayResponse> linePayRefund(num orderId) {
    return apiProvider.post(
      'linepay/$orderId/refund',
      data: {},
      creator: (json) => LinePayResponse.fromJson(json),
    );
  }

  ///
  /// 詳情
  ///
  Future<LinePayResponse> linePayDetail(num orderId) {
    return apiProvider.getResData(
      unencodedPath: 'linepay/$orderId/detail',
      filter: {},
      creator: (json) => LinePayResponse.fromJson(json),
    );
  }
}
