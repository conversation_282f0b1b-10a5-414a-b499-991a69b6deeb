import 'package:bpscm/bpscm.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:muyipork/app/models/invoice_data.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import 'box_provider.dart';

class LocalInvoiceProvider {
  final InvoiceProvider invoiceProvider;
  final BoxProvider boxProvider;
  Logger get logger => boxProvider.logger;

  LocalInvoiceProvider({
    @required this.invoiceProvider,
    @required this.boxProvider,
  });

  Future<String> getInvoiceNumber({String seller, DateTime date}) async {
    final invoiceData = _popInvoiceData();
    if (invoiceData != null && invoiceData.verify(seller: seller, date: date)) {
      // fetch new invoice number from remote
      // fetchInvoiceNumber(seller: seller, date: date);
      return invoiceData.number;
    }
    final invoiceNumber = await invoiceProvider.getInvoiceNumber(
      seller: seller,
      date: date,
    );
    // fetch new invoice number from remote
    // fetchInvoiceNumber(seller: seller, date: date);
    return invoiceNumber;
  }

  Future<void> fetchInvoiceNumber(
      {String seller, DateTime date, int count = 1}) async {
    for (var i = 0; i < count; i++) {
      try {
        await _fetchInvoiceNumber(seller: seller, date: date);
      } catch (e) {
        logger.e('fetchInvoiceNumber error: $e');
      }
    }
  }

  Future<void> _fetchInvoiceNumber({
    String seller,
    DateTime date,
  }) async {
    final invoiceNumber = await invoiceProvider.getInvoiceNumber(
      seller: seller,
      date: date,
    );
    _pushInvoiceData(InvoiceData(
      seller: seller,
      date: date.millisecondsSinceEpoch,
      number: invoiceNumber,
    ));
  }

  ///
  /// 取得預存發票資料
  ///
  InvoiceData _popInvoiceData() {
    final box = boxProvider.getGsBox(kBoxInvoice);
    final keys = [...box.getKeys()];
    if (keys.isEmpty) {
      return null;
    }
    final key = keys.first;
    final json = box.read(key);
    box.remove(key);
    return InvoiceData.fromJson(json);
  }

  @visibleForTesting
  InvoiceData popInvoiceData() => _popInvoiceData();

  ///
  /// 儲存發票資料
  ///
  void _pushInvoiceData(InvoiceData value) {
    final box = boxProvider.getGsBox(kBoxInvoice);
    box.write(value.number, value.toJson());
  }

  @visibleForTesting
  void pushInvoiceData(InvoiceData value) => _pushInvoiceData(value);
}
