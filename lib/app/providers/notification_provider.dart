import 'dart:convert';
import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:muyipork/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:muyipork/app/modules/orders/controllers/orders_controller.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:permission_handler/permission_handler.dart';

class NotificationProvider {
  final channel = AndroidNotificationChannel(
    'order', // id
    '訂單', // title
    '接收到訂單時的通知', // description
    importance: Importance.high,
  );

  final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  final OrderProvider orderProvider;
  Logger get logger => orderProvider.logger;

  NotificationProvider({
    this.orderProvider,
  });

  Future<void> init() async {
    // 使用 permission_handler 取得推播權限
    var status = await Permission.notification.status;
    if (!status.isGranted) {
      // The permission is not granted, request it. status = await Permission.notification.request();
      status = await Permission.notification.request();
      logger.d('status: $status');
    }

    /// Create an Android Notification Channel.
    ///
    /// We use this channel in the `AndroidManifest.xml` file to override the
    /// default FCM channel to enable heads up notifications.
    // await flutterLocalNotificationsPlugin
    //     .resolvePlatformSpecificImplementation<
    //         AndroidFlutterLocalNotificationsPlugin>()
    //     ?.createNotificationChannel(channel);

    // final notificationAppLaunchDetails =
    //     await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
    // String initialRoute = HomePage.routeName;
    // if (notificationAppLaunchDetails?.didNotificationLaunchApp ?? false) {
    //   selectedNotificationPayload = notificationAppLaunchDetails!.payload;
    //   initialRoute = SecondPage.routeName;
    // }
    // android
    const initializationSettingsAndroid =
        AndroidInitializationSettings('ic_notification');
    // ios
    /// Note: permissions aren't requested here just to demonstrate that can be
    /// done later
    final initializationSettingsIOS = IOSInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
      onDidReceiveLocalNotification: (
        int id,
        String title,
        String body,
        String payload,
      ) async {
        // didReceiveLocalNotificationSubject.add(
        //   ReceivedNotification(
        //     id: id,
        //     title: title,
        //     body: body,
        //     payload: payload,
        //   ),
        // );
      },
    );
    // mac
    const initializationSettingsMacOS = MacOSInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    final initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
      macOS: initializationSettingsMacOS,
    );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onSelectNotification: _onSelectNotification,
    );
  }

  Future<void> _onSelectNotification(String payload) async {
    if (payload != null && payload.isNotEmpty) {
      final jsonObject = jsonDecode(payload);
      if ('order' == jsonObject['type']) {
        Get.until((route) => route.isFirst);
        await 600.milliseconds.delay();
        final orderId = num.tryParse(jsonObject['order_id']) ?? 0;
        final value = await Get.toNamed(
          Routes.ORDER_DETAIL,
          parameters: {
            Keys.Tag: '$orderId',
            Keys.Id: '$orderId',
          },
        );
        if (value != null) {
          final orderRoot =
              await orderProvider.getOrderDetail(orderId, cacheFirst: true);
          final controller = Get.find<OrdersController>(tag: '1');
          final orderSummary = orderRoot.data.asOrderSummary();
          switch (value) {
            case OrderDetailViewShortCut.AcceptOrder:
              // TODO: move to order provider
              controller.acceptOrder(orderSummary);
              break;
            case OrderDetailViewShortCut.RejectOrder:
              // TODO: move to order provider
              controller.rejectOrder(orderSummary);
              break;
            default:
              break;
          }
        }
      }
    }
  }

  Future<void> showNotification(RemoteMessage message) async {
    final androidPlatformChannelSpecifics = AndroidNotificationDetails(
      channel.id,
      channel.name,
      channel.description,
      priority: Priority.high,
      importance: Importance.max,
      playSound: true,
      icon: 'ic_notification',
      sound: RawResourceAndroidNotificationSound('new_order'),
    );
    const iOSPlatformChannelSpecifics = IOSNotificationDetails(
      threadIdentifier: 'muyipork',
      sound: 'new_order.wav',
    );
    // mac
    const macOSPlatformChannelSpecifics =
        MacOSNotificationDetails(sound: 'new_order.wav');
    final platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
      macOS: macOSPlatformChannelSpecifics,
    );
    final orderNumber = message.data['order_number'];
    final id = num.tryParse(message.data['order_id']);
    try {
      await flutterLocalNotificationsPlugin.show(
        id,
        '您有新訂單',
        '訂單編號 $orderNumber',
        platformChannelSpecifics,
        payload: jsonEncode(message.data),
      );
    } catch (e) {
      // logger.e(e);
      log(e.toString());
    }
  }
}
