import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart' hide Condition;
import 'package:logger/logger.dart';
import 'package:muyipork/app/models/orders_combining_post_req.dart';
import 'package:muyipork/app/models/orders_orderid_invoice_post_req.dart';
import 'package:muyipork/app/models/orders_orderid_status_put_qry.dart';
import 'package:muyipork/app/models/orders_orderid_status_put_req.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/app/models/setting_get_res.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/app/models/orders_get_res.dart';
import 'package:muyipork/app/models/order_root.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/extension.dart';

import 'box_provider.dart';

class OrderProvider {
  final MemberProvider memberProvider;
  ApiProvider get apiProvider => memberProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => memberProvider.boxProvider;
  Logger get logger => boxProvider.logger;
  final _detailCached = <num, OrderRoot>{}.obs;
  // 分類
  // Iterable<OrderSummary> get activeOrders => _activeOrders;
  Iterable<OrderSummary> get activeOrders {
    final box = boxProvider.getGsBox(kBoxOrder);
    final list = List.from(box.getValues(), growable: false);
    return list.map((e) => OrderSummary.fromJson(e)).where((order) {
      // 不顯示子訂單
      if (order.isSlave) return false;
      return true == order.status?.orderStatus?.isActive;
    });
  }

  final _activeOrders = <OrderSummary>[].obs;
  final _completedOrders = <OrderSummary>[].obs;
  // 進行中餐飲
  final _activeDinnerOrders = <OrderSummary>[].obs;
  num get activeDinnerOrdersCount => _activeDinnerOrders.length;
  // Iterable<OrderSummary> get activeDinnerOrders {
  //   return activeOrders.where((element) => element.orderType.isDinner);
  // }

  // 進行中零售
  final _activeRetailOrders = <OrderSummary>[].obs;
  num get activeRetailOrdersCount => _activeRetailOrders.length;
  // Iterable<OrderSummary> get activeRetailOrders {
  //   return activeOrders.where((element) => element.orderType.isRetail);
  // }
  // final completedDinnerOrders = <OrderSummary>[].obs;
  // final completedRetailOrders = <OrderSummary>[].obs;
  // Iterable<OrderSummary> get activeOrders => _activeOrders;
  // Iterable<OrderSummary> get activeDinnerOrders => _activeDinnerOrders;
  // Iterable<OrderSummary> get activeRetailOrders => _activeRetailOrders;
  // Iterable<OrderSummary> get completedOrders => _completedOrders;
  // Iterable<OrderSummary> get completedDinnerOrders => _completedDinnerOrders;
  // Iterable<OrderSummary> get completedRetailOrders => _completedRetailOrders;
  final _currentOrders = <OrderSummary>[].obs;
  final _nextOrders = <OrderSummary>[].obs;

  num get currentOrdersCount => _currentOrders.length;
  num get nextOrdersCount => _nextOrders.length;

  OrderProvider({
    @required this.memberProvider,
  });

  Stream<List<OrderSummary>> observeActiveOrders() {
    return _activeDinnerOrders.stream.merge(_activeRetailOrders.stream);
  }

  /// 呼叫時機:
  /// 1. 時段變更
  /// 2. 進行中單變更
  void refreshOrdersWithTime(
    StoreType storeType, {
    OpenHoursDateTime inHours,
    OpenHoursDateTime outOfHours,
  }) {
    final activeOrders = storeType == StoreType.Dinner
        ? _activeDinnerOrders
        : _activeRetailOrders;
    // 本時段(最接近或包含)
    final current = activeOrders.where((element) {
      final x = element.mealAtLocalDateTime().millisecondsSinceEpoch;
      final y =
          inHours?.end?.millisecondsSinceEpoch ?? double.maxFinite.round();
      return x <= y;
    });
    _currentOrders.assignAll(current);
    // 下時段()
    final next = activeOrders.where((element) {
      final x = element.mealAtLocalDateTime().millisecondsSinceEpoch;
      final y = outOfHours?.end?.millisecondsSinceEpoch ?? 0;
      return x > y;
    });
    _nextOrders.assignAll(next);
  }

  void init(Completer _disposable) {
    // final completedStream = completedOrders.stream.asBroadcastStream();
    // 已完成餐飲
    // completedStream.listen((orders) {
    //   final it = orders.where((element) => element.orderType.isDinner);
    //   completedDinnerOrders.assignAll(it);
    // });
    // 已完成零售
    // completedStream.listen((orders) {
    //   final it = orders.where((element) => element.orderType.isRetail);
    //   completedRetailOrders.assignAll(it);
    // });
    final activeStream = _activeOrders.stream.asBroadcastStream();
    // observe activeOrders, 更新進行中餐飲
    activeStream.takeUntil(_disposable.future).listen((orders) {
      final it = orders.where((element) => element.orderType.isDinner);
      _activeDinnerOrders.assignAll(it);
    });
    // observe activeOrders, 更新進行中零售
    activeStream.takeUntil(_disposable.future).listen((orders) {
      final it = orders.where((element) => element.orderType.isRetail);
      _activeRetailOrders.assignAll(it);
    });
    final box = boxProvider.getGsBox(kBoxOrder);
    logger.d('[OrderProvider] init: erase box');
    box.erase();
    final cachedStream =
        box.watch().debounce(300.milliseconds).asBroadcastStream();
    // observe mem, 更新進行中單
    cachedStream.takeUntil(_disposable.future).listen((event) {
      final list = List.from(box.getValues(), growable: false);
      final it = list.map((e) => OrderSummary.fromJson(e)).where((order) {
        // 不顯示子訂單
        if (order.isSlave) return false;
        return true == order.status?.orderStatus?.isActive;
      });
      logger.d(
          '[OrderProvider] mem changed(${list.length}), activeOrders(${it.length})');
      _activeOrders.assignAll(it);
    });
    // observe mem, 更新已完成單
    cachedStream.takeUntil(_disposable.future).listen((event) {
      final list = List.from(box.getValues(), growable: false);
      final it = list.map((e) => OrderSummary.fromJson(e)).where((order) {
        // 不顯示子訂單
        if (order.isSlave) return false;
        return true != order.status?.orderStatus?.isActive;
      });
      logger.d(
          '[OrderProvider] mem changed(${list.length}), completedOrders(${it.length})');
      _completedOrders.assignAll(it);
    });
    // observe db
    getDbQueryStream()
        .debounce(300.milliseconds)
        // .asyncExpand((query) => query.stream())
        // .asyncMap((query) async {
        //   logger.d(
        //       '[OrdersController] _initObservable: db changed, count(${query.count()})');
        //   query.limit = kLimit;
        //   await for (var order in query.stream()) {
        //     // logger.d('[OrderProvider] db changed, order(${order.id})');
        //     box.write('${order.id}', order.toJson());
        //   }
        // })
        .takeUntil(_disposable.future)
        .listen((query) {
      logger.d(
          '[OrdersController] _initObservable: db changed, count(${query.count()})');
      query.limit = kLimit;
      final box = boxProvider.getGsBox(kBoxOrder);
      for (var order in query.find()) {
        // logger.d('[OrderProvider] db changed, order(${order.id})');
        order.member = order.customer.target;
        box.write('${order.id}', order.toJson());
      }
    });
  }

  QueryBuilder<OrderSummary> _createQueryBuilder([OrderReq req]) {
    final box = boxProvider.store.box<OrderSummary>();
    final builder = box.query(req?.toQueryCondition());
    final customerCondition = req?.toMemberQueryCondition();
    if (customerCondition != null) {
      builder.link(OrderSummary_.customer, customerCondition);
    }
    builder.order(OrderSummary_.updatedAt, flags: Order.descending);
    // builder.order(OrderSummary_.id, flags: Order.descending);
    return builder;
  }

  Stream<OrderSummary> getQueryStream([OrderReq req]) async* {
    final builder = _createQueryBuilder(req);
    // builder.order(OrderSummary_.id, flags: Order.descending);
    final query = builder.build();
    query.limit = min(kLimit, req?.limit ?? kLimit);
    query.offset = req?.offset ?? 0;
    yield* query.stream();
    logger.d('[OrderProvider] getQueryStream: close query');
    query.close();
  }

  ///
  /// 訂單列表 (含分頁)
  ///
  Future<OrderPage> getOrderPage([OrderReq req]) async {
    req ??= OrderReq();
    req.page ??= 1;
    req.limit ??= 50;
    // HACK: test limit
    // req.limit = 2;
    final ret = await apiProvider.getResData<OrderPage>(
      unencodedPath: 'orders',
      filter: req.toJson(),
      creator: (json) => OrderPage.fromJson(json),
    );
    ret.data ??= <OrderSummary>[];
    final timestamp = DateTime.now().toUtc().millisecondsSinceEpoch;
    logger.d('[OrderProvider] Begin update');
    for (var order in ret.data) {
      // update member
      final memberId = order.member?.id ?? 0;
      // logger.d('[OrderProvider] memberId: $memberId');
      if (memberId > 0) {
        // logger.d('[OrderProvider] contains memberId: $memberId');
        final member =
            await memberProvider.getMember(memberId, cacheFirst: true);
        // logger.d('[OrderProvider] member: $member');
        member.nicknameStore = order.member?.nicknameStore ?? '';
        order.member = member;
        order.customer.target = member;
      }
      // update item count
      final status = order.status?.orderStatus ?? OrderStatus.Max;
      // final activeOrder = ORDERS_STATUS_ACTIVE.contains(status);
      final orderFromLine = order.source?.orderSource?.isLine ?? false;
      order.itemCount ??= 0;
      final zeroItemCount = 0 == order.itemCount;
      if (orderFromLine && zeroItemCount) {
        final orderDetail = await getOrderDetail(order.id, cacheFirst: true);
        order.itemCount = orderDetail.normalItemsCount;
      } else {
        order.itemCount = order.itemCount.toInt();
      }
      // timestamp
      order.timestamp = timestamp;
      // rawJson
      order.rawJson = order.toRawJson();
    }
    logger.d('[OrderProvider] End update');
    // insert db
    final box = boxProvider.store.box<OrderSummary>();
    try {
      box.putMany(ret.data);
    } catch (e) {
      logger.e(e);
      _writeToMem(ret.data);
    }
    return ret;
  }

  // 取得最新的訂單
  OrderSummary getLatestOrderFromLocalStorage() {
    final box = boxProvider.store.box<OrderSummary>();
    final builder = box.query(OrderSummary_.updatedAt.notNull());
    builder.order(OrderSummary_.updatedAt, flags: Order.descending);
    final query = builder.build();
    return query.findFirst();
  }

  Future<void> fetchLatestOrders() async {
    const limit = kLimit;
    // 取得本地訂單
    final box = boxProvider.store.box<OrderSummary>();
    final boxCount = box.count();
    final lastPage = boxCount ~/ limit;
    // // 取得遠端訂單數量
    // final pagi = await getPagination(limit);
    // pagi.total ??= 0; // 避免 null
    // pagi.lastPage ??= 1; // 避免 null
    var page = 1;
    do {
      logger.d('[OrderProvider] fetchLatestOrders: 下載最新$limit筆訂單 page($page)');
      // final builder = box.query(OrderSummary_.updatedAt.notNull());
      // builder.order(OrderSummary_.updatedAt, flags: Order.descending);
      // final query = builder.build();
      // query.limit = limit;
      // query.offset = (page - 1) * limit;
      // final current = query.find();
      // query.close();
      final filter = OrderReq(
        page: page,
        limit: limit,
        sort: "desc",
        sortType: "updated_at",
      );
      final orders = await getOrders(filter);
      break;
      // final everyUpdated = orders.every((order) {
      //   final index = current.indexWhere((element) => element.id == order.id);
      //   if (index == -1) {
      //     // new
      //     return false;
      //   }
      //   if (current.elementAt(index).updatedAt != order.updatedAt) {
      //     // changed
      //     return false;
      //   }
      //   // same
      //   return true;
      // });
      // if (everyUpdated) {
      //   logger.d('[OrderProvider] everyUpdated');
      //   break;
      // }
      page++;
    } while (page < lastPage);
    logger.d('[OrderProvider] 結束更新訂單');
  }

  Stream<Query<OrderSummary>> getDbQueryStream([OrderReq req]) {
    final builder = _createQueryBuilder(req);
    return builder.watch();
  }

  Future<Pagination> getPagination(num limit) async {
    final req = OrderReq(
      page: 1,
      limit: limit,
    );
    final ret = await getOrderPage(req);
    return ret.pagination;
  }

  Future<void> fetchAllOrders() async {
    const limit = kLimit;
    // 取得遠端訂單數量
    final pagi = await getPagination(limit);
    pagi.total ??= 0; // 避免 null
    pagi.lastPage ??= 1; // 避免 null
    logger.d('[OrderProvider] fetchAllOrders: 遠端訂單數量(${pagi.total})');
    // 取得本地訂單數量
    final box = boxProvider.store.box<OrderSummary>();
    final orderCount = box.count();
    logger.d('[OrderProvider] fetchAllOrders: 本地訂單數量($orderCount)');
    // 比對數量
    if (orderCount < pagi.total) {
      var page = 1 + orderCount ~/ limit;
      do {
        logger.d(
            '[OrderProvider] fetchAllOrders: 續載訂單 page($page/${pagi.lastPage})');
        await 5.seconds.delay();
        final filter = OrderReq(
          page: page,
          limit: limit,
          sort: "desc",
          // sortType: "updated_at",
        );
        final orders = await getOrders(filter);
        if (orders.length < limit) {
          break;
        }
        page++;
      } while (page <= pagi.lastPage);
      logger.d('[OrderProvider] fetchAllOrders: 續載訂單結束');
      // 新訂單
      page = 1;
      while (box.count() < pagi.total) {
        logger.d('[OrderProvider] fetchAllOrders: 新訂單 page($page)');
        await 5.seconds.delay();
        final filter = OrderReq(
          page: page,
          limit: limit,
          sort: "desc",
          // sortType: "updated_at",
        );
        await getOrders(filter);
        page++;
        if (page > pagi.lastPage) {
          break;
        }
      }
      logger.d('[OrderProvider] fetchAllOrders: 新訂單結束');
    } else {
      logger.d('[OrderProvider] fetchAllOrders: 本地訂單數量已同步');
    }
    logger.d('[OrderProvider] fetchAllOrders: 完成');
  }

  ///
  /// 訂單列表
  ///
  Future<Iterable<OrderSummary>> getOrders([OrderReq req]) async {
    final ret = await getOrderPage(req);
    return ret.data ?? <OrderSummary>[];
  }

  Future<OrderRoot> getOrderDetail(num id, {bool cacheFirst}) async {
    if (true == cacheFirst && _detailCached.containsKey(id)) {
      return _detailCached[id];
    }
    final ret = await apiProvider.getResData(
      unencodedPath: 'orders/$id',
      creator: (json) => OrderRoot.fromJson(json),
    );
    // 規格附加品項取代所有的+0
    ret.normalItems.forEach((item) {
      item.productSpec1 = item.productSpec1.replaceAll('+0', '');
    });
    // 下載回來後更新需要的資訊
    ret.refresh();
    _detailCached[id] = ret;
    // 更新 db
    final order = ret.data.asOrderSummary();
    // update member
    final memberId = order.member?.id ?? 0;
    if (memberId > 0) {
      final member = await memberProvider.getMember(memberId, cacheFirst: true);
      member.nicknameStore = order.member?.nicknameStore ?? '';
      order.member = member;
      order.customer.target = member;
    }
    order.itemCount = ret.normalItemsCount;
    // timestamp
    order.timestamp ??= DateTime.now().toUtc().millisecondsSinceEpoch;
    // rawJson
    order.rawJson ??= order.toRawJson();
    order.updatedAt ??= order.createdAt;
    order.updatedAt ??= order.createdAt;
    // 複製 detail 缺少的資料
    final box = boxProvider.store.box<OrderSummary>();
    if (box.contains(id)) {
      final current = box.get(id);
      order.masterId = current.masterId;
      order.isPrint = current.isPrint;
    }
    try {
      box.put(order);
    } catch (e) {
      logger.e(e);
      _writeToMem([order]);
    }
    // 設定子訂單 masterId
    final children = (ret.subOrder ?? <OrderDetail>[]).map((e) {
      final child = e.asOrderSummary();
      child.masterId = order.id;
      child.updatedAt = order.updatedAt;
      return child;
    }).toList(growable: false);
    if (children.isNotEmpty) {
      try {
        box.putMany(children);
      } catch (e) {
        logger.e(e);
        _writeToMem(children);
      }
    }
    return ret;
  }

  void _writeToMem(Iterable<OrderSummary> orders) {
    final box = boxProvider.getGsBox(kBoxOrder);
    for (var order in orders) {
      order.member = order.customer.target;
      box.write('${order.id}', order.toJson());
    }
  }

  bool containsDetail(num id) => _detailCached.containsKey(id);

  OrderRoot getOrderDetailFromLocalStorage(num id) {
    return containsDetail(id) ? _detailCached[id] : null;
  }

  ///
  /// 訂單總數
  ///
  // Future<OrderTotal> getOrdersTotal() {
  //   return apiProvider.getData(
  //     unencodedPath: 'orders/total',
  //     creator: (json) => OrderTotal.fromJson(json),
  //   );
  // }

  ///
  /// 新增訂單
  ///
  Future<num> postOrders(OrdersPostReq data) async {
    data.ensureData();
    final orderId = await apiProvider.post<num>(
      'orders',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          if (json.containsKey('order_id')) {
            return json['order_id'];
          }
        }
        return 0;
      },
    );
    if (orderId is num && orderId > 0) {
      await getOrderDetail(orderId);
    }
    return orderId;
  }

  ///
  /// 訂單發票
  ///
  Future<num> postOrderInvoice(num id, OrdersOrderIdInvoicePostReq data) async {
    final orderId = await apiProvider.post(
      '/orders/$id/invoice',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('order_id')) {
            return json['order_id'] as num;
          }
        }
        return 0;
      },
    );
    if (orderId is num && orderId > 0) {
      await getOrderDetail(orderId);
    }
    return orderId;
  }

  ///
  /// 更新發票為作廢，返回發票號碼
  ///
  Future<String> putOrderInvoice(num id) async {
    final invoiceNumber = await apiProvider.put(
      '/orders/$id/invoice',
      data: {},
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('invoice_number')) {
            return json['invoice_number'] as String;
          }
        }
        return '';
      },
    );
    if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
      // 更新本地資料
      await getOrderDetail(id);
    }
    return invoiceNumber;
  }

  ///
  /// 更新訂單 (後結)
  ///
  Future<num> putOrder(num id, OrdersPostReq data) async {
    // server's rule, must have something
    data.ensureData();
    final json = data.toJson();
    // 移除品項
    json.remove('items');
    final orderId = await apiProvider.put(
      'orders/$id',
      data: json,
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('order_id')) {
            return json['order_id'];
          }
        }
        return 0;
      },
    );
    if (orderId is num && orderId > 0) {
      await getOrderDetail(orderId);
    }
    return orderId;
  }

  ///
  /// 合併訂單
  ///
  Future<num> postOrdersCombining(OrdersCombiningPostReq data) async {
    final orderId = await apiProvider.post(
      'orders/combining',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          if (json.containsKey('order_id')) {
            return json['order_id'];
          }
        }
        return 0;
      },
    );
    if (orderId is num && orderId > 0) {
      await getOrderDetail(orderId);
    }
    return orderId;
  }

  ///
  /// 更新訂單 (PostOrdersReq modal 共用，可省略後半部參數, 用 OrdersPostReq.asPutReq())
  /// 更新訂單(含產品)
  ///
  Future<num> putOrderReedit(num id, OrdersPostReq data) async {
    // server's rule, must have something
    data.ensureData();
    final json = data.toJson();
    json.removeNull();
    final orderId = await apiProvider.put(
      'orders/$id/reedit',
      data: json,
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('order_id')) {
            return json['order_id'];
          }
        }
        return 0;
      },
    );
    if (orderId is num && orderId > 0) {
      await getOrderDetail(orderId);
    }
    return orderId;
  }

  ///
  /// 改變訂單狀態
  ///
  Future<num> putOrderStatus(num id, OrdersOrderIdStatusPutQry filter,
      OrdersOrderIdStatusPutReq data) async {
    // 如果會員不存在，不是 line 訂單，取消推送
    final box = boxProvider.store.box<OrderSummary>();
    if (box.contains(id)) {
      final order = box.get(id);
      if (order == null ||
          order.source != OrderSource.Line.index ||
          order.member == null ||
          order.member.id == 0) {
        filter.isPushMsg = Switcher.Off.index;
      }
    }

    num orderId = id;
    try {
      orderId = await apiProvider.put(
        'orders/$id/status',
        data: data.toJson(),
        filter: filter.toJson(),
        creator: (json) {
          if (json.containsKey(Keys.IsUpdated) &&
              true == json[Keys.IsUpdated]) {
            if (json.containsKey('order_id')) {
              return json['order_id'];
            }
          }
          return 0;
        },
      );
    } catch (e) {
      if (e == 'LINE 訊息無法傳遞：對象不存在') {
        logger.e(e);
      } else {
        rethrow;
      }
    }
    if (orderId is num && orderId > 0) {
      await getOrderDetail(orderId);
    }
    return orderId;
  }
}
