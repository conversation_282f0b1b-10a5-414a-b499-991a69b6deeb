import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:muyipork/app/models/printer_extension.dart';
import 'package:muyipork/app/models/printer_task.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

import 'box_provider.dart';

class PrinterProvider {
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Logger get logger => boxProvider.logger;
  final printerExtension = <num, PrinterExtension>{}.obs;

  PrinterProvider({
    @required this.apiProvider,
  });

  SettingLabel getCloudPrinter(num id) {
    final box = boxProvider.getGsBox(kBoxCloudPrinter);
    if (box.hasData('$id')) {
      final json = box.read('$id');
      return SettingLabel.fromJson(json);
    }
    return null;
  }

  SettingLabel getPrinter(num id) {
    final box = boxProvider.getGsBox(kBoxPrinter);
    if (box.hasData('$id')) {
      final json = box.read('$id');
      return SettingLabel.fromJson(json);
    }
    return null;
  }

  void clearAllTasks() {
    for (var printerX in printerExtension.values) {
      printerX.tasks = <PrinterTask>[];
    }
  }

  Future<void> flushAll() async {
    // logger.d('[PrinterProvider] flushAll');
    final box = boxProvider.getGsBox(kBoxPrinter);
    for (var printerId in box.getKeys()) {
      await _flushPrinter(int.tryParse(printerId));
    }
    // Iterable<Future<void>> children() sync* {
    //   final box = boxProvider.getGsBox(kBoxPrinter);
    //   for (var printerId in box.getKeys()) {
    //     yield _flushPrinter(int.tryParse(printerId));
    //   }
    // }

    // await Future.wait(children());
  }

  Future<void> _flushPrinter(num printerId) async {
    final box = boxProvider.getGsBox(kBoxPrinter);
    if (box.hasData('$printerId') && printerExtension.containsKey(printerId)) {
      final printerX = printerExtension[printerId];
      if (printerX.containsTask && printerX.isAvailable) {
        final json = box.read('$printerId');
        final printer = SettingLabel.fromJson(json);
        final tasks = [...printerX.tasks.where((task) => !task.expired)];
        printerX.tasks = <PrinterTask>[];
        try {
          printerX.printing = Switcher.On.index;
          await printer.flushQueue(tasks);
          printerX.resetDelay();
        } catch (e) {
          logger.e('[PrinterProvider] flushPrinter: $e');
          logger.i('printerId($printerId): 延長重試時間');
          printerX.appendDelay();
        } finally {
          printerX.printing = Switcher.Off.index;
          // 列印失敗的 task 重新加入列印工作
          printerX.insertAll(tasks.where((task) => task.isPending));
        }
      }
    }
  }

  void pushTask(num printerId, List<int> task) {
    logger.d('[PrinterProvider] pushTask: printerId($printerId)');
    final printerTask = PrinterTask(
      printerId: printerId,
      bytes: task,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      status: TaskStatus.Pending.index,
    );
    printerExtension[printerId] ??= PrinterExtension(
      availableAt: 0,
      times: 0,
      printing: Switcher.Off.index,
      tasks: <PrinterTask>[],
    );
    printerExtension[printerId].addAll([printerTask]);
  }

  Iterable<SettingLabel> getSettingLabelsFromStorage() sync* {
    final box = boxProvider.getGsBox(kBoxPrinter);
    for (var json in box.getValues()) {
      yield SettingLabel.fromJson(json);
    }
  }

  ///
  /// 標籤機列表
  ///
  Future<Iterable<SettingLabel>> getSettingLabels() async {
    final it = await apiProvider.getList(
      unencodedPath: 'setting/labels',
      creator: (json) => SettingLabel.fromJson(json),
    );
    // save to storage
    final box = boxProvider.getGsBox(kBoxPrinter);
    box.erase();
    for (var printer in it) {
      box.write('${printer.uuid}', printer.toJson());
    }
    return it;
  }

  ///
  /// 新增標籤機
  ///
  Future<bool> postSettingLabels(SettingLabel printer) async {
    final json = printer.toJson();
    json.removeNull();
    json['category_ids'] = jsonEncode(printer.categoryIds ?? <num>[]);
    final labelId = await apiProvider.post<num>(
      'setting/labels',
      data: json,
      creator: (data) {
        if (data.containsKey(Keys.IsCreated) && true == data[Keys.IsCreated]) {
          if (data.containsKey('channel_label_id')) {
            return data['channel_label_id'];
          }
        }
        return 0;
      },
    );
    // save to storage
    final box = boxProvider.getGsBox(kBoxPrinter);
    printer.id = labelId;
    box.write('${printer.uuid}', printer.toJson());
    // update cache
    if (labelId is num && labelId > 0) {
      return true;
    }
    return false;
  }

  ///
  /// 更新標籤機
  ///
  Future<bool> putSettingLabels(SettingLabel printer) async {
    final json = printer.toJson();
    json.removeNull();
    json['category_ids'] = jsonEncode(printer.categoryIds ?? <num>[]);
    // upload
    final labelId = await apiProvider.put<num>(
      'setting/labels/${printer.id}',
      data: json,
      creator: (data) {
        if (data.containsKey(Keys.IsUpdated) && true == data[Keys.IsUpdated]) {
          // ['label_id'] = 941;
          // update cache
          if (data.containsKey('label_id')) {
            return data['label_id'];
          }
        }
        return 0;
      },
    );
    // save to storage
    final box = boxProvider.getGsBox(kBoxPrinter);
    box.write('${printer.uuid}', printer.toJson());
    // update cache
    if (labelId is num && labelId > 0) {
      return true;
    }
    return false;
  }

  ///
  /// 刪除標籤機
  ///
  Future<bool> deleteSettingLabels(SettingLabel printer) async {
    // upload
    final labelId = await apiProvider.delete<num>(
      'setting/labels/${printer.id}',
      creator: (data) {
        // ['label_id'] = 940;
        if (data.containsKey(Keys.IsDeleted) && true == data[Keys.IsDeleted]) {
          if (data.containsKey('label_id')) {
            return data['label_id'];
          }
        }
        return 0;
      },
    );
    final box = boxProvider.getGsBox(kBoxPrinter);
    box.remove('${printer.uuid}');
    // update cache
    if (labelId is num && labelId > 0) {
      return true;
    }
    return false;
  }

  ///
  /// 批次新增標籤機
  ///
  Future<bool> postSettingLabelsBatch(Iterable<SettingLabel> printers) async {
    // upload
    final ids = await apiProvider.post(
      'setting/labels/batch',
      data: {
        "labels": jsonEncode(printers.toList()),
      },
      creator: (data) {
        if (data.containsKey(Keys.IsCreated) && true == data[Keys.IsCreated]) {
          // data['channel_label_ids'] = [942];
          if (data.containsKey('channel_label_ids')) {
            return List<num>.from(data['channel_label_ids']);
          }
        }
        return <num>[];
      },
    );
    // update cache
    if (ids.isNotEmpty) {
      final box = boxProvider.getGsBox(kBoxPrinter);
      for (var i = 0; i < min(ids.length, printers.length); i++) {
        final printer = printers.elementAt(i);
        printer.id = ids.elementAt(i);
        box.write('${printer.uuid}', printer.toJson());
      }
      return true;
    }
    return false;
  }

  ///
  /// 批次更新標籤機
  ///
  Future<bool> putSettingLabelsBatch(Iterable<SettingLabel> printers) async {
    // upload
    final ids = await apiProvider.put(
      'setting/labels/batch',
      data: {
        "labels": jsonEncode(printers.toList()),
      },
      creator: (data) {
        if (data.containsKey(Keys.IsUpdated) && true == data[Keys.IsUpdated]) {
          if (data.containsKey('channel_label_ids')) {
            return List<num>.from(data['channel_label_ids']);
          }
        }
        return <num>[];
      },
    );
    // update cache
    if (ids.isNotEmpty) {
      final box = boxProvider.getGsBox(kBoxPrinter);
      for (var i = 0; i < min(ids.length, printers.length); i++) {
        final printer = printers.elementAt(i);
        printer.id = ids.elementAt(i);
        box.write('${printer.uuid}', printer.toJson());
      }
      return true;
    }
    return false;
  }

  ///
  /// 批次新增標籤機(清除原先所有設定)
  ///
  Future<bool> putSettingLabelsBatchWithTruncate(
      Iterable<SettingLabel> printers) async {
    printers.forEach((element) {
      element.categoryIds ??= <num>[];
      // element.id ??= 0;
      // element.ip ??= '';
      element.macAddress ??= '';
      element.name ??= '';
      element.printCount ??= 0;
      element.status ??= 1;
    });
    // upload
    final ids = await apiProvider.post(
      'setting/labels/batch_with_truncate',
      data: {
        "labels": jsonEncode(printers.toList()),
      },
      creator: (data) {
        if (data.containsKey(Keys.IsCreated) && true == data[Keys.IsCreated]) {
          if (data.containsKey('channel_label_ids')) {
            return List<num>.from(data['channel_label_ids']);
          }
        }
        return <num>[];
      },
    );
    if (ids.isNotEmpty) {
      final box = boxProvider.getGsBox(kBoxPrinter);
      box.erase();
      for (var i = 0; i < min(ids.length, printers.length); i++) {
        final printer = printers.elementAt(i);
        printer.id = ids.elementAt(i);
        box.write('${printer.uuid}', printer.toJson());
      }
      return true;
    }
    return false;
  }

  Iterable<SettingLabel> getPrinterWithCategories(
      Iterable<num> categoryIds) sync* {
    final box = boxProvider.getGsBox(kBoxPrinter);
    for (var json in box.getValues()) {
      final printer = SettingLabel.fromJson(json);
      if (printer.status == Switcher.On.index &&
          printer.containsCategories(categoryIds)) {
        yield printer;
      }
    }
  }

  ///
  /// 取得本地雲印表機列表
  ///
  Iterable<SettingLabel> getSettingPrintersFromStorage() sync* {
    final box = boxProvider.getGsBox(kBoxCloudPrinter);
    for (var json in box.getValues()) {
      yield SettingLabel.fromJson(json);
    }
  }

  ///
  /// 雲印表機列表
  ///
  Future<Iterable<SettingLabel>> getSettingPrinters() async {
    final printers = await apiProvider.getList(
      unencodedPath: 'setting/printers',
      creator: (json) => SettingLabel.fromJson(json),
    );
    // save to storage
    final box = boxProvider.getGsBox(kBoxCloudPrinter);
    box.erase();
    for (var printer in printers) {
      box.write('${printer.uuid}', printer.toJson());
    }
    return printers;
  }

  ///
  /// 新增雲印表機
  ///
  Future<bool> postSettingPrinters(SettingLabel printer) async {
    // 強制設定 type 為雲印表機
    printer.prnType = 'cloud';
    printer.macAddress ??= '';
    printer.ip ??= '';
    printer.status ??= Switcher.Off.index;
    printer.printCount ??= 1;
    final json = printer.toJson();
    json.removeNull();
    json['category_ids'] = jsonEncode(printer.categoryIds ?? <num>[]);
    final printerId = await apiProvider.post<num>(
      'setting/printers',
      data: json,
      creator: (data) {
        if (data.containsKey(Keys.IsCreated) && true == data[Keys.IsCreated]) {
          // update cache
          if (data.containsKey('channel_printer_id')) {
            return data['channel_printer_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (printerId is num && printerId > 0) {
      printer.id = printerId;
      final box = boxProvider.getGsBox(kBoxCloudPrinter);
      box.write('${printer.uuid}', printer.toJson());
      return true;
    }
    return false;
  }

  ///
  /// 更新雲印表機
  ///
  Future<bool> putSettingPrinters(SettingLabel printer) async {
    // 強制設定 type 為雲印表機
    printer.prnType = 'cloud';
    printer.macAddress ??= '';
    printer.ip ??= '';
    printer.status ??= Switcher.Off.index;
    printer.printCount ??= 1;
    final json = printer.toJson();
    json.removeNull();
    json['category_ids'] = jsonEncode(printer.categoryIds ?? <num>[]);
    // upload
    final printerId = await apiProvider.put<num>(
      'setting/printers/${printer.id}',
      data: json,
      creator: (data) {
        if (data.containsKey(Keys.IsUpdated) && true == data[Keys.IsUpdated]) {
          // ['print_id'] = 941;
          // update cache
          if (data.containsKey('printer_id')) {
            return data['printer_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (printerId is num && printerId > 0) {
      final box = boxProvider.getGsBox(kBoxCloudPrinter);
      box.write('${printer.uuid}', printer.toJson());
      return true;
    }
    return false;
  }

  ///
  /// 刪除雲印表機
  ///
  Future<bool> deleteSettingPrinters(SettingLabel printer) async {
    final printerId = await apiProvider.delete<num>(
      'setting/printers/${printer.id}',
      creator: (data) {
        // ['label_id'] = 940;
        if (data.containsKey(Keys.IsDeleted) && true == data[Keys.IsDeleted]) {
          if (data.containsKey('printer_id')) {
            return data['printer_id'];
          }
        }
        return 0;
      },
    );
    // update storage
    if (printerId is num && printerId > 0) {
      final box = boxProvider.getGsBox(kBoxCloudPrinter);
      box.remove('${printer.uuid}');
      return true;
    }
    return false;
  }
}

extension _PrinterExtensionX on PrinterExtension {
  // 5秒延遲時間
  static const DELAY_PER_TIMES = 5000;
  // 最大重試次數
  static const MAX_RETRY = 6;

  bool get containsTask {
    tasks ??= <PrinterTask>[];
    return tasks.isNotEmpty;
  }

  void insertAll(Iterable<PrinterTask> value) {
    tasks ??= <PrinterTask>[];
    tasks.insertAll(0, value);
  }

  void addAll(Iterable<PrinterTask> value) {
    tasks ??= <PrinterTask>[];
    tasks.addAll(value);
  }

  void resetDelay() {
    // logger.d('[PrinterExtension] makeAvailable');
    times = 0;
    availableAt = 0;
  }

  void appendDelay() {
    times ??= 0;
    // 最多重試6次(30秒)
    times = min(MAX_RETRY, times + 1);
    // logger.d('[PrinterExtension] makeUnavailable: times($times)');
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    // 設定下次可用時間
    availableAt = timestamp + (times * DELAY_PER_TIMES);
  }

  bool get isAvailable => !isBusy && !isCoolDown;
  bool get isBusy => printing == Switcher.On.index;
  bool get isCoolDown => availableAt > DateTime.now().millisecondsSinceEpoch;
}

extension _PrinterTaskX on PrinterTask {
  static const _threeMinutes = 1000 * 60 * 3;

  bool get expired => DateTime.now().millisecondsSinceEpoch > expiredAt;

  num get expiredAt => createdAt + _threeMinutes;
}
