import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:okshop_model/okshop_model.dart';

class RevenueProvider {
  final ApiProvider apiProvider;
  final cached = <num, RevenuePage>{};

  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  RevenueProvider({
    @required this.apiProvider,
  });

  Future<RevenuePage> getRevenues(final RevenueReq req) async {
    req.page ??= 1;
    req.limit ??= 50;
    // HACK: test limit
    // req.limit = 5;
    try {
      // loading flag
      _isLoading.value = true;
      final ret = await apiProvider.getResData<RevenuePage>(
        unencodedPath: 'reports/revenue/${req.date}',
        filter: req?.toJson(),
        creator: (json) => RevenuePage.fromJson(json),
      );
      if (ret.data == null || ret.data.length < req.limit) {
        // 最後一頁
        ret.pagination.setLastPage();
      }
      final key = req.date.hashCode;
      return _push(key, ret);
    } catch (e) {
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  RevenuePage _push(num key, RevenuePage ret) {
    // update data
    cached[key] ??= RevenuePage();
    cached[key].data ??= <Revenue>[];
    final page = cached[key];
    cached[key] = ret;
    cached[key].data ??= <Revenue>[];
    // update list
    final list = [...page.data, ...ret.data];
    final entries = list.map((e) => MapEntry(e.id, e));
    ret.data = Map.fromEntries(entries).values.toList();
    ret.data.sort((x, y) =>
        (y.orderSerialNumber ?? 0).compareTo(x.orderSerialNumber ?? 0));
    return ret;
  }
}
