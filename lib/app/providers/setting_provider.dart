import 'package:flutter/foundation.dart' show required;
import 'package:muyipork/app/models/brands_invoice.dart';
import 'package:muyipork/app/models/brands_invoice_req.dart';
import 'package:muyipork/app/models/payment_instore.dart';
import 'package:muyipork/app/models/shipping_delivery.dart';
import 'package:muyipork/app/models/shipping_delivery_put.dart';
import 'package:muyipork/app/models/shipping_instore.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:okshop_common/okshop_common.dart';

import 'api_provider.dart';

class SettingProvider {
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  SettingProvider({
    @required this.apiProvider,
  });

  Future<num> putPaymethod(num id, PaymentInstore data) {
    return apiProvider.put<num>(
      'setting/payment/$id',
      data: data.toJ<PERSON>(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('channel_id')) {
            return json['channel_id'];
          }
        }
        return 0;
      },
    );
  }

  Future<PaymentInstore> getPayment(num id) {
    return apiProvider.getData(
      unencodedPath: 'setting/payment/$id',
      creator: (json) => PaymentInstore.fromJson(json),
    );
  }

  ///
  /// 取得零售自取設定
  ///
  Future<ShippingInstore> getShippingInstore() {
    return apiProvider.getData(
      unencodedPath: 'setting/shipping/instore',
      creator: (json) => ShippingInstore.fromJson(json),
    );
  }

  ///
  /// 更新零售自取設定
  ///
  Future<num> putShippingInstore(ShippingInstore data) {
    return apiProvider.put<num>(
      'setting/shipping/instore',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('channel_id')) {
            return json['channel_id'];
          }
        }
        return 0;
      },
    );
  }

  ///
  /// 取得零售宅配設定
  ///
  Future<ShippingDelivery> getShippingDelivery() {
    return apiProvider.getData(
      unencodedPath: 'setting/shipping/delivery',
      creator: (json) => ShippingDelivery.fromJson(json),
    );
  }

  ///
  /// 更新零售宅配設定
  ///
  Future<num> putShippingDelivery(ShippingDeliveryPut data) {
    return apiProvider.put<num>(
      'setting/shipping/delivery',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('channel_id')) {
            return json['channel_id'];
          }
        }
        return 0;
      },
    );
  }

  ///
  /// 取得單一店家電子發票資料
  ///
  Future<BrandsInvoice> getBrandsInvoice() async {
    final value = await apiProvider.getData(
      unencodedPath: 'brands/invoice',
      creator: (json) => BrandsInvoice.fromJson(json),
    );
    // save to local storage
    value.status ??= Switcher.Off.index;
    value.taxType ??= TaxType.TX.index;
    value.guiException ??= Switcher.Off.index;
    prefProvider.brandsInvoice = value;
    return value;
  }

  ///
  /// 更新單一店家電子發票資料
  ///
  Future<num> putBrandsInvoice(BrandsInvoiceReq data) {
    return apiProvider.put(
      'brands/invoice',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('brand_id')) {
            return json['brand_id'];
          }
        }
        return 0;
      },
    );
  }
}
