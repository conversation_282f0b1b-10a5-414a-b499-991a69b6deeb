import 'package:get/get.dart';

import 'package:muyipork/app/modules/account_detail/bindings/account_detail_binding.dart';
import 'package:muyipork/app/modules/account_detail/views/account_detail_view.dart';
import 'package:muyipork/app/modules/accounts/bindings/accounts_binding.dart';
import 'package:muyipork/app/modules/accounts/views/accounts_view.dart';
import 'package:muyipork/app/modules/addition_category_setup/bindings/addition_category_setup_binding.dart';
import 'package:muyipork/app/modules/addition_category_setup/views/addition_category_setup_view.dart';
import 'package:muyipork/app/modules/album/bindings/album_binding.dart';
import 'package:muyipork/app/modules/album/views/album_view.dart';
import 'package:muyipork/app/modules/blank/bindings/blank_binding.dart';
import 'package:muyipork/app/modules/blank/views/blank_view.dart';
import 'package:muyipork/app/modules/brands_banners/bindings/brands_banners_binding.dart';
import 'package:muyipork/app/modules/brands_banners/views/brands_banners_view.dart';
import 'package:muyipork/app/modules/brands_basic/bindings/brands_basic_binding.dart';
import 'package:muyipork/app/modules/brands_basic/views/brands_basic_view.dart';
import 'package:muyipork/app/modules/brands_news/bindings/brands_news_binding.dart';
import 'package:muyipork/app/modules/brands_news/views/brands_news_view.dart';
import 'package:muyipork/app/modules/business_hours_setup/bindings/business_hours_setup_binding.dart';
import 'package:muyipork/app/modules/business_hours_setup/views/business_hours_setup_view.dart';
import 'package:muyipork/app/modules/category_setup/bindings/category_setup_binding.dart';
import 'package:muyipork/app/modules/category_setup/views/category_setup_view.dart';
import 'package:muyipork/app/modules/coupon_detail/bindings/coupon_detail_binding.dart';
import 'package:muyipork/app/modules/coupon_detail/views/coupon_detail_view.dart';
import 'package:muyipork/app/modules/coupons/bindings/coupons_binding.dart';
import 'package:muyipork/app/modules/coupons/views/coupons_view.dart';
import 'package:muyipork/app/modules/ecpay/bindings/ecpay_binding.dart';
import 'package:muyipork/app/modules/ecpay/views/ecpay_view.dart';
import 'package:muyipork/app/modules/godex_printer_category_selection/bindings/godex_printer_category_selection_binding.dart';
import 'package:muyipork/app/modules/godex_printer_category_selection/views/godex_printer_category_selection_view.dart';
import 'package:muyipork/app/modules/godex_printer_setup/bindings/godex_printer_setup_binding.dart';
import 'package:muyipork/app/modules/godex_printer_setup/views/godex_printer_setup_view.dart';
import 'package:muyipork/app/modules/home/<USER>/home_binding.dart';
import 'package:muyipork/app/modules/home/<USER>/home_view.dart';
import 'package:muyipork/app/modules/invoice_settings/bindings/invoice_settings_binding.dart';
import 'package:muyipork/app/modules/invoice_settings/views/invoice_settings_view.dart';
import 'package:muyipork/app/modules/line_at_settings/bindings/line_at_settings_binding.dart';
import 'package:muyipork/app/modules/line_at_settings/views/line_at_settings_view.dart';
import 'package:muyipork/app/modules/login/bindings/login_binding.dart';
import 'package:muyipork/app/modules/login/views/login_view.dart';
import 'package:muyipork/app/modules/member_coupons/bindings/member_coupons_binding.dart';
import 'package:muyipork/app/modules/member_coupons/views/member_coupons_view.dart';
import 'package:muyipork/app/modules/member_detail/bindings/member_detail_binding.dart';
import 'package:muyipork/app/modules/member_detail/views/member_detail_view.dart';
import 'package:muyipork/app/modules/member_memo/bindings/member_memo_binding.dart';
import 'package:muyipork/app/modules/member_memo/views/member_memo_view.dart';
import 'package:muyipork/app/modules/member_profile/bindings/member_profile_binding.dart';
import 'package:muyipork/app/modules/member_profile/views/member_profile_view.dart';
import 'package:muyipork/app/modules/member_report/bindings/member_report_binding.dart';
import 'package:muyipork/app/modules/member_report/views/member_report_view.dart';
import 'package:muyipork/app/modules/members/bindings/members_binding.dart';
import 'package:muyipork/app/modules/members/views/members_view.dart';
import 'package:muyipork/app/modules/multiple_payment/bindings/multiple_payment_binding.dart';
import 'package:muyipork/app/modules/multiple_payment/views/multiple_payment_view.dart';
import 'package:muyipork/app/modules/online_order_settings/bindings/online_order_settings_binding.dart';
import 'package:muyipork/app/modules/online_order_settings/views/online_order_settings_view.dart';
import 'package:muyipork/app/modules/order_detail/bindings/order_detail_binding.dart';
import 'package:muyipork/app/modules/order_detail/views/order_detail_view.dart';
import 'package:muyipork/app/modules/order_editing/bindings/order_editing_binding.dart';
import 'package:muyipork/app/modules/order_editing/views/order_editing_view.dart';
import 'package:muyipork/app/modules/order_filter_picker/bindings/order_filter_picker_binding.dart';
import 'package:muyipork/app/modules/order_filter_picker/views/order_filter_picker_view.dart';
import 'package:muyipork/app/modules/order_list/bindings/order_list_binding.dart';
import 'package:muyipork/app/modules/order_list/views/order_list_view.dart';
import 'package:muyipork/app/modules/order_receipt/bindings/order_receipt_binding.dart';
import 'package:muyipork/app/modules/order_receipt/views/order_receipt_view.dart';
import 'package:muyipork/app/modules/orders/bindings/orders_binding.dart';
import 'package:muyipork/app/modules/orders/views/orders_view.dart';
import 'package:muyipork/app/modules/orders_adjust/bindings/orders_adjust_binding.dart';
import 'package:muyipork/app/modules/orders_adjust/views/orders_adjust_view.dart';
import 'package:muyipork/app/modules/orders_confirm/bindings/orders_confirm_binding.dart';
import 'package:muyipork/app/modules/orders_confirm/views/orders_confirm_view.dart';
import 'package:muyipork/app/modules/orders_picker/bindings/orders_picker_binding.dart';
import 'package:muyipork/app/modules/orders_picker/views/orders_picker_view.dart';
import 'package:muyipork/app/modules/orders_selector/bindings/orders_selector_binding.dart';
import 'package:muyipork/app/modules/orders_selector/views/orders_selector_view.dart';
import 'package:muyipork/app/modules/orders_setup/bindings/orders_setup_binding.dart';
import 'package:muyipork/app/modules/orders_setup/views/orders_setup_view.dart';
import 'package:muyipork/app/modules/orders_sum_up/bindings/orders_sum_up_binding.dart';
import 'package:muyipork/app/modules/orders_sum_up/views/orders_sum_up_view.dart';
import 'package:muyipork/app/modules/partition_setup/bindings/partition_setup_binding.dart';
import 'package:muyipork/app/modules/partition_setup/views/partition_setup_view.dart';
import 'package:muyipork/app/modules/password/bindings/password_binding.dart';
import 'package:muyipork/app/modules/password/views/password_view.dart';
import 'package:muyipork/app/modules/payment_bank/bindings/payment_bank_binding.dart';
import 'package:muyipork/app/modules/payment_bank/views/payment_bank_view.dart';
import 'package:muyipork/app/modules/payment_cod/bindings/payment_cod_binding.dart';
import 'package:muyipork/app/modules/payment_cod/views/payment_cod_view.dart';
import 'package:muyipork/app/modules/payment_instore/bindings/payment_instore_binding.dart';
import 'package:muyipork/app/modules/payment_instore/views/payment_instore_view.dart';
import 'package:muyipork/app/modules/payment_line_pay/bindings/payment_line_pay_binding.dart';
import 'package:muyipork/app/modules/payment_line_pay/views/payment_line_pay_view.dart';
import 'package:muyipork/app/modules/points_history/bindings/points_history_binding.dart';
import 'package:muyipork/app/modules/points_history/views/points_history_view.dart';
import 'package:muyipork/app/modules/printer_detail/bindings/printer_detail_binding.dart';
import 'package:muyipork/app/modules/printer_detail/views/printer_detail_view.dart';
import 'package:muyipork/app/modules/printer_picker/bindings/printer_picker_binding.dart';
import 'package:muyipork/app/modules/printer_picker/views/printer_picker_view.dart';
import 'package:muyipork/app/modules/product_editing/bindings/product_editing_binding.dart';
import 'package:muyipork/app/modules/product_editing/views/product_editing_view.dart';
import 'package:muyipork/app/modules/product_setup/bindings/product_setup_binding.dart';
import 'package:muyipork/app/modules/product_setup/views/product_setup_view.dart';
import 'package:muyipork/app/modules/promotion_picker/bindings/promotion_picker_binding.dart';
import 'package:muyipork/app/modules/promotion_picker/views/promotion_picker_view.dart';
import 'package:muyipork/app/modules/push_settings/bindings/push_settings_binding.dart';
import 'package:muyipork/app/modules/push_settings/views/push_settings_view.dart';
import 'package:muyipork/app/modules/receipt_sticker_printing/bindings/receipt_sticker_printing_binding.dart';
import 'package:muyipork/app/modules/receipt_sticker_printing/views/receipt_sticker_printing_view.dart';
import 'package:muyipork/app/modules/reports_realtime_statements/bindings/reports_realtime_statements_binding.dart';
import 'package:muyipork/app/modules/reports_realtime_statements/views/reports_realtime_statements_view.dart';
import 'package:muyipork/app/modules/reports_sales/bindings/reports_sales_binding.dart';
import 'package:muyipork/app/modules/reports_sales/views/reports_sales_view.dart';
import 'package:muyipork/app/modules/reports_statements/bindings/reports_statements_binding.dart';
import 'package:muyipork/app/modules/reports_statements/views/reports_statements_view.dart';
import 'package:muyipork/app/modules/retail_delivery_settings/bindings/retail_delivery_settings_binding.dart';
import 'package:muyipork/app/modules/retail_delivery_settings/views/retail_delivery_settings_view.dart';
import 'package:muyipork/app/modules/retail_takeout_settings/bindings/retail_takeout_settings_binding.dart';
import 'package:muyipork/app/modules/retail_takeout_settings/views/retail_takeout_settings_view.dart';
import 'package:muyipork/app/modules/revenue/bindings/revenue_binding.dart';
import 'package:muyipork/app/modules/revenue/views/revenue_view.dart';
import 'package:muyipork/app/modules/setting_auto_print/bindings/setting_auto_print_binding.dart';
import 'package:muyipork/app/modules/setting_auto_print/views/setting_auto_print_view.dart';
import 'package:muyipork/app/modules/setting_dinner_online_delivery/bindings/setting_dinner_online_delivery_binding.dart';
import 'package:muyipork/app/modules/setting_dinner_online_delivery/views/setting_dinner_online_delivery_view.dart';
import 'package:muyipork/app/modules/setting_dinner_online_here/bindings/setting_dinner_online_here_binding.dart';
import 'package:muyipork/app/modules/setting_dinner_online_here/views/setting_dinner_online_here_view.dart';
import 'package:muyipork/app/modules/setting_dinner_online_order/bindings/setting_dinner_online_order_binding.dart';
import 'package:muyipork/app/modules/setting_dinner_online_order/views/setting_dinner_online_order_view.dart';
import 'package:muyipork/app/modules/setting_dinner_online_togo/bindings/setting_dinner_online_togo_binding.dart';
import 'package:muyipork/app/modules/setting_dinner_online_togo/views/setting_dinner_online_togo_view.dart';
import 'package:muyipork/app/modules/setting_discount/bindings/setting_discount_binding.dart';
import 'package:muyipork/app/modules/setting_discount/views/setting_discount_view.dart';
import 'package:muyipork/app/modules/setting_dm/bindings/setting_dm_binding.dart';
import 'package:muyipork/app/modules/setting_dm/views/setting_dm_view.dart';
import 'package:muyipork/app/modules/setting_pay/bindings/setting_pay_binding.dart';
import 'package:muyipork/app/modules/setting_pay/views/setting_pay_view.dart';
import 'package:muyipork/app/modules/setting_pay_add/bindings/setting_pay_add_binding.dart';
import 'package:muyipork/app/modules/setting_pay_add/views/setting_pay_add_view.dart';
import 'package:muyipork/app/modules/setting_pay_editor/bindings/setting_pay_editor_binding.dart';
import 'package:muyipork/app/modules/setting_pay_editor/views/setting_pay_editor_view.dart';
import 'package:muyipork/app/modules/setting_points/bindings/setting_points_binding.dart';
import 'package:muyipork/app/modules/setting_points/views/setting_points_view.dart';
import 'package:muyipork/app/modules/setting_printer/bindings/setting_printer_binding.dart';
import 'package:muyipork/app/modules/setting_printer/views/setting_printer_view.dart';
import 'package:muyipork/app/modules/setting_service/bindings/setting_service_binding.dart';
import 'package:muyipork/app/modules/setting_service/views/setting_service_view.dart';
import 'package:muyipork/app/modules/settings/bindings/settings_binding.dart';
import 'package:muyipork/app/modules/settings/views/settings_view.dart';
import 'package:muyipork/app/modules/splash/bindings/splash_binding.dart';
import 'package:muyipork/app/modules/splash/views/splash_view.dart';
import 'package:muyipork/app/modules/table_setup/bindings/table_setup_binding.dart';
import 'package:muyipork/app/modules/table_setup/views/table_setup_view.dart';
import 'package:muyipork/app/modules/tables_selection/bindings/tables_selection_binding.dart';
import 'package:muyipork/app/modules/tables_selection/views/tables_selection_view.dart';

import 'auth_middleware.dart';

part 'app_routes.dart';

class AppPages {
  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: _Paths.SPLASH,
      page: () => SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
      middlewares: [
        AuthMiddleware(),
      ],
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.SETTINGS,
      page: () => SettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: _Paths.ORDERS,
      page: () => OrdersView(),
      binding: OrdersBinding(),
    ),
    GetPage(
      name: _Paths.ACCOUNTS,
      page: () => AccountsView(),
      binding: AccountsBinding(),
    ),
    GetPage(
      name: _Paths.PASSWORD,
      page: () => PasswordView(),
      binding: PasswordBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_DETAIL,
      page: () => OrderDetailView(),
      binding: OrderDetailBinding(),
    ),
    GetPage(
      name: _Paths.ACCOUNT_DETAIL,
      page: () => AccountDetailView(),
      binding: AccountDetailBinding(),
    ),
    GetPage(
      name: _Paths.BLANK,
      page: () => BlankView(),
      binding: BlankBinding(),
    ),
    GetPage(
      name: _Paths.PARTITION_SETUP,
      page: () => PartitionSetupView(),
      binding: PartitionSetupBinding(),
    ),
    GetPage(
      name: _Paths.TABLE_SETUP,
      page: () => TableSetupView(),
      binding: TableSetupBinding(),
    ),
    GetPage(
      name: _Paths.CATEGORY_SETUP,
      page: () => CategorySetupView(),
      binding: CategorySetupBinding(),
    ),
    GetPage(
      name: _Paths.ADDITION_CATEGORY_SETUP,
      page: () => AdditionCategorySetupView(),
      binding: AdditionCategorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PRODUCT_SETUP,
      page: () => ProductSetupView(),
      binding: ProductSetupBinding(),
    ),
    GetPage(
      name: _Paths.PRODUCT_EDITING,
      page: () => ProductEditingView(),
      binding: ProductEditingBinding(),
    ),
    GetPage(
      name: _Paths.GODEX_PRINTER_SETUP,
      page: () => GodexPrinterSetupView(),
      binding: GodexPrinterSetupBinding(),
    ),
    GetPage(
      name: _Paths.REVENUE,
      page: () => RevenueView(),
      binding: RevenueBinding(),
    ),
    GetPage(
      name: _Paths.ORDERS_SETUP,
      page: () => OrdersSetupView(),
      binding: OrdersSetupBinding(),
    ),
    GetPage(
      name: _Paths.ORDERS_ADJUST,
      page: () => OrdersAdjustView(),
      binding: OrdersAdjustBinding(),
    ),
    GetPage(
      name: _Paths.ORDERS_CONFIRM,
      page: () => OrdersConfirmView(),
      binding: OrdersConfirmBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_EDITING,
      page: () => OrderEditingView(),
      binding: OrderEditingBinding(),
    ),
    GetPage(
      name: _Paths.TABLES_SELECTION,
      page: () => TablesSelectionView(),
      binding: TablesSelectionBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_RECEIPT,
      page: () => OrderReceiptView(),
      binding: OrderReceiptBinding(),
    ),
    GetPage(
      name: _Paths.GODEX_PRINTER_CATEGORY_SELECTION,
      page: () => GodexPrinterCategorySelectionView(),
      binding: GodexPrinterCategorySelectionBinding(),
    ),
    GetPage(
      name: _Paths.RECEIPT_STICKER_PRINTING,
      page: () => ReceiptStickerPrintingView(),
      binding: ReceiptStickerPrintingBinding(),
    ),
    GetPage(
      name: _Paths.ORDERS_SUM_UP,
      page: () => OrdersSumUpView(),
      binding: OrdersSumUpBinding(),
    ),
    GetPage(
      name: _Paths.ORDERS_SELECTOR,
      page: () => OrdersSelectorView(),
      binding: OrdersSelectorBinding(),
    ),
    GetPage(
      name: _Paths.LINE_AT_SETTINGS,
      page: () => LineAtSettingsView(),
      binding: LineAtSettingsBinding(),
    ),
    GetPage(
      name: _Paths.MEMBERS,
      page: () => MembersView(),
      binding: MembersBinding(),
    ),
    GetPage(
      name: _Paths.ONLINE_ORDER_SETTINGS,
      page: () => OnlineOrderSettingsView(),
      binding: OnlineOrderSettingsBinding(),
    ),
    GetPage(
      name: _Paths.BUSINESS_HOURS_SETUP,
      page: () => BusinessHoursSetupView(),
      binding: BusinessHoursSetupBinding(),
    ),
    GetPage(
      name: _Paths.ORDERS_PICKER,
      page: () => OrdersPickerView(),
      binding: OrdersPickerBinding(),
    ),
    GetPage(
      name: _Paths.RETAIL_TAKEOUT_SETTINGS,
      page: () => RetailTakeoutSettingsView(),
      binding: RetailTakeoutSettingsBinding(),
    ),
    GetPage(
      name: _Paths.RETAIL_DELIVERY_SETTINGS,
      page: () => RetailDeliverySettingsView(),
      binding: RetailDeliverySettingsBinding(),
    ),
    GetPage(
      name: _Paths.PAYMENT_BANK,
      page: () => PaymentBankView(),
      binding: PaymentBankBinding(),
    ),
    GetPage(
      name: _Paths.PAYMENT_INSTORE,
      page: () => PaymentInstoreView(),
      binding: PaymentInstoreBinding(),
    ),
    GetPage(
      name: _Paths.PAYMENT_COD,
      page: () => PaymentCodView(),
      binding: PaymentCodBinding(),
    ),
    GetPage(
      name: _Paths.INVOICE_SETTINGS,
      page: () => InvoiceSettingsView(),
      binding: InvoiceSettingsBinding(),
    ),
    GetPage(
      name: _Paths.BRANDS_BASIC,
      page: () => BrandsBasicView(),
      binding: BrandsBasicBinding(),
    ),
    GetPage(
      name: _Paths.BRANDS_NEWS,
      page: () => BrandsNewsView(),
      binding: BrandsNewsBinding(),
    ),
    GetPage(
      name: _Paths.ECPAY,
      page: () => EcpayView(),
      binding: EcpayBinding(),
    ),
    GetPage(
      name: _Paths.BRANDS_BANNERS,
      page: () => BrandsBannersView(),
      binding: BrandsBannersBinding(),
    ),
    GetPage(
      name: _Paths.PUSH_SETTINGS,
      page: () => PushSettingsView(),
      binding: PushSettingsBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_PAY,
      page: () => SettingPayView(),
      binding: SettingPayBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_PAY_EDITOR,
      page: () => SettingPayEditorView(),
      binding: SettingPayEditorBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_PAY_ADD,
      page: () => SettingPayAddView(),
      binding: SettingPayAddBinding(),
    ),
    GetPage(
      name: _Paths.MULTIPLE_PAYMENT,
      page: () => MultiplePaymentView(),
      binding: MultiplePaymentBinding(),
    ),
    GetPage(
      name: _Paths.REPORTS_STATEMENTS,
      page: () => ReportsStatementsView(),
      binding: ReportsStatementsBinding(),
    ),
    GetPage(
      name: _Paths.REPORTS_SALES,
      page: () => ReportsSalesView(),
      binding: ReportsSalesBinding(),
    ),
    GetPage(
      name: _Paths.MEMBER_PROFILE,
      page: () => MemberProfileView(),
      binding: MemberProfileBinding(),
    ),
    GetPage(
      name: _Paths.MEMBER_DETAIL,
      page: () => MemberDetailView(),
      binding: MemberDetailBinding(),
    ),
    GetPage(
      name: _Paths.MEMBER_MEMO,
      page: () => MemberMemoView(),
      binding: MemberMemoBinding(),
    ),
    GetPage(
      name: _Paths.POINTS_HISTORY,
      page: () => PointsHistoryView(),
      binding: PointsHistoryBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_POINTS,
      page: () => SettingPointsView(),
      binding: SettingPointsBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_SERVICE,
      page: () => SettingServiceView(),
      binding: SettingServiceBinding(),
    ),
    GetPage(
      name: _Paths.MEMBER_REPORT,
      page: () => MemberReportView(),
      binding: MemberReportBinding(),
    ),
    GetPage(
      name: _Paths.ALBUM,
      page: () => AlbumView(),
      binding: AlbumBinding(),
    ),
    GetPage(
      name: _Paths.COUPON_DETAIL,
      page: () => CouponDetailView(),
      binding: CouponDetailBinding(),
    ),
    GetPage(
      name: _Paths.COUPONS,
      page: () => CouponsView(),
      binding: CouponsBinding(),
    ),
    GetPage(
      name: _Paths.MEMBER_COUPONS,
      page: () => MemberCouponsView(),
      binding: MemberCouponsBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_AUTO_PRINT,
      page: () => SettingAutoPrintView(),
      binding: SettingAutoPrintBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_DM,
      page: () => SettingDmView(),
      binding: SettingDmBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_FILTER_PICKER,
      page: () => OrderFilterPickerView(),
      binding: OrderFilterPickerBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_LIST,
      page: () => OrderListView(),
      binding: OrderListBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_PRINTER,
      page: () => SettingPrinterView(),
      binding: SettingPrinterBinding(),
    ),
    GetPage(
      name: _Paths.PRINTER_PICKER,
      page: () => PrinterPickerView(),
      binding: PrinterPickerBinding(),
    ),
    GetPage(
      name: _Paths.PRINTER_DETAIL,
      page: () => PrinterDetailView(),
      binding: PrinterDetailBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_DISCOUNT,
      page: () => SettingDiscountView(),
      binding: SettingDiscountBinding(),
    ),
    GetPage(
      name: _Paths.PROMOTION_PICKER,
      page: () => PromotionPickerView(),
      binding: PromotionPickerBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_DINNER_ONLINE_ORDER,
      page: () => SettingDinnerOnlineOrderView(),
      binding: SettingDinnerOnlineOrderBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_DINNER_ONLINE_HERE,
      page: () => SettingDinnerOnlineHereView(),
      binding: SettingDinnerOnlineHereBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_DINNER_ONLINE_TOGO,
      page: () => SettingDinnerOnlineTogoView(),
      binding: SettingDinnerOnlineTogoBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_DINNER_ONLINE_DELIVERY,
      page: () => SettingDinnerOnlineDeliveryView(),
      binding: SettingDinnerOnlineDeliveryBinding(),
    ),
    GetPage(
      name: _Paths.PAYMENT_LINE_PAY,
      page: () => PaymentLinePayView(),
      binding: PaymentLinePayBinding(),
    ),
    GetPage(
      name: _Paths.REPORTS_REALTIME_STATEMENTS,
      page: () => ReportsRealtimeStatementsView(),
      binding: ReportsRealtimeStatementsBinding(),
    ),
  ];
}
