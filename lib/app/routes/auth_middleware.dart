import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/pref_provider.dart';

import 'app_pages.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings redirect(String route) {
    if (Routes.LOGIN == route) {
      return super.redirect(route);
    }
    final prefProvider = Get.find<PrefProvider>();
    if (prefProvider.isLogout) {
      return RouteSettings(name: Routes.LOGIN);
    }
    // return null;
    return super.redirect(route);
  }
}
