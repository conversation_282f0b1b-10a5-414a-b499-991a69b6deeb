// FIXME: try remove me.
export 'package:okshop_common/okshop_common.dart';
// import 'package:flutter/material.dart';

// class BaseColor {
//   final Color value;
//   const BaseColor() : this.value = Colors.red;
//   const BaseColor._internal(this.value);
//   static const Tab = const Color(0xff3e4b5a);
//   static const Must = const Color(0xffe00707);
//   static const Error = const Color(0xFFE02020);
//   static const Line = const Color(0xFF00c330);
//   static const Primary = const Color(0xFFF89328);
//   static const PrimaryWeight = const Color(0xFFE66F53);
//   static const Secondary = const Color(0xFFF7AB00);
//   static const Accent = const Color(0xFFED4C00);
//   static const Vip = const Color(0xfffa5700);
//   static const Retail = const Color(0xFFE0A471);
//   static const Shadow = const Color(0x29000000);
//   static const Destructive = Error;
//   static const GrayBF = const Color(0xffbfbfbf);
//   static const GrayDD = const Color(0xffdddddd);
//   static const Gray22 = const Color(0xff222222);
//   static const Gray33 = const Color(0xff333333);
//   static const Gray4D = const Color(0xff4d4d4d);
//   static const Gray66 = const Color(0xff666666);
//   static const GrayB9 = const Color(0xffb9b9b9);
//   static const Gray58 = const Color(0xff585858);
//   static const Gray70 = const Color(0xFF707070);
//   static const GrayF7 = const Color(0xFFF7F7F7);
// }
