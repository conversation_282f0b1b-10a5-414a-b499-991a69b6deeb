import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'extension.dart';

// TODO: remove me
const kColorPrimary = const Color(0xfff89328);
const kColorOtherBusinessHours = const Color(0xFFF7AB00);
const kColorRetailMode = const Color(0xFFE0A471);
const kColorError = const Color(0xFFE02020);
const kColorSecondary = const Color(0xffe66f53);
const kColorBackground = const Color(0xffeeeef3);
const kColorTabBar = const Color(0xff3e4b5a);
// TODO: remove me
const kColorLine = const Color(0xff00c330);

//Get theme color by different mode.
// Color themeColorByModes(
//     StoreType appBusinessMode, OrdersBusinessHoursMode ordersBusinessHoursMode,
//     {OrdersViewMode ordersViewMode = OrdersViewMode.ActiveOrders}) {
//   if (appBusinessMode == StoreType.Dinner) {
//     if (ordersViewMode == OrdersViewMode.ActiveOrders) {
//       if (ordersBusinessHoursMode == OrdersBusinessHoursMode.Current) {
//         return kColorPrimary;
//       } else if (ordersBusinessHoursMode == OrdersBusinessHoursMode.Other) {
//         return kColorOtherBusinessHours;
//       }
//     }
//   } else if (appBusinessMode == StoreType.Retail) {
//     return kColorRetailMode;
//   }

//   return kColorPrimary;
// }

final logger = Logger();
const kSettingsUrl =
    'https://dl.dropboxusercontent.com/s/6mxvt5jfuodd41e/app_settings.json';
const kInsetSize = 8.0;
const kDefaultInsetPadding = const EdgeInsets.symmetric(
  vertical: 24.0,
  horizontal: 40.0,
);
const kPadding = 20.0;
const kPadding20 = kPadding;
const kContentPadding = const EdgeInsets.symmetric(
  horizontal: kPadding,
);
const kChipPadding = const EdgeInsets.symmetric(
  vertical: 4.0,
  horizontal: 8.0,
);
const kInsetPadding = const EdgeInsets.symmetric(
  vertical: 24.0,
  horizontal: 80.0,
);
const kRadius = 30.0;
const kRadiusCircular = const Radius.circular(kRadius);
const kBorderRadius = const BorderRadius.all(kRadiusCircular);
const kBorderRadiusTop = const BorderRadius.vertical(
  top: kRadiusCircular,
);
const kRadius10 = const Radius.circular(10.0);
const kBorderRadius10 = const BorderRadius.all(kRadius10);
const kRadius20 = const Radius.circular(24.0);
const kBorderRadius20 = const BorderRadius.all(kRadius20);
const kIconRadius = 37.0;
const kIconSize = kIconRadius * 2.0;
const kBottomPadding = 50.0;
const kBottomButtonPadding = 80.0;
const kButtonHeight = 48.0;
// const kTaxRateNormal = 0.05; // 應稅 (5%)
// const kTaxRateFree = 0.0; // 免稅 (農產品)
const kTaxTypeNormal = 1; // 應稅
const kTaxTypeZero = 2; // 零稅率
const kTaxTypeFree = 3; // 免稅 (農產品)
const kTaxTypeMix = 9; // 混合稅率
// key
const kKeySettings = 'settings';
const kKeyId = 'id';
const kKeyKey = 'key';
const kKeyValue = 'value';
const kKeyModel = 'model';
const kKeyV2Pro = 'V2_PRO';
const kKeyToken = 'token';
const kKeyTotal = 'total';
const kKeyCode = 'code';
const kKeyUsername = 'username';
const kKeyData = 'data';
const kKeyPage = 'page';
const kKeyKeyword = 'keyword';
const kKeyLimit = 'limit';
const kKeyName = 'name';
const kKeyType = 'type';
const kKeyError = 'error';
const kKeySetting = 'setting';
const kKeyOrders = 'orders';
const kKeyMessage = 'message';
const kKeyPagination = 'pagination';
const kKeyIsCreated = 'is_created';
const kKeyIsUpdated = 'is_updated';
const kKeyBrandsInvoice = 'brands_invoice';
const kKeyInnerPrinter = 'inner_printer';
const kFcmToken = 'fcm_token';
const kKeyBoxOrderInvoice = 'box_order_invoice';
const kMachineNumber = 'machine_number';
//[Dep]: Replaced by new server setting labels API
// const kKeyGodexPrinterSettings = 'godex_printer_settings';
const kKeyOrdersBusinessHoursMode = 'orders_business_hours_mode';
const kKeyStoreType = 'app_business_mode';
const kKeyRememberMe = 'remember_me';
const kOkShopService =
    'https://liff.line.me/**********-kWRPP32q/?accountId=okshoptw';
const kOkShopServiceUrl = 'https://page.line.me/okshoptw';
const kPrintStyle = const TextStyle(
  fontSize: 20,
  color: Colors.black,
  fontWeight: FontWeight.normal,
);
const kLimit = 50;
const kLimitMax = 500;
const kBoxMember = 'box_member';
const kBoxOrder = 'box_order';
const kBoxOrderCache = 'box_order_cache';
const kBoxCategory = 'box_category';
const kBoxAdditionCategory = 'box_addition_category';
// const kBoxProductSingle = 'box_product_single';
const kBoxAdditionProduct = 'box_addition_product';
const kBoxProductInfo = 'box_product_info';
const kBoxPrinter = 'box_printer';
const kBoxCloudPrinter = 'box_cloud_printer';
const kBoxTable = 'box_table';
const kBoxInvoice = 'box_invoice'; // 預取發票

class Constants {
  static const designWidth = 375.0;
  static const designHeight = 667.0;
  static const designWidthRatio = 100.0 / Constants.designWidth;
  static const designHeightRatio = 100.0 / Constants.designHeight;
  static final buttonHeight = 48.dh;
  static const chipPadding = EdgeInsets.symmetric(
    vertical: 4.0,
    horizontal: 8.0,
  );
  static const paddingHorizontal = 20.0;
  static const paddingVertical = 12.0;
  static const paddingSymmetric = EdgeInsets.symmetric(
    horizontal: paddingHorizontal,
  );
}

const boxes = [
  kBoxMember,
  kBoxOrder,
  kBoxOrderCache,
  kBoxCategory,
  kBoxAdditionCategory,
  // kBoxProductSingle,
  kBoxAdditionProduct,
  kBoxProductInfo,
  kBoxPrinter,
  kBoxCloudPrinter,
  kBoxTable,
  kBoxInvoice,
];

const rejectActions = [
  OrderStatus.Padding,
  OrderStatus.CancelByApp,
  OrderStatus.CancelByLine,
];
