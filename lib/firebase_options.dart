// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions getCurrentPlatform(bool isSandbox) {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return isSandbox == true ? androidSandbox : android;
      case TargetPlatform.iOS:
        return isSandbox == true ? iosSandbox : ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAbtw9PFZXxl7CY3XphjhVefvYhyf3bArc',
    appId: '1:44148148864:web:1e2b1cb23a9d1622f9b465',
    messagingSenderId: '44148148864',
    projectId: 'okshop-d2c15',
    authDomain: 'okshop-d2c15.firebaseapp.com',
    storageBucket: 'okshop-d2c15.appspot.com',
    measurementId: 'G-6C58SDLBXJ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDgV__h0JK7z8x7hZxeAJkT_ghtXjZq1yg',
    appId: '1:44148148864:android:cc70f53b0cddcef9f9b465',
    messagingSenderId: '44148148864',
    projectId: 'okshop-d2c15',
    storageBucket: 'okshop-d2c15.appspot.com',
  );

  static const FirebaseOptions androidSandbox = FirebaseOptions(
    apiKey: 'AIzaSyDgV__h0JK7z8x7hZxeAJkT_ghtXjZq1yg',
    appId: '1:44148148864:android:d9085ff6d86927c4f9b465',
    messagingSenderId: '44148148864',
    projectId: 'okshop-d2c15',
    storageBucket: 'okshop-d2c15.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCHkVV4a0w17gEUpSWZgScWzELiQ78GclA',
    appId: '1:44148148864:ios:f97887af7a3ddb3df9b465',
    messagingSenderId: '44148148864',
    projectId: 'okshop-d2c15',
    storageBucket: 'okshop-d2c15.appspot.com',
    androidClientId: '44148148864-h95lo9magknehr3clir63fkcefv6aj6o.apps.googleusercontent.com',
    iosBundleId: 'tw.omos.muyipork',
  );

  static const FirebaseOptions iosSandbox = FirebaseOptions(
    apiKey: 'AIzaSyCHkVV4a0w17gEUpSWZgScWzELiQ78GclA',
    appId: '1:44148148864:ios:f6cef23f71e05bc1f9b465',
    messagingSenderId: '44148148864',
    projectId: 'okshop-d2c15',
    storageBucket: 'okshop-d2c15.appspot.com',
    androidClientId: '44148148864-h95lo9magknehr3clir63fkcefv6aj6o.apps.googleusercontent.com',
    iosBundleId: 'tw.omos.muyipork.dev',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCHkVV4a0w17gEUpSWZgScWzELiQ78GclA',
    appId: '1:44148148864:ios:f1bc5e4859e6fa3bf9b465',
    messagingSenderId: '44148148864',
    projectId: 'okshop-d2c15',
    storageBucket: 'okshop-d2c15.appspot.com',
    androidClientId: '44148148864-h95lo9magknehr3clir63fkcefv6aj6o.apps.googleusercontent.com',
    iosBundleId: 'tw.omos.okshop',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAbtw9PFZXxl7CY3XphjhVefvYhyf3bArc',
    appId: '1:44148148864:web:b67d2c68e5435c16f9b465',
    messagingSenderId: '44148148864',
    projectId: 'okshop-d2c15',
    authDomain: 'okshop-d2c15.firebaseapp.com',
    storageBucket: 'okshop-d2c15.appspot.com',
    measurementId: 'G-MCY3HYDYQM',
  );
}
