import 'package:hive/hive.dart';

import 'constants.dart';

class ReadWriteValue<T> {
  final String key;
  final T defaultValue;
  final Box box;

  ReadWriteValue(
    this.key,
    this.defaultValue,
    this.box,
  );

  Box get _box => this.box ?? Hive.box(kKeySettings);
  T get val => this._box.get(this.key, defaultValue: this.defaultValue);
  set val(T newVal) => this._box.put(this.key, newVal);
}

extension Data<T> on T {
  ReadWriteValue<T> val(
    String valueKey, {
    Box box,
    T defVal,
  }) {
    return ReadWriteValue(valueKey, defVal ?? this, box);
  }
}
