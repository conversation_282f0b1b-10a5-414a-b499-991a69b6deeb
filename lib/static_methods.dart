import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/extension.dart';

import 'app/models/res_base.dart';

//這個檔案專門放共用的 static functions (你找不到地方放，但是大家都會用到的東西)

//用 DialogGeneral 顯示 Response 錯誤訊息
Future<void> handleGeneralResponseError(ResBase resBase) async {
  await Get.showAlert(resBase.formattedErrorStr());
}

//Form a time Str from timeOfDay.
String timeOfDayFormattedStr(TimeOfDay timeOfDay) {
  if (timeOfDay != null) {
    return timeOfDay.hour.toString().padLeft(2, '0') +
        ':' +
        timeOfDay.minute.toString().padLeft(2, '0');
  }
  return '';
}

//The format should be like this: '01:30'.
//Possible return null if parse failed.
TimeOfDay tryParseToTimeOfDay(String timeStr) {
  final regexp = RegExp(r'^\d{2}:\d{2}$');
  if (regexp.hasMatch(timeStr)) {
    final matches = RegExp(r'\d{2}').allMatches(timeStr);
    final hh = matches.elementAt(0).group(0);
    final mm = matches.elementAt(1).group(0);
    return TimeOfDay(
      hour: int.parse(hh),
      minute: int.parse(mm),
    );
  }
  if (timeStr != null) {
    List<String> numbers = timeStr.split(':');
    if (numbers.length != 2) {
      return null;
    }

    int hour = int.tryParse(numbers[0]);
    int minute = int.tryParse(numbers[1]);

    if (hour != null && minute != null) {
      return TimeOfDay(hour: hour, minute: minute);
    }
  }
  return null;
}
