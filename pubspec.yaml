name: muyipork
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.12.24+508

environment:
  sdk: ">=2.7.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.3
  get: ^4.1.4
  package_info: ^2.0.2 # TODO: use package_info_plus instead
  dio: ^4.0.0
  logger: ^1.0.0
  intl: ^0.17.0
  flutter_svg: ^0.22.0
  cached_network_image: ^3.1.0
  hive: ^2.0.4
  hive_flutter: ^1.1.0
  jwt_decoder: ^2.0.1
  tuple: ^2.0.0 # TODO: try to remove me
  device_info: ^2.0.2 # TODO: use device_info_plus instead
  screenshot: ^0.3.0
  # For resize image to fit DT2x printer.
  image: ^3.0.2
  # Try add expandable addition category item.
  expandable: ^5.0.1
  okshop_esc_pos:
    # path: ../okshop_esc_pos
    git:
      url: https://bitbucket.org/umomos/okshop_esc_pos.git
      ref: 9dd914252d4c91dea01a1ab94e2b9dac19f664e5
  flutter_sunmi_printer:
    # path: ../flutter_sunmi_printer
    git:
      url: https://bitbucket.org/umomos/flutter_sunmi_printer.git
      # url: *****************:umomos/flutter_sunmi_printer.git
      ref: 47bf21b3966d5274802b1640c0e33e8d26bfca58
  flutter_godex_printer:
    git:
      url: https://bitbucket.org/umomos/flutter_godex_printer.git
      ref: b38cb137e7c9e7118f5ad303121c02c0ccd057fd
  bpscm:
    # path: ../bpscm
    git:
      url: https://bitbucket.org/umomos/bpscm.git
      ref: 679b076a8bc233ce1d8c6f25fba3f7e045cdd85d
  stream_transform: ^2.0.0
  url_launcher: ^6.0.9
  reorderables: ^0.4.1 # TODO: upgrade version to 0.5.0
  image_picker: ^0.8.3+2
  image_cropper: ^1.4.1
  flutter_star_prnt: ^2.3.4
  qr_code_scanner: ^0.5.2
  firebase_messaging: ^10.0.7
  firebase_analytics: ^9.1.1
  timeago: ^3.1.0
  photo_view: ^0.13.0
  objectbox: ^1.5.0
  objectbox_flutter_libs: any
  path: ^1.8.0
  path_provider: ^2.0.4
  get_storage: ^2.0.3
  sizer: ^2.0.15
  permission_handler:
    git:
      url: https://github.com/umomos/flutter-permission-handler.git
      ref: d4ae21be8728a2c6bc216932101f6d455a2b846f
      path: permission_handler
  flutter_local_notifications:
    git:
      url: https://github.com/umomos/flutter_local_notifications.git
      ref: 460679baf917048e0894d571dae34d97fb724822
      path: flutter_local_notifications

dependency_overrides:
  okshop_common:
    path: ../okshop_common
    # git:
    #     url: https://bitbucket.org/umomos/okshop_common.git
    #     ref: 608972984cfe15332e1516b715fd9bf651fe224a
  okshop_model:
    # path: ../okshop_model/
    git:
        url: https://bitbucket.org/umomos/okshop_model.git
        ref: 587661a5ea71d55419f8767fcdafc543af694ea7

dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.1.2
  mockito: ^5.0.15
  http_mock_adapter: ^0.3.3
  objectbox_generator: any
  flutter_lints: ^1.0.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/models/code_message.json
    - assets/models/order_detail_1.json
    - assets/models/order_detail_2.json
    - assets/models/invoice.json
    - docs/models/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
  # flutter packages pub run flutter_launcher_icons:main
flutter_icons:
  ios: true
  android: false
  image_path_ios: "assets/icons/icon.png"
  image_path_android: "assets/icons/ic_launcher.png"
