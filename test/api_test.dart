import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:muyipork/app/models/brands_info.dart';
import 'package:muyipork/app/models/brands_news.dart';
import 'package:muyipork/app/models/image_model.dart';
import 'package:muyipork/app/models/payment.dart' as OkShop;
import 'package:muyipork/app/models/payment_bank.dart';
import 'package:muyipork/app/models/payment_instore.dart';
import 'package:muyipork/app/models/setting_all.dart';
import 'package:muyipork/app/models/products_productid_menu_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/message_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';

class MockPrefProvider extends Mock implements PrefProvider {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  ApiProvider apiProvider;
  DioAdapter dioAdapter;

  ///
  /// 只會執行一次
  ///
  setUpAll(() {
    log('root: setUpAll');
    final prefProvider = MockPrefProvider();
    when(prefProvider.token).thenReturn('');
    final dio = Dio(BaseOptions());
    apiProvider = ApiProvider(
      dio: dio,
      prefProvider: prefProvider,
      messageProvider: MessageProvider(codeMessage: {}),
    );
    dioAdapter = DioAdapter(dio: dio);
  });

  ///
  /// 每次執行職都重置
  ///
  setUp(() {
    log('root: setUp');
  });

  group('api test', () {
    setUp(() async {
      log('child: setUp');
    });

    setUpAll(() {
      log('child: setUpAll');
    });

    test('response', () async {
      final jsonString =
          await rootBundle.loadString('docs/models/brands_info_res.json');
      final jsonObject = jsonDecode(jsonString);

      dioAdapter.onGet(
        'https:brands/info?',
        (server) => server.reply(
          200,
          jsonObject,
        ),
      );

      try {
        final response = await apiProvider.getBrandsInfo();
        expect(response, isA<BrandsInfo>());
      } on Exception catch (e) {
        fail('$e');
      }
    });

    test('exception', () async {
      dioAdapter.onGet('https:brands/info?', (server) {
        server.throws(
          401,
          DioError(
            response: Response(
              requestOptions: RequestOptions(path: ''),
              data: {
                "data": {
                  'error': {
                    'code': '0100',
                    'message': 'aaa',
                  }
                },
              },
              statusCode: 401,
              statusMessage: 'aaa',
            ),
            requestOptions: RequestOptions(path: ''),
          ),
        );
      });

      try {
        final res = await apiProvider.getBrandsInfo();
        fail('should not pass');
      } catch (e) {
        expect(e, isA<DioError>());
      }
    });
  });

  test('renew', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/renew_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:renew?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getRenew();
      log('$res');
      expect(res.token, jsonObject['token']);
    } catch (e) {
      fail('$e');
    }
  });

  test('products id menu', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/products_id_menu_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:products/558/menu?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getProductsProductIdMenu(558);
      log('$res');
      expect(res, isA<ProductsProductIdMenuGetRes>());
    } catch (e) {
      fail('$e');
    }
  });

  test('setting/payment', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/setting_payment_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:setting/payment?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getPayment();
      log('$res');
      expect(res, isA<OkShop.Payment>());
    } catch (e) {
      fail('$e');
    }
  });

  test('setting/all', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/setting_all_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:setting/all?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getSettingAll();
      log('$res');
      expect(res, isA<SettingAll>());
    } catch (e) {
      fail('$e');
    }
  });

  test('setting/payment/bank', () async {
    final jsonString = await rootBundle
        .loadString('docs/models/setting_payment_bank_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:setting/payment/bank?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getPaymentBank();
      log('$res');
      expect(res, isA<PaymentBank>());
    } catch (e) {
      fail('$e');
    }
  });

  test('setting/payment/instore', () async {
    final jsonString = await rootBundle
        .loadString('docs/models/setting_payment_instore_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:setting/payment/instore?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getPaymentInstore();
      log('$res');
      expect(res, isA<PaymentInstore>());
    } catch (e) {
      fail('$e');
    }
  });

  test('brands/news', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/brands_news_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:brands/news?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getBrandsNews();
      log('$res');
      expect(res, isA<BrandsNews>());
    } catch (e) {
      fail('$e');
    }
  });

  test('products', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/products_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:products?limit=500',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );
  });

  test('images/id', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/image_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:images/50?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getImage(50);
      log('$res');
      expect(res, isA<ImageModel>());
    } catch (e) {
      fail('$e');
    }
  });

  test('images', () async {
    final jsonString =
        await rootBundle.loadString('docs/models/images_res.json');
    final jsonObject = jsonDecode(jsonString);

    dioAdapter.onGet(
      'https:images?',
      (server) => server.reply(
        200,
        jsonObject,
      ),
    );

    try {
      final res = await apiProvider.getImages();
      log('$res');
      expect(res, isA<List<ImageModel>>());
    } catch (e) {
      fail('$e');
    }
  });
}
