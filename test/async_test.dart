import 'package:flutter_test/flutter_test.dart';

main() {
  //
  group('description', () {
    Future<num> getNumber() => Future.delayed(Duration(seconds: 1), () => 123);

    Stream<num> foo() async* {
      for (int i = 0; i < 42; i++) {
        await Future.delayed(const Duration(seconds: 1));
        yield i;
      }
    }

    Iterable<num> foo2() sync* {
      for (int i = 0; i < 3; i++) {
        // await Future.delayed(const Duration(seconds: 1));
        yield i;
      }
    }

    //
    test('test1', () async {
      final f = getNumber();
      expect(await f, 123);
      // expect(await getNumber(), 123);
      // expect(await fetchPost(client), const TypeMatcher<Post>());
    });

    test('test2', () async {
      expect(foo2(), [0, 1, 2]);
    });
  });
}
