import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Collection package replacement tests', () {
    test('listEquals should work correctly for equal lists', () {
      final list1 = [1, 2, 3];
      final list2 = [1, 2, 3];
      
      expect(listEquals(list1, list2), true);
    });

    test('listEquals should work correctly for different lists', () {
      final list1 = [1, 2, 3];
      final list2 = [1, 2, 4];
      
      expect(listEquals(list1, list2), false);
    });

    test('listEquals should work correctly for different length lists', () {
      final list1 = [1, 2, 3];
      final list2 = [1, 2];
      
      expect(listEquals(list1, list2), false);
    });

    test('listEquals should work correctly for null lists', () {
      expect(listEquals(null, null), true);
      expect(listEquals([1, 2], null), false);
      expect(listEquals(null, [1, 2]), false);
    });

    test('listEquals should work correctly for empty lists', () {
      final list1 = <int>[];
      final list2 = <int>[];
      
      expect(listEquals(list1, list2), true);
    });

    test('listEquals should work correctly for product spec comparison', () {
      // 模拟 productSpec1Ids 的比较场景
      final spec1 = [1, 2, 3];
      final spec2 = [1, 2, 3];
      final spec3 = [1, 2, 4];
      
      expect(listEquals(spec1, spec2), true);
      expect(listEquals(spec1, spec3), false);
    });
  });
}
