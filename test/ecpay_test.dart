import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/models/payment_ecpay.dart';

void main() {
  final json = {
    "credit_status": "",
    "description": "",
    "setting": {
      "hash_iv": "v77hoKGq4kWxNNIS",
      "hash_key": "5294y06JbISpM5x9",
      "merchant_id": "2000214"
    }
  };
  test('parse json', () {
    try {
      final model = PaymentEcpay.fromJson(json);
      expect(model.creditStatus, json['credit_status']);
    } catch (e) {
      print(e);
    }
  });
}
