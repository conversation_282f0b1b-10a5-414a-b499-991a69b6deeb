import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart' hide Response;
import 'package:muyipork/app/models/res_base.dart';
import 'package:muyipork/app/providers/message_provider.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  const CODE = '0100';
  const MESSAGE = '為了安全起見，您已超過登入有效時間，請點選確認後，再行登入。';

  group('response error messafge', () {
    MessageProvider messageProvider;
    ResBase resBase;
    Dio dio;

    setUp(() async {
      resBase = ResBase.fromErrorJson({
        'error': {
          'code': CODE,
          'message': 'Invalid authentication',
        }
      });

      final jsonString =
          await rootBundle.loadString('assets/models/code_message.json');
      final jsonObject = jsonDecode(jsonString);
      messageProvider = Get.put(MessageProvider(
        codeMessage: Map.from(jsonObject),
      ));
    });

    // test('has message provider', () {
    //   when(messageProvider[CODE]).thenReturn(MESSAGE);
    //   expect(Get.find<MessageProvider>() != null, true);
    //   expect(Get.find<MessageProvider>()['0100'], MESSAGE);
    // });

    test('has error', () {
      expect(resBase.hasError(), true);
    });

    test('0010', () {
      expect(resBase.error.code, CODE);
      expect(resBase.formattedErrorStr(),
          '[${resBase.error.code}] ${resBase.error.message}\n');
    });
  });
}
