import 'dart:math';

import 'package:flutter_test/flutter_test.dart';
import 'package:okshop_model/okshop_model.dart';

main() {
  group('list', () {
    test('include', () {
      final dest = [1, 2, 3];
      final src = [2, 3];
      expect(dest.any((element) => src.contains(element)), true);
    });
    test('exclude', () {
      final dest = [1, 2, 3];
      final src = [4, 5, 6];
      expect(dest.any((element) => src.contains(element)), false);
    });
    test('nothing0', () {
      final dest = [];
      final src = [];
      expect(dest.any((element) => src.contains(element)), false);
    });
    test('nothing1', () {
      final dest = [];
      final src = [1, 2, 3];
      expect(dest.any((element) => src.contains(element)), false);
    });
    test('nothing2', () {
      final dest = [4, 5, 6];
      final src = [];
      expect(dest.any((element) => src.contains(element)), false);
    });
    test('sublist', () {
      final start = 0;
      final src = [0, 1, 2, 3, 4, 5];
      final count = 4.clamp(start, src.length);
      final dest = src.sublist(start, count);
      src.removeRange(start, count);
      expect(dest, [0, 1, 2, 3]);
      expect(src, [4, 5]);
    });
  });

  group('pointer', () {
    final input = <Member>[];

    setUp(() {
      input.clear();
      input.add(Member(id: 1));
    });

    test('equal list', () {
      final actual = input;
      input.clear();
      expect(actual.isEmpty, true);
    });
    test('diff list', () {
      final actual = [...input];
      input.clear();
      expect(actual.isNotEmpty, true);
    });
    test('modify content 1', () {
      for (var i = 0; i < input.length; i++) {
        input[i].id = 2;
      }
      expect(input[0].id, 2);
    });
    test('modify content 2', () {
      for (var i = 0; i < input.length; i++) {
        final member = input.elementAt(i);
        member.id = 2;
      }
      expect(input[0].id, 2);
    });
    test('modify content 3', () {
      for (var member in input) {
        member.id = 2;
      }
      expect(input[0].id, 2);
    });
    test('modify content 4', () {
      input.forEach((element) {
        element.id = 2;
      });
      expect(input[0].id, 2);
    });
  });
}
