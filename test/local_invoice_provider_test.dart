import 'package:bpscm/bpscm.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:muyipork/app/models/invoice_data.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/local_invoice_provider.dart';
import 'package:muyipork/constants.dart';

import 'local_invoice_provider_test.mocks.dart';

// flutter pub run build_runner build
@GenerateMocks([
  InvoiceProvider,
  BoxProvider
], customMocks: <MockSpec<dynamic>>[
  MockSpec<GetStorage>(
    as: #MockGetStorage,
    fallbackGenerators: {
      #getKeys: mShim,
      #getValues: mShim,
    },
  ),
])

// root function for const function
T mShim<T>(T a, int b) {
  if (a is int) return 1 as T;
  throw 'unknown';
}

void main() {
  group('LocalInvoiceProvider', () {
    // test('getInvoiceNumber should return the correct invoice number', () async {
    //   // Arrange
    //   final mockInvoiceProvider = MockInvoiceProvider();
    //   final mockBoxProvider = MockBoxProvider();
    //   final invoiceProvider = LocalInvoiceProvider(
    //     invoiceProvider: mockInvoiceProvider,
    //     boxProvider: mockBoxProvider,
    //   );
    //   final expectedInvoiceNumber = 'INV-001';
    //   when(mockInvoiceProvider.getInvoiceNumber())
    //       .thenAnswer((_) async => expectedInvoiceNumber);

    //   final mockGetStorage = MockGetStorage();
    //   when(mockBoxProvider.getGsBox(any)).thenReturn(mockGetStorage);
    //   when(mockGetStorage.getKeys()(any)).thenReturn([]);

    //   // Act
    //   final actualInvoiceNumber = await invoiceProvider.getInvoiceNumber();

    //   // Assert
    //   expect(actualInvoiceNumber, equals(expectedInvoiceNumber));
    // });

    test('test pop invoice data', () {
      // Arrange
      final invoiceData = InvoiceData(
        seller: 'seller',
        date: DateTime(2024, 1, 1).millisecondsSinceEpoch,
        number: 'INV-001',
      );

      final mockBoxProvider = MockBoxProvider();
      final mockGetStorage = MockGetStorage();
      when(mockGetStorage.getKeys()).thenReturn([invoiceData.number]);
      when(mockGetStorage.remove(invoiceData.number)).thenAnswer((_) async {});
      when(mockGetStorage.read(invoiceData.number)).thenReturn(invoiceData.toJson());
      when(mockBoxProvider.getGsBox(kBoxInvoice)).thenReturn(mockGetStorage);
      final invoiceProvider = LocalInvoiceProvider(
        invoiceProvider: MockInvoiceProvider(),
        boxProvider: mockBoxProvider,
      );

      // Act
      final actualInvoiceData = invoiceProvider.popInvoiceData();

      // Assert
      expect(actualInvoiceData.number, invoiceData.number);
    });

    test('test push invoice data', () {
      // Arrange
      final mockBoxProvider = MockBoxProvider();
      final invoiceProvider = LocalInvoiceProvider(
        invoiceProvider: MockInvoiceProvider(),
        boxProvider: mockBoxProvider,
      );
      final invoiceData = InvoiceData(
        seller: 'seller',
        date: DateTime(2024, 1, 1).millisecondsSinceEpoch,
        number: 'INV-001',
      );
      final mockGetStorage = MockGetStorage();
      when(mockBoxProvider.getGsBox(any)).thenReturn(mockGetStorage);
      when(mockGetStorage.write(any, any)).thenAnswer((_) async {});

      // Act
      invoiceProvider.pushInvoiceData(invoiceData);

      // Assert
      verify(mockGetStorage.write(any, any)).called(1);
    });
  });
}
