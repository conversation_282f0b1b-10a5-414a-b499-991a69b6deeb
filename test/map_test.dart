import 'package:flutter_test/flutter_test.dart';
import 'package:okshop_model/okshop_model.dart';

void main() {
  group('pointer test', () {
    final member = Member();
    final input = <num, Member>{};

    setUp(() {
      member.id = 1;
      input.clear();
      input.addEntries([MapEntry(member.id, member)]);
    });

    test('modify content', () {
      final element = input[member.id];
      element.id = 2;
      expect(member.id, element.id);
    });
  });
}
