import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/extension.dart';

const jsonString = '''
{
  "comment": "",
  "created_at": "2021-08-23T06:18:24",
  "id": 2602,
  "invoice_info": {
    "address": "",
    "carrier_id": "",
    "city_id": "",
    "cityarea_id": "",
    "company": "",
    "name": "",
    "vat_number": ""
  },
  "invoice_type": 1,
  "member_name": "",
  "member_status": 1,
  "order_addresses": [{
    "address": "",
    "city": "",
    "city_id": 1,
    "cityarea": "",
    "cityarea_id": 1,
    "name": "現場消費者",
    "phone": "0",
    "type": 0
  }],
  "order_diner": {
    "adult": 0,
    "cancel_reason": "",
    "checkout_at": "2021-08-23T06:30:00",
    "child": 0,
    "id": 3439,
    "is_print": 0,
    "meal_at": "2021-08-23T06:30:00",
    "memo": "",
    "source": 0,
    "table1_id": 217,
    "table1_name": "ggg",
    "table2_id": 0,
    "table2_name": ""
  },
  "order_discount": [{
    "discount_name": "現場折價",
    "discount_price": -50,
    "type": 3
  }],
  "order_invoice": {
    "carrier_id": "/8LR6-P2",
    "carrier_type": 3,
    "invoice_paper": true,
    "npo_ban": "987",
    "number": "GG12345678",
    "random_number": "1234",
    "status": 0,
    "vat_number": "12345678"
  },
  "order_items": [{
    "final_price": 90,
    "id": 4156,
    "product_category_ids": [71],
    "product_id": 47,
    "product_name": "日式炸雞塊",
    "product_spec_1": "",
    "product_spec_1_ids": [1, 2],
    "product_tax_type": 1,
    "quantity": 1,
    "type": 0
  }],
  "order_number": "L20210823000043",
  "order_payment": {
    "info": {
      "change": 0,
      "paid": 90,
      "payment_fee": 200
    },
    "name": "現金",
    "pay_method_id": 1,
    "payment_method_id": 1,
    "total": 90
  },
  "order_shipping": {
    "shipping_fee": 300,
    "shipping_id": 23,
    "shipping_method_id": 23,
    "shipping_name": "常溫"
  },
  "payment_status": 2,
  "status": 2,
  "subtotal": 90,
  "total": 90,
  "type": 0
}
''';

main() {
  final orderDetail = OrderDetail.fromJson(jsonDecode(jsonString));
  final recipient = orderDetail.buyerAddress;
  group('購買人', () {
    test('地址', () {
      expect(recipient.address, '');
    });
  });
  group('收件人', () {
    test('物件', () {
      expect(orderDetail.receiverAddress, null);
    });
  });
}
