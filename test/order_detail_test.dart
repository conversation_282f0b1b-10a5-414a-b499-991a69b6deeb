import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/app/models/order_root.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    logger.d('[main] setUpAll: main scope 只執行一次');
  });

  setUp(() {
    logger.d('[main] setUp: scope 及 子scope 每一次 test 都會執行一次');
  });

  group('order root', () {
    setUpAll(() async {
      logger.d('[OrderRoot] setUpAll');
    });

    OrderRoot data;
    setUp(() async {
      logger.d('[OrderRoot] setUp');
      const jsonFile = 'assets/models/order_detail_2.json';
      // const jsonFile = 'assets/models/orders_combined.json';
      final jsonString = await rootBundle.loadString(jsonFile);
      data = OrderRoot.fromRawJson(jsonString);
    });

    test('normal items count', () {
      expect(data.normalItemsCount, 3);
    });

    test('normal items price', () {
      expect(data.normalItemsPrice, data.data.subtotal);
    });

    test('order id', () {
      expect(data.data.id, 4779);
    });

    test('sub order count', () {
      expect(data.subOrder.length, 2);
    });

    test('item count', () {
      expect(data.allOrderItems.length, 5);
    });

    test('sub order 1 - item count', () {
      final sub = data.subOrder.elementAt(0);
      expect(sub.orderItems.length, 3);
    });

    test('sub order 2 - item count', () {
      final sub = data.subOrder.elementAt(1);
      expect(sub.orderItems.length, 2);
    });

    test('sub order 1 - total', () {
      final sub = data.subOrder.elementAt(0);
      expect(sub.total, 594);
    });

    test('sub order 2 - total', () {
      final sub = data.subOrder.elementAt(1);
      expect(sub.total, 132);
    });
  });

  group('order detail', () {
    OrderDetail orderDetail;

    setUpAll(() async {
      logger.d('[OrderDetail] setUpAll');
    });

    setUp(() async {
      logger.d('[OrderDetail] setUp');
      const jsonFile = 'assets/models/order_detail_1.json';
      final jsonString = await rootBundle.loadString(jsonFile);
      orderDetail = OrderDetail.fromJson(jsonDecode(jsonString));
    });

    test('normal items count', () {
      expect(orderDetail.normalItemsCount, 1);
    });

    test('normal items price', () {
      expect(orderDetail.normalItemsPrice, orderDetail.subtotal);
    });

    test('order serial', () {
      expect(orderDetail.orderSerial, '000043');
    });
    test('order serial number', () {
      expect(orderDetail.orderSerialNumber, 43);
    });
    test('order type', () {
      expect(orderDetail.displayType, '內用');
    });
    test('order type order', () {
      expect(orderDetail.displayTypeColor, OrderTypeColor.DinnerHere);
    });
    test('服務費', () {
      expect(orderDetail.serviceCharges, 14);
    });
    test('額外費用', () {
      expect(orderDetail.additionalCharges, 27);
    });
  });
}
