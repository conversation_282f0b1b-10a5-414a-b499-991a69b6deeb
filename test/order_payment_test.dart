import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/extension.dart';

final L2021022000015 = '''
{
  "data": {
    "comment": "",
    "created_at": "2021-10-22T02:29:08",
    "id": 3975,
    "invoice_info": {
      "address": "",
      "carrier_id": "",
      "city_id": "",
      "cityarea_id": "",
      "company": "",
      "name": "",
      "vat_number": ""
    },
    "invoice_type": null,
    "member_id": null,
    "member_name": null,
    "member_status": null,
    "order_addresses": [{
      "address": null,
      "city": null,
      "city_id": null,
      "cityarea": null,
      "cityarea_id": null,
      "name": null,
      "phone": null,
      "type": 0
    }],
    "order_diner": {
      "adult": 0,
      "cancel_reason": null,
      "checkout_at": "2021-10-22T02:29:08",
      "child": 0,
      "id": 5453,
      "is_print": 1,
      "meal_at": "2021-10-22T02:29:08",
      "memo": null,
      "source": 0,
      "table1_id": null,
      "table1_name": null,
      "table2_id": null,
      "table2_name": null
    },
    "order_discount": [],
    "order_invoice": {
      "carrier_id": "",
      "carrier_type": null,
      "invoice_paper": true,
      "npo_ban": "",
      "number": "RQ00000422",
      "random_number": "1333",
      "status": 0,
      "vat_number": ""
    },
    "order_items": [{
      "final_price": 51,
      "id": 6093,
      "product_category_ids": [],
      "product_id": 142,
      "product_name": "服務費",
      "product_spec_1": null,
      "product_spec_1_ids": null,
      "product_tax_type": 1,
      "quantity": null,
      "type": 5
    }],
    "order_number": "L20211022000015",
    "order_payment": {
      "info": {
        "change": 0,
        "other": "",
        "paid": 561,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 506
    },
    "order_payments": [{
      "info": {
        "change": 0,
        "other": "",
        "paid": 561,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 506
    }, {
      "info": {
        "change": 0,
        "other": "3333",
        "paid": 55
      },
      "name": "信用卡",
      "pay_method_id": 2,
      "payment_method_id": 74,
      "total": 55
    }],
    "order_shipping": null,
    "payment_status": 2,
    "status": 2,
    "subtotal": 510,
    "total": 561,
    "type": 0
  },
  "sub_order": [{
    "comment": "",
    "created_at": "2021-10-22T02:01:05",
    "id": 3964,
    "invoice_info": {
      "address": "",
      "carrier_id": "",
      "city_id": "",
      "cityarea_id": "",
      "company": "",
      "name": "",
      "vat_number": ""
    },
    "invoice_type": null,
    "member_id": null,
    "member_name": null,
    "member_status": null,
    "order_addresses": [{
      "address": null,
      "city": null,
      "city_id": null,
      "cityarea": null,
      "cityarea_id": null,
      "name": "現場消費者",
      "phone": "-",
      "type": 0
    }],
    "order_diner": {
      "adult": 0,
      "cancel_reason": null,
      "checkout_at": "2021-10-22T02:29:08",
      "child": 0,
      "id": 5442,
      "is_print": 0,
      "meal_at": "2021-10-22T02:15:00",
      "memo": "",
      "source": 0,
      "table1_id": 220,
      "table1_name": "戶外",
      "table2_id": 229,
      "table2_name": "-外A"
    },
    "order_discount": [],
    "order_invoice": null,
    "order_items": [{
      "final_price": 110,
      "id": 6080,
      "product_category_ids": [141],
      "product_id": 154,
      "product_name": "鮭魚生魚片",
      "product_spec_1": "不要蔥",
      "product_spec_1_ids": [242],
      "product_tax_type": 2,
      "quantity": 1,
      "type": 0
    }],
    "order_number": "L20211022000004",
    "order_payment": {
      "info": {
        "change": 0,
        "paid": 0,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 110
    },
    "order_payments": [{
      "info": {
        "change": 0,
        "paid": 0,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 110
    }],
    "order_shipping": null,
    "payment_status": 2,
    "status": 2,
    "subtotal": 110,
    "total": 110,
    "type": 0
  }, {
    "comment": "",
    "created_at": "2021-10-22T02:21:37",
    "id": 3973,
    "invoice_info": {
      "address": "",
      "carrier_id": "",
      "city_id": "",
      "cityarea_id": "",
      "company": "",
      "name": "",
      "vat_number": ""
    },
    "invoice_type": null,
    "member_id": null,
    "member_name": null,
    "member_status": null,
    "order_addresses": [{
      "address": null,
      "city": null,
      "city_id": null,
      "cityarea": null,
      "cityarea_id": null,
      "name": "現場消費者",
      "phone": "-",
      "type": 0
    }],
    "order_diner": {
      "adult": 0,
      "cancel_reason": null,
      "checkout_at": "2021-10-22T02:29:08",
      "child": 0,
      "id": 5451,
      "is_print": 0,
      "meal_at": "2021-10-22T02:30:00",
      "memo": "",
      "source": 0,
      "table1_id": 220,
      "table1_name": "戶外",
      "table2_id": 229,
      "table2_name": "-外A"
    },
    "order_discount": [],
    "order_invoice": null,
    "order_items": [{
      "final_price": 400,
      "id": 6090,
      "product_category_ids": [141],
      "product_id": 153,
      "product_name": "生魚片拼盤",
      "product_spec_1": "不要洋蔥",
      "product_spec_1_ids": [243],
      "product_tax_type": 1,
      "quantity": 1,
      "type": 0
    }],
    "order_number": "L20211022000013",
    "order_payment": {
      "info": {
        "change": 0,
        "paid": 0,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 400
    },
    "order_payments": [{
      "info": {
        "change": 0,
        "paid": 0,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 400
    }],
    "order_shipping": null,
    "payment_status": 2,
    "status": 2,
    "subtotal": 400,
    "total": 400,
    "type": 0
  }]
}
''';

const L2021022000016 = '''
{
  "data": {
    "comment": "",
    "created_at": "2021-10-22T02:32:30",
    "id": 3976,
    "invoice_info": {
      "address": "",
      "carrier_id": "",
      "city_id": "",
      "cityarea_id": "",
      "company": "",
      "name": "",
      "vat_number": ""
    },
    "invoice_type": null,
    "member_id": null,
    "member_name": null,
    "member_status": null,
    "order_addresses": [{
      "address": null,
      "city": null,
      "city_id": null,
      "cityarea": null,
      "cityarea_id": null,
      "name": null,
      "phone": null,
      "type": 0
    }],
    "order_diner": {
      "adult": 0,
      "cancel_reason": null,
      "checkout_at": "2021-10-22T02:32:30",
      "child": 0,
      "id": 5454,
      "is_print": 0,
      "meal_at": "2021-10-22T02:32:30",
      "memo": null,
      "source": 0,
      "table1_id": null,
      "table1_name": null,
      "table2_id": null,
      "table2_name": null
    },
    "order_discount": [],
    "order_invoice": {
      "carrier_id": "",
      "carrier_type": null,
      "invoice_paper": true,
      "npo_ban": "",
      "number": "RQ00000423",
      "random_number": "3475",
      "status": 0,
      "vat_number": ""
    },
    "order_items": [{
      "final_price": 20,
      "id": 6094,
      "product_category_ids": [],
      "product_id": 142,
      "product_name": "服務費",
      "product_spec_1": null,
      "product_spec_1_ids": null,
      "product_tax_type": 1,
      "quantity": null,
      "type": 5
    }],
    "order_number": "L20211022000016",
    "order_payment": {
      "info": {
        "change": 180,
        "other": "",
        "paid": 400,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 300
    },
    "order_payments": [{
      "info": {
        "change": 180,
        "other": "",
        "paid": 400,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 300
    }, {
      "info": {
        "change": 0,
        "other": "3333",
        "paid": 100
      },
      "name": "信用卡",
      "pay_method_id": 2,
      "payment_method_id": 74,
      "total": 100
    }],
    "order_shipping": null,
    "payment_status": 2,
    "status": 2,
    "subtotal": 200,
    "total": 220,
    "type": 0
  },
  "sub_order": [{
    "comment": "",
    "created_at": "2021-10-22T02:09:56",
    "id": 3967,
    "invoice_info": {
      "address": "",
      "carrier_id": "",
      "city_id": "",
      "cityarea_id": "",
      "company": "",
      "name": "",
      "vat_number": ""
    },
    "invoice_type": null,
    "member_id": null,
    "member_name": null,
    "member_status": null,
    "order_addresses": [{
      "address": null,
      "city": null,
      "city_id": null,
      "cityarea": null,
      "cityarea_id": null,
      "name": "現場消費者",
      "phone": "-",
      "type": 0
    }],
    "order_diner": {
      "adult": 0,
      "cancel_reason": null,
      "checkout_at": "2021-10-22T02:32:30",
      "child": 0,
      "id": 5445,
      "is_print": 0,
      "meal_at": "2021-10-22T02:15:00",
      "memo": "",
      "source": 0,
      "table1_id": 218,
      "table1_name": "榻榻米座",
      "table2_id": 221,
      "table2_name": "A01"
    },
    "order_discount": [],
    "order_invoice": null,
    "order_items": [{
      "final_price": 160,
      "id": 6084,
      "product_category_ids": [140],
      "product_id": 152,
      "product_name": "牛肉串燒",
      "product_spec_1": "不要洋蔥",
      "product_spec_1_ids": [243],
      "product_tax_type": 1,
      "quantity": 1,
      "type": 0
    }],
    "order_number": "L20211022000007",
    "order_payment": {
      "info": {
        "change": 0,
        "paid": 0,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 160
    },
    "order_payments": [{
      "info": {
        "change": 0,
        "paid": 0,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 160
    }],
    "order_shipping": null,
    "payment_status": 2,
    "status": 2,
    "subtotal": 160,
    "total": 160,
    "type": 0
  }, {
    "comment": "",
    "created_at": "2021-10-22T02:21:18",
    "id": 3972,
    "invoice_info": {
      "address": "",
      "carrier_id": "",
      "city_id": "",
      "cityarea_id": "",
      "company": "",
      "name": "",
      "vat_number": ""
    },
    "invoice_type": null,
    "member_id": null,
    "member_name": null,
    "member_status": null,
    "order_addresses": [{
      "address": null,
      "city": null,
      "city_id": null,
      "cityarea": null,
      "cityarea_id": null,
      "name": "現場消費者",
      "phone": "-",
      "type": 0
    }],
    "order_diner": {
      "adult": 0,
      "cancel_reason": null,
      "checkout_at": "2021-10-22T02:32:30",
      "child": 0,
      "id": 5450,
      "is_print": 0,
      "meal_at": "2021-10-22T02:30:00",
      "memo": "",
      "source": 0,
      "table1_id": 218,
      "table1_name": "榻榻米座",
      "table2_id": 221,
      "table2_name": "A01"
    },
    "order_discount": [],
    "order_invoice": null,
    "order_items": [{
      "final_price": 40,
      "id": 6089,
      "product_category_ids": [142],
      "product_id": 157,
      "product_name": "烏龍茶",
      "product_spec_1": "熱飲",
      "product_spec_1_ids": [254],
      "product_tax_type": 1,
      "quantity": 1,
      "type": 0
    }],
    "order_number": "L20211022000012",
    "order_payment": {
      "info": {
        "change": 0,
        "paid": 0,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 40
    },
    "order_payments": [{
      "info": {
        "change": 0,
        "paid": 0,
        "payment_fee": null
      },
      "name": "現金",
      "pay_method_id": 1,
      "payment_method_id": 73,
      "total": 40
    }],
    "order_shipping": null,
    "payment_status": 2,
    "status": 2,
    "subtotal": 40,
    "total": 40,
    "type": 0
  }]
}
''';

main() {
  group('order payment', () {
    test('order serial', () {
      final jsonObject = jsonDecode(L2021022000016);
      final detail = OrderDetail.fromJson(jsonObject['data']);
      expect(detail.orderSerial, '000016');
    });
  });
}
