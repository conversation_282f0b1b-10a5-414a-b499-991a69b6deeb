import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/models/order_detail.dart';
import 'package:muyipork/app/models/orders_post_req.dart';
import 'package:muyipork/app/models/order_root.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

const jsonString = '''
{
  "comment": "第一次來拜訪",
  "created_at": "2021-04-24T15:32:50",
  "id": 430,
  "invoice_info": {
    "address": "",
    "carrier_id": "",
    "city_id": "",
    "cityarea_id": "",
    "company": "",
    "name": "",
    "vat_number": ""
  },
  "invoice_type": null,
  "member_name": "林姓消費者",
  "member_status": 1,
  "order_addresses": [{
    "address": "222",
    "city": "高雄市",
    "city_id": 1,
    "cityarea": "新興區",
    "cityarea_id": 1,
    "name": "yuming",
    "phone": "111",
    "postcode": "",
    "type": 1
  }],
  "order_diner": {
    "adult": 1,
    "cancel_reason": "",
    "checkout_at": "2020-01-01T13:20:29",
    "child": 1,
    "id": 93,
    "meal_at": "2020-01-01T13:20:29",
    "memo": "",
    "source": 0,
    "table1_id": 1,
    "table1_name": "",
    "table2_id": 1,
    "table2_name": ""
  },
  "order_discount": [{
    "discount_name": "現場折價",
    "discount_price": -50,
    "type": 3
  }],
  "order_invoice": {
    "carrier_id": "/8LR6-P2",
    "carrier_type": 3,
    "invoice_paper": true,
    "npo_ban": "987",
    "number": "GG12345678",
    "random_number": "1234",
    "status": 0,
    "vat_number": "12345678"
  },
  "order_items": [{
    "final_price": 490,
    "id": 70,
    "product_category_ids": [71],
    "product_id": 1,
    "product_name": "厚切腰內豬排",
    "product_spec_1": "[\"香草優格+100\", \"香草優格+0\"]",
    "product_spec_1_ids": [1, 2],
    "product_tax_type": 1,
    "quantity": 3,
    "type": 0
  }, {
    "final_price": 390,
    "id": 71,
    "product_category_ids": [71],
    "product_id": 1,
    "product_name": "厚切腰內豬排",
    "product_spec_1": "香草優格+100, 草莓優格-100",
    "product_spec_1_ids": [1, 2],
    "product_tax_type": 1,
    "quantity": 15,
    "type": 0
  }, {
    "final_price": 700,
    "id": 346,
    "product_category_ids": [71],
    "product_id": 15,
    "product_name": "厚切腰內豬排",
    "product_spec_1": "半碗飯、不要太多肉",
    "product_spec_1_ids": [1, 2],
    "product_tax_type": 1,
    "quantity": 1,
    "type": 0
  }, {
    "final_price": 380,
    "id": 347,
    "product_category_ids": [71],
    "product_id": 16,
    "product_name": "西班牙松阪豬",
    "product_spec_1": "",
    "product_spec_1_ids": [1, 2],
    "product_tax_type": 1,
    "quantity": 2,
    "type": 0
  }, {
    "final_price": 0,
    "id": 348,
    "product_category_ids": [71],
    "product_id": 7,
    "product_name": "額外費用",
    "product_spec_1": null,
    "product_spec_1_ids": [1, 2],
    "product_tax_type": 1,
    "quantity": null,
    "type": 0
  }, {
    "final_price": 0,
    "id": 349,
    "product_category_ids": [71],
    "product_id": 9,
    "product_name": "服務費",
    "product_spec_1": "",
    "product_spec_1_ids": [1, 2],
    "product_tax_type": 1,
    "quantity": null,
    "type": 0
  }],
  "order_number": "L20210425000001",
  "order_payment": {
    "info": {
      "paid": 300,
      "change": 30,
      "payment_fee": 200
    },
    "name": "現金",
    "pay_method_id": 1,
    "payment_method_id": 1,
    "total": 12
  },
  "order_shipping": {
    "shipping_fee": 300,
    "shipping_id": 23,
    "shipping_method_id": 23,
    "shipping_name": "常溫"
  },
  "payment_status": 2,
  "status": 0,
  "subtotal": 2980,
  "total": 12,
  "type": 0,
  "sub_order": []
}
''';

main() {
  group('order test', () {
    final orderDetail = OrderDetail.fromJson(jsonDecode(jsonString));
    final res = OrderRoot();
    res.data = orderDetail;
    final OrdersPostReq req = res.asOrdersPutReq();

    test('every item', () {
      req.recalculate();
      final other = orderDetail.orderShipping.shippingFee +
          orderDetail.orderPayment.info.paymentFee +
          orderDetail.getDiscountPrice(DiscountType.Discount);

      expect(req.total, other + 380 * 2 + 700 + 390 * 15 + 490 * 3);
    });
  });
}
