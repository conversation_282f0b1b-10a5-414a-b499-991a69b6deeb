// Mocks generated by <PERSON><PERSON><PERSON> 5.0.15 from annotations
// in muyipork/test/pref_provider_test.dart.
// Do not manually edit this file.

import 'package:get_storage/get_storage.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:muyipork/app/providers/box_provider.dart' as _i2;
import 'package:package_info/package_info.dart' as _i3;

// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis

/// A class which mocks [BoxProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockBoxProvider extends _i1.Mock implements _i2.BoxProvider {
  MockBoxProvider() {
    _i1.throwOnMissingStub(this);
  }
}

/// A class which mocks [PackageInfo].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockPackageInfo extends _i1.Mock implements _i3.PackageInfo {
  MockPackageInfo() {
    _i1.throwOnMissingStub(this);
  }
}

/// A class which mocks [GetStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetStorage extends _i1.Mock implements _i4.GetStorage {
  MockGetStorage() {
    _i1.throwOnMissingStub(this);
  }
}
