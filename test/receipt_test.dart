import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/models/order_root.dart';
import 'package:muyipork/extension.dart';

const jsonString = '''
{
  "data": {
    "comment": null,
    "created_at": "2021-09-02T08:36:04",
    "id": 2989,
    "invoice_info": {
      "address": "",
      "carrier_id": "",
      "city_id": "",
      "cityarea_id": "",
      "company": "",
      "name": "",
      "vat_number": ""
    },
    "invoice_type": 1,
    "member_name": "小蘇",
    "member_status": 1,
    "order_addresses": [{
      "address": "一路",
      "city": "臺北市",
      "city_id": 2,
      "cityarea": "大同區",
      "cityarea_id": 9,
      "name": "小蘇",
      "phone": "0911888555",
      "type": 1
    }, {
      "address": "一路",
      "city": "臺北市",
      "city_id": 2,
      "cityarea": "大同區",
      "cityarea_id": 9,
      "name": "小蘇",
      "phone": "0911888555",
      "type": 0
    }],
    "order_diner": {
      "adult": 0,
      "cancel_reason": null,
      "checkout_at": null,
      "child": 0,
      "id": 4072,
      "is_print": 1,
      "meal_at": "2021-09-01T16:00:00",
      "memo": "測試運費會不會變成服務費",
      "source": 1,
      "table1_id": null,
      "table1_name": null,
      "table2_id": null,
      "table2_name": null
    },
    "order_discount": [],
    "order_invoice": null,
    "order_items": [{
      "final_price": 129,
      "id": 4770,
      "product_category_ids": [242, 248],
      "product_id": 422,
      "product_name": "很難安撫奶嘴",
      "product_spec_1": "",
      "product_spec_1_ids": [],
      "product_tax_type": 1,
      "quantity": 1,
      "type": 0
    }],
    "order_number": "L20210902000036",
    "order_payment": {
      "info": {
        "payment_fee": 240
      },
      "name": "貨到付款",
      "pay_method_id": 17,
      "payment_method_id": 182,
      "total": null
    },
    "order_shipping": {
      "shipping_fee": 120,
      "shipping_id": 30,
      "shipping_method_id": 3,
      "shipping_name": "宅配-低溫"
    },
    "payment_status": 0,
    "status": 1,
    "subtotal": 129,
    "total": 489,
    "type": 4
  },
  "sub_order": []
}
''';

main() {
  final jsonObject = jsonDecode(jsonString);
  final res = OrderRoot.fromJson(jsonObject);
  final receipt = res.asReceipt();

  group('receipt', () {
    // test('description1', () {
    //   expect(receipt.displayCategory, '取貨');
    //   expect(receipt.source.orderSource, OrderSource.Line);
    //   expect(receipt.type.orderType.name, '宅配');
    //   expect(receipt.displayType, '宅配 036');
    //   expect(
    //       receipt.type.orderType.getNameWithSource(receipt.source.orderSource),
    //       '宅配');
    // });
  });
}
