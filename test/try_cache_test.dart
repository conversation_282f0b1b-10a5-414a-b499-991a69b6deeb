import 'package:flutter_test/flutter_test.dart';

void main() {
  Iterable<num> foo2() {
    final children = <num>[];
    try {
      for (var i = 0; i < 2; i++) {
        if (i == 0) {
          num.parse('X');
        }
        children.add(i);
      }
    } catch (e) {
      // 會離開 for 迴圈
      print(e);
    }
    return children;
  }

  test('try for 2', () {
    final it = foo2();
    expect(it.length, 0);
  });

  Iterable<num> foo() {
    final children = <num>[];
    for (var i = 0; i < 2; i++) {
      try {
        if (i == 0) {
          num.parse('X');
        }
        children.add(i);
      } catch (e) {
        // throw 會跳出 foo
        // throw e;
        print(e);
      }
    }
    return children;
  }

  test('try for', () {
    final it = foo();
    expect(it.length, 1);
  });
}
