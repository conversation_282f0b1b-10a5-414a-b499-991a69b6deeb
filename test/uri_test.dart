import 'package:flutter_test/flutter_test.dart';

void main() {
  const emptyUrl = '';
  const shopUrl = 'https://omos01.diner.omos.tw/shop/product/239';
  const dinnerUrl = 'https://omos01.diner.omos.tw/product/166';
  final uri = Uri.parse(dinnerUrl);
  group('description', () {
    setUp(() {
      //
    });
    test('scheme', () => expect(uri.scheme, 'https'));
    test('https', () => expect(uri.scheme.contains(RegExp('^https?')), true));
    test('path', () => expect(uri.path, '/product/166'));
    test('origin', () => expect(uri.origin, 'https://omos01.diner.omos.tw'));
    test('host', () => expect(uri.host, 'omos01.diner.omos.tw'));
    test('authority', () => expect(uri.authority, 'omos01.diner.omos.tw'));
    test('pathSegments length', () => expect(uri.pathSegments.length, 2));
    test('pathSegments element 0', () {
      expect(uri.pathSegments.elementAt(0), 'product');
    });
    test('pathSegments element 1', () {
      expect(uri.pathSegments.elementAt(1), '166');
    });
    test('shop', () {
      expect(shopUrl.contains(RegExp('shop')), true);
    });
    test('dinner', () {
      expect(dinnerUrl.contains(RegExp('shop')), false);
    });
    test('empty', () {
      expect(Uri.parse(emptyUrl).scheme, '');
    });
  });
}
